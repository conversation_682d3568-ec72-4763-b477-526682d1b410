package com.shandai.xuan.dto;

import lombok.Data;

@Data
public class LiuYaoRequest {
    private int year;
    private int month;
    private int day;
    private int hour;
    private int minute;
    private int sex; // 性别（0:女。1:男）
    private String name; // 姓名
    private String occupy; // 占事
    private String address; // 地区
    private int dateType; // 日期类型（0:公历。1:农历）
    private int leapMonth; // 闰月（0:不使用闰月。1:使用闰月）
    private int qiGuaMode; // 起卦模式（0:日期。1:自动。2:手动）
    private int liuYao; // 上爻(六爻)→ 0:—（2正1背） 1:- -（1正2背） 2:— ○（0正3背） 3:- - ×（3正0背）
    private int wuYao;  // 五爻→ 0:—（2正1背） 1:- -（1正2背） 2:— ○（0正3背） 3:- - ×（3正0背）
    private int siYao;  // 四爻→ 0:—（2正1背） 1:- -（1正2背） 2:— ○（0正3背） 3:- - ×（3正0背）
    private int sanYao; // 三爻→ 0:—（2正1背） 1:- -（1正2背） 2:— ○（0正3背） 3:- - ×（3正0背）
    private int erYao;  // 二爻→ 0:—（2正1背） 1:- -（1正2背） 2:— ○（0正3背） 3:- - ×（3正0背）
    private int yiYao;  // 初爻(一爻)→ 0:—（2正1背） 1:- -（1正2背） 2:— ○（0正3背） 3:- - ×（3正0背）
    private int yearGanZhiSet; // 年干支设置（0:以正月初一作为新年的开始。1:以立春当天作为新年的开始。2:以立春交接的时刻作为新年的开始）
} 