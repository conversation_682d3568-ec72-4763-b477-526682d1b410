# AI API配置 - 支持多种后端
# 可选：gemini, ollama, openai
ai.backend.type=gemini

# Gemini API配置
gemini.api.key=AIzaSyAT2Xg1_6NZdWlpJ6j6dQ_qwpQcihARf7o
gemini.api.url=https://gemini.scself1700.dpdns.org/v1/chat/completions

# Ollama本地模型配置
ollama.api.url=http://localhost:11434/v1/chat/completions
ollama.model.name=qwen2.5:7b

# AI分析配置
ai.analysis.enabled=true
ai.analysis.timeout-seconds=120
ai.analysis.max-retries=2
ai.analysis.retry-interval-ms=2000
# 快速失败配置 - 解决慢速中转站问题
ai.analysis.fast-fail-timeout=15
ai.analysis.cache-enabled=true
ai.analysis.cache-expiration-minutes=60
# 模型选择（改用Gemini模型）
ai.analysis.default-model=gemini-2.5-flash
ai.analysis.temperature=0.7
# 设置极高的token限制，基本等于无限制
ai.analysis.max-tokens=32768

# 中转站特殊配置
ai.analysis.use-simple-prompts=false
ai.analysis.fallback-enabled=true

# 性能优化配置
ai.analysis.async-enabled=true
ai.analysis.connection-pool-size=20
ai.analysis.read-timeout-seconds=45
ai.analysis.connect-timeout-seconds=10
ai.analysis.connection-request-timeout-seconds=5

# 网络优化配置
ai.analysis.keep-alive-duration-seconds=30
ai.analysis.max-connections-per-route=10
ai.analysis.max-total-connections=50
ai.analysis.validate-after-inactivity-seconds=2

# 缓存配置
spring.cache.type=simple

# 模拟AI服务配置（测试用，不需要真实API密钥）
# 设置为true以使用模拟AI服务，false以使用真实的Gemini API
ai.mock.enabled=false

# 性能监控配置
performance.monitor.enabled=true
performance.monitor.console-output=true
performance.monitor.detailed-steps=true

# 性能阈值配置（毫秒）- 基于实际测试优化
performance.monitor.thresholds.liuyao-calculation=100
performance.monitor.thresholds.data-extraction=50
performance.monitor.thresholds.prompt-building=20
performance.monitor.thresholds.ai-call=35000
performance.monitor.thresholds.cache-processing=10
performance.monitor.thresholds.network-request=30000
performance.monitor.thresholds.response-parsing=100

# 用户体验优化配置
ui.progress.show-steps=true
ui.progress.estimated-time=35
ui.progress.show-percentage=true

# 模型使用监控配置
ai.analysis.usage-monitoring-enabled=true
ai.analysis.auto-model-switch-enabled=true
ai.analysis.usage-threshold=80.0
ai.analysis.monitoring-interval-seconds=30