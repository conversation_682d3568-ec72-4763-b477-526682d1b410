#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不设置token限制的效果
"""

import requests
import json

# Gemini API配置
API_KEY = "AIzaSyAT2Xg1_6NZdWlpJ6j6dQ_qwpQcihARf7o"
API_URL = "https://gemini.scself1700.dpdns.org/v1/chat/completions"
MODEL = "gemini-2.5-flash"

def test_without_max_tokens():
    """测试不设置max_tokens参数"""
    print("🔍 测试不设置max_tokens限制...")
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    # 六爻分析提示词
    prompt = """你是专业的六爻占卜师。请根据以下卦象信息进行详细分析：

## 卦象
本卦：乾为天 → 变卦：天风姤

## 六爻
上爻：世 动
三爻：应

## 时间
日辰：甲子

## 问题
占事：测试工作运势

请提供完整、专业的分析结果，包括：
1. 卦象解读
2. 用神分析  
3. 动静变化
4. 吉凶判断
5. 具体建议"""
    
    payload = {
        "model": MODEL,
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.7,
        # 不设置max_tokens参数
        "stream": False
    }
    
    try:
        print("📡 发送请求（无token限制）...")
        response = requests.post(API_URL, headers=headers, json=payload, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"📊 状态: {response.status_code}")
            print(f"🔍 响应结构:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            if "choices" in result and len(result["choices"]) > 0:
                choice = result["choices"][0]
                message = choice.get("message", {})
                content = message.get("content")
                finish_reason = choice.get("finish_reason")
                
                print(f"\n✅ 内容长度: {len(content) if content else 0} 字符")
                print(f"📝 结束原因: {finish_reason}")
                print(f"📊 Token使用: {result.get('usage', {})}")
                
                if content:
                    print(f"\n🔮 AI分析结果:\n{content}")
                    return True
                else:
                    print("❌ 内容为空")
                    return False
            
        else:
            print(f"❌ 请求失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"💥 错误: {e}")
        return False

def test_with_high_max_tokens():
    """测试设置很高的max_tokens"""
    print("\n" + "="*50)
    print("🔍 测试设置极高的max_tokens...")
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    prompt = "请详细分析乾为天卦的含义，包括卦象特点、象征意义、适用情况、历史典故等各个方面。"
    
    payload = {
        "model": MODEL,
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.7,
        "max_tokens": 32768,  # 设置极高的token限制
        "stream": False
    }
    
    try:
        print("📡 发送请求（高token限制）...")
        response = requests.post(API_URL, headers=headers, json=payload, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"📊 状态: {response.status_code}")
            
            if "choices" in result and len(result["choices"]) > 0:
                choice = result["choices"][0]
                message = choice.get("message", {})
                content = message.get("content")
                finish_reason = choice.get("finish_reason")
                
                print(f"✅ 内容长度: {len(content) if content else 0} 字符")
                print(f"📝 结束原因: {finish_reason}")
                print(f"📊 Token使用: {result.get('usage', {})}")
                
                if content:
                    print(f"\n🔮 AI分析结果:\n{content}")
                    return True
                else:
                    print("❌ 内容为空")
                    return False
            
        else:
            print(f"❌ 请求失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"💥 错误: {e}")
        return False

def main():
    print("🚀 测试无token限制的Gemini API调用")
    print("="*50)
    
    # 测试1：不设置max_tokens
    success1 = test_without_max_tokens()
    
    # 测试2：设置极高的max_tokens
    success2 = test_with_high_max_tokens()
    
    print("\n" + "="*50)
    print("📊 测试总结:")
    print(f"不设置max_tokens: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"高max_tokens设置: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 or success2:
        print("\n🎉 至少有一种方式成功！可以解决token限制问题。")
    else:
        print("\n❌ 两种方式都失败，可能需要其他解决方案。")

if __name__ == "__main__":
    main()
