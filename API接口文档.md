# 六爻AI分析系统 - API接口文档

## 概述

本文档描述了六爻AI分析系统的所有API接口，包括请求格式、响应格式、错误处理等详细信息。

## 基础信息

- **Base URL**: `http://localhost:8080`
- **Content-Type**: `application/json`
- **编码**: UTF-8

## 接口列表

### 1. 优化版流式六爻AI分析接口（推荐）

**接口地址**: `POST /api/divination/liuyao/optimized-streaming-ai`

**接口描述**: 
- 先发起AI请求，立即返回六爻基础信息
- 异步处理AI分析，支持排队机制
- 流式返回结果，用户体验优先

**请求格式**:
```json
{
    "year": 2025,
    "month": 1,
    "day": 31,
    "hour": 14,
    "minute": 30,
    "sex": 1,
    "name": "张三",
    "occupy": "考学",
    "address": "北京",
    "dateType": 0,
    "leapMonth": 0,
    "qiGuaMode": 0,
    "liuYao": 0,
    "wuYao": 1,
    "siYao": 0,
    "sanYao": 2,
    "erYao": 1,
    "chuYao": 3
}
```

**请求参数说明**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| year | int | 是 | 年份 |
| month | int | 是 | 月份 (1-12) |
| day | int | 是 | 日期 (1-31) |
| hour | int | 是 | 小时 (0-23) |
| minute | int | 是 | 分钟 (0-59) |
| sex | int | 是 | 性别 (0:女, 1:男) |
| name | string | 否 | 姓名 |
| occupy | string | 是 | 占事 (考学/财运/男测感情/女测感情/出行) |
| address | string | 否 | 地区 |
| dateType | int | 是 | 日期类型 (0:公历, 1:农历) |
| leapMonth | int | 是 | 闰月 (0:不使用, 1:使用) |
| qiGuaMode | int | 是 | 起卦模式 (0:日期, 1:自动, 2:手动) |
| liuYao | int | 否 | 上爻 (0:—, 1:--, 2:—○, 3:--×) |
| wuYao | int | 否 | 五爻 |
| siYao | int | 否 | 四爻 |
| sanYao | int | 否 | 三爻 |
| erYao | int | 否 | 二爻 |
| chuYao | int | 否 | 初爻 |

**响应格式**: Server-Sent Events (text/event-stream)

**响应数据类型**:

1. **开始信号**:
```
data: {"type":"start","message":"🚀 开始优化AI分析..."}
```

2. **进度信号**:
```
data: {"type":"progress","message":"📊 正在计算六爻基础信息..."}
```

3. **基础信息**:
```
data: {"type":"basic_info","data":{六爻完整数据对象}}
```

4. **AI开始信号**:
```
data: {"type":"ai_start","message":"🤖 AI分析已开始，请查看上方基础信息..."}
```

5. **排队信息**:
```
data: {"type":"queue","message":"⏳ 您前面还有2个用户，预计等待4分钟"}
```

6. **处理中信号**:
```
data: {"type":"processing","message":"🤖 AI正在分析中..."}
```

7. **AI内容块**:
```
data: {"type":"chunk","content":"分析内容片段"}
```

8. **完成信号**:
```
data: {"type":"complete","message":"✅ 优化分析完成"}
```

9. **错误信号**:
```
data: {"type":"error","message":"❌ 分析失败: 错误信息"}
```

**基础信息数据结构**:
```json
{
    "benGua": "乾为天",
    "bianGua": "天风姤",
    "huGua": "乾为天",
    "cuoGua": "坤为地",
    "zongGua": "乾为天",
    "benGuaGuaCi": "乾：元，亨，利，贞。",
    "bianGuaGuaCi": "姤：女壮，勿用取女。",
    "liuYaoAs": ["—", "—", "—", "—", "—", "—"],
    "liuYaoYaoXiangMarkName": ["", "", "", "", "", "○"],
    "benGuaLiuYaoShiYing": ["", "", "世", "", "", "应"],
    "benGuaLiuYaoLiuQin": ["父母", "兄弟", "官鬼", "父母", "兄弟", "官鬼"],
    "benGuaLiuYaoGanZhi": ["壬戌", "壬申", "壬午", "壬戌", "壬申", "壬午"],
    "benGuaLiuYaoWuXing": ["土", "金", "火", "土", "金", "火"],
    "liuYaoWangShuaiPercent": ["45%", "67%", "23%", "45%", "67%", "23%"],
    "duangua": "系统断卦结果..."
}
```

### 2. 原版流式六爻AI分析接口

**接口地址**: `POST /api/divination/liuyao/streaming-ai`

**接口描述**: 原版接口，已修复超时问题，但仍为串行处理

**请求格式**: 同优化版接口

**响应格式**: 同优化版接口，但没有基础信息和排队功能

### 3. 异步AI分析接口

**接口地址**: `POST /api/optimized/ai-analysis/async`

**接口描述**: 纯异步接口，立即返回RequestID

**请求格式**:
```json
{
    "liuYaoRequest": {六爻请求对象},
    "question": "具体问题",
    "analysisType": "comprehensive"
}
```

**响应格式**:
```json
{
    "success": true,
    "requestId": "req_1722334567890_1_abcd1234",
    "queuePosition": 2,
    "estimatedWaitMinutes": 4,
    "status": "QUEUED",
    "message": "您前面还有2个用户，预计等待4分钟",
    "mode": "async",
    "timestamp": 1722334567890
}
```

### 4. 获取异步分析结果

**接口地址**: `GET /api/optimized/ai-analysis/result/{requestId}`

**响应格式**:
```json
{
    "requestId": "req_1722334567890_1_abcd1234",
    "status": "USER_NOTIFIED",
    "completed": true,
    "success": true,
    "result": "AI分析结果..."
}
```

**状态说明**:
- `QUEUED`: 排队中
- `PROCESSING`: AI处理中
- `AI_COMPLETED`: AI完成
- `USER_NOTIFIED`: 用户已通知
- `COMPLETED`: 全部完成
- `FAILED`: 失败
- `TIMEOUT`: 超时

### 5. 系统状态接口

**接口地址**: `GET /api/optimized/health`

**响应格式**:
```json
{
    "activeProcessing": 3,
    "totalRequests": 8,
    "waitingQueue": 5,
    "activeTasks": 8,
    "backgroundQueueSize": 2,
    "userResponsePoolActive": 2,
    "backgroundPoolActive": 1,
    "maxConcurrentProcessing": 5,
    "maxQueueSize": 100,
    "healthy": true,
    "status": "HEALTHY",
    "queuePosition": 5,
    "estimatedWaitMinutes": 10
}
```

### 6. 排队状态接口

**接口地址**: `GET /api/optimized/queue-status`

**响应格式**:
```json
{
    "success": true,
    "currentQueuePosition": 5,
    "estimatedWaitMinutes": 10,
    "activeProcessing": 3,
    "totalRequests": 8,
    "systemStatus": "HEALTHY",
    "healthy": true,
    "timestamp": 1722334567890
}
```

## 错误处理

### HTTP状态码
- `200`: 成功
- `400`: 请求参数错误
- `500`: 服务器内部错误
- `503`: 服务不可用（系统过载）

### 错误响应格式
```json
{
    "success": false,
    "error": "错误信息",
    "timestamp": 1722334567890
}
```

## 使用建议

### 1. 推荐使用流程
1. 使用 `/api/divination/liuyao/optimized-streaming-ai` 获得最佳用户体验
2. 前端监听SSE事件，处理不同类型的消息
3. 优先显示基础信息，AI结果作为补充

### 2. 前端集成示例
```javascript
const response = await fetch('/api/divination/liuyao/optimized-streaming-ai', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream'
    },
    body: JSON.stringify(requestData)
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    
    const chunk = decoder.decode(value);
    const lines = chunk.split('\n');
    
    for (const line of lines) {
        if (line.startsWith('data: ')) {
            const data = JSON.parse(line.slice(6));
            handleStreamingData(data);
        }
    }
}
```

### 3. 性能优化建议
- 使用优化版接口获得最佳性能
- 监控系统健康状态，避免过载
- 合理设置前端超时时间（建议2分钟）
- 实现重试机制处理网络异常

## 版本信息
- **当前版本**: v2.0
- **更新日期**: 2025-01-31
- **兼容性**: 向后兼容v1.0接口
