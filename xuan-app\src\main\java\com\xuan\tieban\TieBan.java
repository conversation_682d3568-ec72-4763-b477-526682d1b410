package com.xuan.tieban;

import com.nlf.calendar.Lunar;
import com.nlf.calendar.Solar;
import com.xuan.core.liuyao.LiuYaoMap;
import com.xuan.core.liuyao.LiuYaoUtil;
import com.xuan.core.liuyao.LiuYaoSetting;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 铁板神数
 * <p>
 * 实现八卦加则计算法。
 */
public class TieBan {

    private final Solar solar;
    private final Lunar lunar;
    private final String week;

    private final String yearGan;
    private final String monthGan;
    private final String dayGan;
    private final String hourGan;

    private final String yearZhi;
    private final String monthZhi;
    private final String dayZhi;
    private final String hourZhi;

    // 年柱
    private String niangua;
    private List<String> nianzhuganzhi;
    // 月柱
    private String yuegua;
    private List<String> yuezhuganzhi;
    // 日柱
    private String rigua;
    private List<String> rizhuganzhi;
    // 时柱
    private String shigua;
    private List<String> shizhuganzhi;


    private List<String> ages;
    private List<String> comments;
    private List<Integer> fsums; // 用于存储年、月、日、时四柱的 fsum 值
    private List<String> pillarErrors; // 用于存储计算过程中的错误信息
    private List<String> shangGuaNames; // 新增：上卦名
    private List<String> xiaGuaNames;   // 新增：下卦名
    private List<Integer> sums;         // 新增：六爻地支求和

    // 新增太玄数相关结果的存储
    private List<Integer> taiXuanSums; // 用于存储太玄条目数
    private List<String> taiXuanAges; // 用于存储太玄年龄
    private List<String> taiXuanComments; // 用于存储太玄断语

    // 新增天干数断法相关结果的存储
    private List<Integer> tianGanShuEntryNumbers;
    private List<String> tianGanShuAges;
    private List<String> tianGanShuComments;

    // 为了性能，提前构建好八卦名称到纯卦卦象符号的映射
    private static final Map<String, String> PURE_GUA_SYMBOL_MAP = Map.of(
            "乾", "䷀", "坤", "䷁", "震", "䷲", "巽", "䷸",
            "坎", "䷜", "离", "䷝", "艮", "䷳", "兑", "䷹"
    );

    /**
     * 用于封装单柱计算结果的内部类
     */
    private static class PillarCalculationResult {
        String benGuaSymbol;
        List<String> liuYaoGanZhi;
        String age = "错误";
        String comment;
        int fsum = -1;
        String error;
        String shangGuaName = "错误";
        String xiaGuaName = "错误";
        int sum = -1;
    }

    /**
     * 用于封装太玄数计算结果的内部类
     */
    private static class TaiXuanPillarResult {
        int taiXuanSum = -1;
        String taiXuanAge;
        String taiXuanComment;
    }


    /**
     * 新的构造函数，直接接收所有计算好的参数
     */
    public TieBan(Solar solar, Lunar lunar, List<String> yearGanZhi, List<String> monthGanZhi, List<String> dayGanZhi, List<String> hourGanZhi) {
        this.solar = solar;
        this.lunar = lunar;
        this.week = "周" + lunar.getWeekInChinese();

        this.yearGan = yearGanZhi.get(0);
        this.yearZhi = yearGanZhi.get(1);
        this.monthGan = monthGanZhi.get(0);
        this.monthZhi = monthGanZhi.get(1);
        this.dayGan = dayGanZhi.get(0);
        this.dayZhi = dayGanZhi.get(1);
        this.hourGan = hourGanZhi.get(0);
        this.hourZhi = hourGanZhi.get(1);

        // 核心计算
        tiebanDuanGua();
    }


    /**
     * 执行四柱的铁板神数计算，并汇总结果
     */
    private void tiebanDuanGua() {
        this.ages = new ArrayList<>();
        this.comments = new ArrayList<>();
        this.fsums = new ArrayList<>();
        this.pillarErrors = new ArrayList<>();
        this.shangGuaNames = new ArrayList<>();
        this.xiaGuaNames = new ArrayList<>();
        this.sums = new ArrayList<>();
        // 初始化太玄数相关列表
        this.taiXuanSums = new ArrayList<>();
        this.taiXuanAges = new ArrayList<>();
        this.taiXuanComments = new ArrayList<>();
        // 初始化天干数断法相关列表
        this.tianGanShuEntryNumbers = new ArrayList<>();
        this.tianGanShuAges = new ArrayList<>();
        this.tianGanShuComments = new ArrayList<>();


        // 对年、月、日、时四柱分别进行计算
        PillarCalculationResult nianResult = calculatePillarResult(yearGan, yearZhi);
        this.niangua = nianResult.benGuaSymbol;
        this.nianzhuganzhi = nianResult.liuYaoGanZhi;
        addResultToLists(nianResult);

        PillarCalculationResult yueResult = calculatePillarResult(monthGan, monthZhi);
        this.yuegua = yueResult.benGuaSymbol;
        this.yuezhuganzhi = yueResult.liuYaoGanZhi;
        addResultToLists(yueResult);

        PillarCalculationResult riResult = calculatePillarResult(dayGan, dayZhi);
        this.rigua = riResult.benGuaSymbol;
        this.rizhuganzhi = riResult.liuYaoGanZhi;
        addResultToLists(riResult);

        PillarCalculationResult shiResult = calculatePillarResult(hourGan, hourZhi);
        this.shigua = shiResult.benGuaSymbol;
        this.shizhuganzhi = shiResult.liuYaoGanZhi;
        addResultToLists(shiResult);

        // 在获得各柱干支后，计算太玄数
        TaiXuanPillarResult nianTaiXuan = calculateTaiXuanPillarResult(this.nianzhuganzhi);
        this.taiXuanSums.add(nianTaiXuan.taiXuanSum);
        this.taiXuanAges.add(nianTaiXuan.taiXuanAge);
        this.taiXuanComments.add(nianTaiXuan.taiXuanComment);

        TaiXuanPillarResult yueTaiXuan = calculateTaiXuanPillarResult(this.yuezhuganzhi);
        this.taiXuanSums.add(yueTaiXuan.taiXuanSum);
        this.taiXuanAges.add(yueTaiXuan.taiXuanAge);
        this.taiXuanComments.add(yueTaiXuan.taiXuanComment);

        TaiXuanPillarResult riTaiXuan = calculateTaiXuanPillarResult(this.rizhuganzhi);
        this.taiXuanSums.add(riTaiXuan.taiXuanSum);
        this.taiXuanAges.add(riTaiXuan.taiXuanAge);
        this.taiXuanComments.add(riTaiXuan.taiXuanComment);

        TaiXuanPillarResult shiTaiXuan = calculateTaiXuanPillarResult(this.shizhuganzhi);
        this.taiXuanSums.add(shiTaiXuan.taiXuanSum);
        this.taiXuanAges.add(shiTaiXuan.taiXuanAge);
        this.taiXuanComments.add(shiTaiXuan.taiXuanComment);

        // 新增：计算天干数断法结果
        calculateTianGanShuResult();
    }

    /**
     * 将单柱的计算结果添加到全局列表中
     * @param result 计算结果
     */
    private void addResultToLists(PillarCalculationResult result) {
        this.fsums.add(result.fsum);
        this.ages.add(result.age);
        this.comments.add(result.comment);
        this.pillarErrors.add(result.error);
        this.shangGuaNames.add(result.shangGuaName);
        this.xiaGuaNames.add(result.xiaGuaName);
        this.sums.add(result.sum);
    }


    /**
     * 八卦加则计算法：对单个干支柱进行计算，并返回结果
     *
     * @param gan 天干
     * @param zhi 地支
     * @return PillarCalculationResult 包含该柱所有计算结果的对象
     */
    private PillarCalculationResult calculatePillarResult(String gan, String zhi) {
        PillarCalculationResult result = new PillarCalculationResult();
        try {
            // 1. 天干、地支 -> 八卦名
            String ganGuaName = TieBanMap.TIAN_GAN_GUA_MAP.get(gan.charAt(0));
            String zhiGuaName = TieBanMap.DI_ZHI_GUA_MAP.get(zhi.charAt(0));

            // 2. 八卦名 -> 三爻列表
            List<String> ganYaos = getTrigramYaos(ganGuaName);
            List<String> zhiYaos = getTrigramYaos(zhiGuaName);

            // 3. 拼接六爻 (地支为下卦, 天干为上卦)
            List<String> sixYaos = Stream.concat(zhiYaos.stream(), ganYaos.stream()).collect(Collectors.toList());

            // 4. 六爻 -> 卦信息
            List<String> guaInfo = LiuYaoMap.NAME_AND_AS.get(sixYaos);
            if (guaInfo == null) {
                result.error = "无法从六爻 " + sixYaos + " 找到卦信息";
                result.comment = result.error;
                return result;
            }

            result.shangGuaName = guaInfo.get(0);
            result.xiaGuaName = guaInfo.get(2);
            result.benGuaSymbol = guaInfo.get(5);

            // 5. 卦象 -> 六爻干支
            result.liuYaoGanZhi = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_GAN_ZHI.get(result.benGuaSymbol);
            if (result.liuYaoGanZhi == null) {
                throw new IllegalStateException("根据卦象符号 '" + result.benGuaSymbol + "' 未能找到六爻干支信息。");
            }
            List<Character> liuYaoZhi = result.liuYaoGanZhi.stream()
                    .map(gz -> gz.charAt(1))
                    .collect(Collectors.toList());

            // 6. 六爻地支 -> 数字求和
            int sum = 0;
            for (Character yaoZhi : liuYaoZhi) {
                sum += TieBanMap.LIU_YAO_MAP.get(yaoZhi);
            }
            result.sum = sum;

            // 7. 上下卦名 -> 数字
            int shangGuaNum = TieBanMap.BA_GUA_SHU_MAP.get(result.shangGuaName.charAt(0));
            int xiaGuaNum = TieBanMap.BA_GUA_SHU_MAP.get(result.xiaGuaName.charAt(0));

            // 8. 计算最终条目数
            result.fsum = (shangGuaNum * 1000) + sum - xiaGuaNum;

            // 9. 查询条文
            List<String> tiebanResult = TieBanMap.getTieBans(result.fsum);
            if (tiebanResult != null && !tiebanResult.isEmpty()) {
                if (tiebanResult.size() >= 2) {
                    result.age = tiebanResult.get(0); // 年龄是第一个元素
                    result.comment = tiebanResult.stream().skip(1).collect(Collectors.joining(", ")); // 其余是批语
                } else { // 只有一个元素，当作批语
                    result.age = "-";
                    result.comment = tiebanResult.get(0);
                }
            } else {
                result.error = "未找到条目: " + result.fsum;
                result.comment = result.error;
            }

        } catch (Exception e) {
            // 捕获任何潜在的错误，例如Map中找不到key
            result.error = "计算失败: " + e.getMessage();
            result.comment = result.error;
        }
        return result;
    }


    /**
     * 新增：天干数断法
     */
    private void calculateTianGanShuResult() {
        try {
            // 1. 获取天干并查表取数
            Map<Character, Integer> peiShuMap = TieBanMap.TIAN_GAN_PEI_SHU_MAP;
            Integer nianGanShu = peiShuMap.get(this.yearGan.charAt(0));
            Integer yueGanShu = peiShuMap.get(this.monthGan.charAt(0));
            Integer riGanShu = peiShuMap.get(this.dayGan.charAt(0));
            Integer shiGanShu = peiShuMap.get(this.hourGan.charAt(0));

            if (nianGanShu == null || yueGanShu == null || riGanShu == null || shiGanShu == null) {
                throw new IllegalStateException("天干配数表中缺少对应天干的数值");
            }

            // 2. 计算天干基础数
            int tianganbase = yueGanShu * 1000 + riGanShu * 100 + shiGanShu * 10 + nianGanShu;

            // 如果天干基础数小于1001，则加1000
            if (tianganbase < 1001) {
                tianganbase += 1000;
            }

            // 3. 循环计算4个条目数并查询结果
            for (int i = 1; i <= 4; i++) {
                int entryNumber = tianganbase + 96 * i;
                this.tianGanShuEntryNumbers.add(entryNumber);

                List<String> tiebanResult = TieBanMap.getTieBans(entryNumber);
                if (tiebanResult != null && !tiebanResult.isEmpty()) {
                    if (tiebanResult.size() >= 2) {
                        this.tianGanShuAges.add(tiebanResult.get(0)); // 年龄
                        this.tianGanShuComments.add(tiebanResult.stream().skip(1).collect(Collectors.joining(", "))); // 批语
                    } else {
                        this.tianGanShuAges.add("-");
                        this.tianGanShuComments.add(tiebanResult.get(0));
                    }
                } else {
                    this.tianGanShuAges.add("N/A");
                    this.tianGanShuComments.add("未找到条目: " + entryNumber);
                }
            }
        } catch (Exception e) {
            // 清空可能已部分填充的列表，以保证数据一致性
            this.tianGanShuEntryNumbers.clear();
            this.tianGanShuAges.clear();
            this.tianGanShuComments.clear();
            // 填充错误信息
            for (int i = 0; i < 4; i++) {
                this.tianGanShuEntryNumbers.add(-1);
                this.tianGanShuAges.add("错误");
                this.tianGanShuComments.add("计算天干数断法时出错: " + e.getMessage());
            }
        }
    }


    /**
     * 根据单柱的六爻干支计算太玄数结果
     *
     * @param liuYaoGanZhi 六爻的干支列表
     * @return TaiXuanPillarResult 包含太玄条目数和断语的对象
     */
    private TaiXuanPillarResult calculateTaiXuanPillarResult(List<String> liuYaoGanZhi) {
        TaiXuanPillarResult result = new TaiXuanPillarResult();
        if (liuYaoGanZhi == null || liuYaoGanZhi.size() != 6) {
            result.taiXuanComment = "干支数据不足，无法计算太玄数";
            return result;
        }

        try {
            int[] taixuanSums = new int[6];
            for (int i = 0; i < 6; i++) {
                String ganZhi = liuYaoGanZhi.get(i);
                char gan = ganZhi.charAt(0);
                char zhi = ganZhi.charAt(1);

                int ganShu = TieBanMap.TAI_XUAN_SHU_MAP.get(gan);
                int zhiShu = TieBanMap.TAI_XUAN_SHU_MAP.get(zhi);
                taixuanSums[i] = ganShu + zhiShu;
            }

            int shangSanHe = taixuanSums[3] + taixuanSums[4] + taixuanSums[5];
            int xiaSanHe = taixuanSums[0] + taixuanSums[1] + taixuanSums[2];

            result.taiXuanSum = shangSanHe * 100 + xiaSanHe;

            List<String> tiebanResult = TieBanMap.getTieBans(result.taiXuanSum);
            if (tiebanResult != null && !tiebanResult.isEmpty()) {
                if (tiebanResult.size() >= 2) {
                    result.taiXuanAge = tiebanResult.get(0); // 年龄是第一个元素
                    result.taiXuanComment = tiebanResult.stream().skip(1).collect(Collectors.joining(", ")); // 其余是批语
                } else { // 只有一个元素，当作批语
                    result.taiXuanAge = "-";
                    result.taiXuanComment = tiebanResult.get(0);
                }
            } else {
                result.taiXuanComment = "未找到对应太玄数条文: " + result.taiXuanSum;
            }
        } catch (Exception e) {
            result.taiXuanComment = "计算太玄数时出错: " + e.getMessage();
        }

        return result;
    }


    /**
     * 统一处理计算过程中的错误
     * @param errorMessage 错误信息
     */
    private void handlePillarError(String errorMessage) {
        this.fsums.add(-1); // 使用-1等特殊值表示fsum计算失败
        this.ages.add("错误");
        this.comments.add(errorMessage);
        this.pillarErrors.add(errorMessage);
        this.shangGuaNames.add("错误");
        this.xiaGuaNames.add("错误");
        this.sums.add(-1);
    }

    /**
     * 根据八卦名称（如 "乾"）获取其对应的三爻列表（如 ["—", "—", "—"]）
     *
     * @param guaName 八卦名称
     * @return 三爻列表
     */
    private List<String> getTrigramYaos(String guaName) {
        // 从纯卦（如"乾为天"）的六爻中提取下卦（前三爻）作为该单卦的爻象
        String pureGuaSymbol = PURE_GUA_SYMBOL_MAP.get(guaName);
        if (pureGuaSymbol == null) {
            throw new IllegalArgumentException("无法找到八卦 '" + guaName + "' 对应的卦象符号。");
        }
        List<String> sixYaos = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_AS.get(pureGuaSymbol);
        if (sixYaos == null) {
            throw new IllegalStateException("根据卦象符号 '" + pureGuaSymbol + "' 未能找到六爻信息。");
        }
        return sixYaos.subList(0, 3);
    }

    // --- Getters ---

    public Solar getSolar() {
        return solar;
    }

    public Lunar getLunar() {
        return lunar;
    }

    public String getWeek() {
        return week;
    }

    public String getYearGan() {
        return yearGan;
    }

    public String getMonthGan() {
        return monthGan;
    }

    public String getDayGan() {
        return dayGan;
    }

    public String getHourGan() {
        return hourGan;
    }

    public String getYearZhi() {
        return yearZhi;
    }

    public String getMonthZhi() {
        return monthZhi;
    }

    public String getDayZhi() {
        return dayZhi;
    }

    public String getHourZhi() {
        return hourZhi;
    }

    /**
     * 获取四柱对应的 fsum 值列表
     * @return fsum 值列表
     */
    public List<Integer> getFsums() {
        return fsums;
    }

    /**
     * 获取计算过程中的错误信息列表
     * @return 错误信息列表，如果没有错误则对应位置为 null
     */
    public List<String> getPillarErrors() {
        return pillarErrors;
    }

    public List<String> getShangGuaNames() {
        return shangGuaNames;
    }

    public List<String> getXiaGuaNames() {
        return xiaGuaNames;
    }

    public List<Integer> getSums() {
        return sums;
    }

    public List<String> getAges() {
        return ages;
    }

    public List<String> getComments() {
        return comments;
    }

    public List<Integer> getTaiXuanSums() {
        return taiXuanSums;
    }

    public List<String> getTaiXuanAges() {
        return taiXuanAges;
    }

    public List<String> getTaiXuanComments() {
        return taiXuanComments;
    }

    public List<Integer> getTianGanShuEntryNumbers() {
        return tianGanShuEntryNumbers;
    }

    public List<String> getTianGanShuAges() {
        return tianGanShuAges;
    }

    public List<String> getTianGanShuComments() {
        return tianGanShuComments;
    }

    public String getNiangua() {
        return niangua;
    }

    public List<String> getNianzhuganzhi() {
        return nianzhuganzhi;
    }

    public String getYuegua() {
        return yuegua;
    }

    public List<String> getYuezhuganzhi() {
        return yuezhuganzhi;
    }

    public String getRigua() {
        return rigua;
    }

    public List<String> getRizhuganzhi() {
        return rizhuganzhi;
    }

    public String getShigua() {
        return shigua;
    }

    public List<String> getShizhuganzhi() {
        return shizhuganzhi;
    }
} 