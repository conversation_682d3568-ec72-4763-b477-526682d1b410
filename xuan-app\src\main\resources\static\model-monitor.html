<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI模型使用监控</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .status-card {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .model-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .model-card {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            background: white;
        }
        .model-card.active {
            border-color: #007bff;
            background: #f8f9fa;
        }
        .model-name {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .usage-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 5px 0;
            overflow: hidden;
        }
        .usage-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        .usage-fill.low { background: #28a745; }
        .usage-fill.medium { background: #ffc107; }
        .usage-fill.high { background: #dc3545; }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.secondary {
            background: #6c757d;
        }
        .btn.secondary:hover {
            background: #545b62;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .switch-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
        }
        .model-select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI模型使用监控系统</h1>
            <p>实时监控Gemini模型使用情况和自动切换状态</p>
        </div>

        <div class="status-card">
            <h3>当前状态</h3>
            <div id="currentStatus">加载中...</div>
        </div>

        <div class="controls">
            <button class="btn" onclick="refreshData()">🔄 刷新数据</button>
            <button class="btn secondary" onclick="resetStats()">🔄 重置统计</button>
            <button class="btn secondary" onclick="toggleAutoRefresh()">⏱️ 自动刷新</button>
        </div>

        <div class="switch-section">
            <h3>手动模型切换</h3>
            <select id="modelSelect" class="model-select">
                <option value="">选择模型...</option>
            </select>
            <button class="btn" onclick="switchModel()">切换模型</button>
            <button class="btn secondary" onclick="getNextRecommended()">获取推荐模型</button>
        </div>

        <div class="model-grid" id="modelGrid">
            <!-- 模型卡片将在这里动态生成 -->
        </div>

        <div>
            <h3>操作日志</h3>
            <div class="log" id="logArea"></div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let isAutoRefresh = false;

        // 页面加载时初始化
        window.onload = function() {
            refreshData();
            loadAvailableModels();
        };

        // 刷新数据
        async function refreshData() {
            try {
                const response = await fetch('/api/model/usage/all');
                const data = await response.json();
                updateModelGrid(data);
                updateCurrentStatus();
                log('数据已刷新');
            } catch (error) {
                log('刷新数据失败: ' + error.message);
            }
        }

        // 更新当前状态
        async function updateCurrentStatus() {
            try {
                const response = await fetch('/api/model/status');
                const data = await response.json();
                
                const statusDiv = document.getElementById('currentStatus');
                statusDiv.innerHTML = `
                    <strong>当前活跃模型:</strong> ${data.currentModel}<br>
                    <strong>切换状态:</strong> ${data.switchingInProgress ? '正在切换' : '正常'}<br>
                    ${data.usage ? `
                        <strong>RPM使用:</strong> ${data.usage.rpmUsed}/${data.usage.rpmLimit} (${data.usage.rpmUsagePercentage.toFixed(1)}%)<br>
                        <strong>每日使用:</strong> ${data.usage.dailyUsed}/${data.usage.dailyLimit} (${data.usage.dailyUsagePercentage.toFixed(1)}%)<br>
                        <strong>可发送请求:</strong> ${data.usage.canSendRequest ? '是' : '否'}
                    ` : ''}
                `;
            } catch (error) {
                log('更新状态失败: ' + error.message);
            }
        }

        // 更新模型网格
        function updateModelGrid(data) {
            const grid = document.getElementById('modelGrid');
            grid.innerHTML = '';

            for (const [modelName, stats] of Object.entries(data.usageStats)) {
                const card = document.createElement('div');
                card.className = `model-card ${modelName === data.currentActiveModel ? 'active' : ''}`;
                
                const rpmPercentage = stats.rpmUsagePercentage;
                const dailyPercentage = stats.dailyUsagePercentage;
                
                card.innerHTML = `
                    <div class="model-name">${modelName} ${modelName === data.currentActiveModel ? '(当前)' : ''}</div>
                    <div>RPM: ${stats.rpmUsed}/${stats.rpmLimit} (${rpmPercentage.toFixed(1)}%)</div>
                    <div class="usage-bar">
                        <div class="usage-fill ${getUsageLevel(rpmPercentage)}" style="width: ${Math.min(rpmPercentage, 100)}%"></div>
                    </div>
                    <div>每日: ${stats.dailyUsed}/${stats.dailyLimit} (${dailyPercentage.toFixed(1)}%)</div>
                    <div class="usage-bar">
                        <div class="usage-fill ${getUsageLevel(dailyPercentage)}" style="width: ${Math.min(dailyPercentage, 100)}%"></div>
                    </div>
                    <div>状态: ${stats.canSendRequest ? '✅ 可用' : '❌ 已满'}</div>
                `;
                
                grid.appendChild(card);
            }
        }

        // 获取使用级别
        function getUsageLevel(percentage) {
            if (percentage < 60) return 'low';
            if (percentage < 80) return 'medium';
            return 'high';
        }

        // 加载可用模型
        async function loadAvailableModels() {
            try {
                const response = await fetch('/api/model/available');
                const data = await response.json();
                
                const select = document.getElementById('modelSelect');
                select.innerHTML = '<option value="">选择模型...</option>';
                
                data.primaryModels.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.modelName;
                    option.textContent = `${model.modelName} (RPM:${model.rpmLimit}, 每日:${model.dailyLimit})`;
                    select.appendChild(option);
                });
            } catch (error) {
                log('加载模型列表失败: ' + error.message);
            }
        }

        // 切换模型
        async function switchModel() {
            const select = document.getElementById('modelSelect');
            const modelName = select.value;
            
            if (!modelName) {
                alert('请选择一个模型');
                return;
            }

            try {
                const response = await fetch(`/api/model/switch/${modelName}`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    log(`成功切换到模型: ${modelName}`);
                    refreshData();
                } else {
                    log(`切换失败: ${data.message}`);
                }
            } catch (error) {
                log('切换模型失败: ' + error.message);
            }
        }

        // 获取推荐模型
        async function getNextRecommended() {
            try {
                const response = await fetch('/api/model/next-recommended');
                const data = await response.json();
                
                if (data.nextRecommendedModel) {
                    log(`推荐切换到: ${data.nextRecommendedModel}`);
                    document.getElementById('modelSelect').value = data.nextRecommendedModel;
                } else {
                    log('当前没有推荐的备用模型');
                }
            } catch (error) {
                log('获取推荐模型失败: ' + error.message);
            }
        }

        // 重置统计
        async function resetStats() {
            if (!confirm('确定要重置所有模型的使用统计吗？')) {
                return;
            }

            try {
                const response = await fetch('/api/model/reset-stats', {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    log('使用统计已重置');
                    refreshData();
                } else {
                    log('重置失败');
                }
            } catch (error) {
                log('重置统计失败: ' + error.message);
            }
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            if (isAutoRefresh) {
                clearInterval(autoRefreshInterval);
                isAutoRefresh = false;
                log('自动刷新已关闭');
            } else {
                autoRefreshInterval = setInterval(refreshData, 10000); // 每10秒刷新
                isAutoRefresh = true;
                log('自动刷新已开启 (每10秒)');
            }
        }

        // 记录日志
        function log(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
    </script>
</body>
</html>
