package com.xuan.core.bazi;

import java.util.*;

/**
 * 八字-常量
 *
 * <AUTHOR>
 */
public class BaZiMap {

    /**
     * 十二节
     */
    public static final String[] SHI_ER_JIE = {"立春", "惊蛰", "清明", "立春", "芒种", "小暑", "立秋", "白露", "寒露", "立冬", "大雪", "小寒"};

    /**
     * 天干五行（天干为键）
     */
    public static final Map<String, String> GAN_WU_XING = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("甲", "木");
            put("乙", "木");
            put("丙", "火");
            put("丁", "火");
            put("戊", "土");
            put("己", "土");
            put("庚", "金");
            put("辛", "金");
            put("壬", "水");
            put("癸", "水");
        }
    };

    /**
     * 地支五行（地支为键）
     */
    public static final Map<String, String> ZHI_WU_XING = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("子", "水");
            put("丑", "土");
            put("寅", "木");
            put("卯", "木");
            put("辰", "土");
            put("巳", "火");
            put("午", "火");
            put("未", "土");
            put("申", "金");
            put("酉", "金");
            put("戌", "土");
            put("亥", "水");
        }
    };

    /**
     * 天干阴阳（天干为键）
     */
    public static final Map<String, String> GAN_YIN_YANG = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("甲", "阳");
            put("乙", "阴");
            put("丙", "阳");
            put("丁", "阴");
            put("戊", "阳");
            put("己", "阴");
            put("庚", "阳");
            put("辛", "阴");
            put("壬", "阳");
            put("癸", "阴");
        }
    };

    /**
     * 地支阴阳（地支为键）
     */
    public static final Map<String, String> ZHI_YIN_YANG = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("子", "阳");
            put("丑", "阴");
            put("寅", "阳");
            put("卯", "阴");
            put("辰", "阳");
            put("巳", "阴");
            put("午", "阳");
            put("未", "阴");
            put("申", "阳");
            put("酉", "阴");
            put("戌", "阳");
            put("亥", "阴");
        }
    };

    /**
     * 《子平真诠》十二月令人元司令分野
     */
    public static final Map<String, List<String>> ZI_REN_YUAN = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /*
           农历月                值令日                          节气
              ↓                     ↓                              ↓

            寅月→     立春后戊七日，丙七日，甲十六日           立春、雨水
            卯月→     惊蛰后甲十日，乙二十日                   惊蛰、春分
            辰月→     清明后乙九日，癸三日，戊十八日           清明、谷雨
            巳月→     立夏后戊五日，庚九日，丙十六日           立夏、小满
            午月→     芒种后丙十日，己九日，丁十一日           芒种、夏至
            未月→     小暑后丁九日，乙三日，己十八日           小暑、大署
            申月→     立秋后戊己十日，壬三日，庚十七日         立秋、处暑
            酉月→     白露后庚十日，辛二十日                   白露、秋分
            戌月→     寒露后辛九日，丁三日，戊十八日           寒露、霜降
            亥月→     立冬后戊七日，甲五日，壬十八日           立冬、小雪
            子月→     大雪后壬十日，癸二十日                   大雪、冬至
            丑月→     小寒后癸九日，辛三日，己十八日           小寒、大寒

            -----------------------------------------------------------------------------

            例：甲木（年干）生于寅月，立春后的前七日为戊土值令，从第八日到第十四日为丙火值令，从第十五日到第三十日为甲木值令。
         */

        {
            put("寅", Arrays.asList("戊", "戊", "戊", "戊", "戊", "戊", "戊", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲"));
            put("卯", Arrays.asList("甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙"));
            put("辰", Arrays.asList("乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "癸", "癸", "癸", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊"));
            put("巳", Arrays.asList("戊", "戊", "戊", "戊", "戊", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙"));
            put("午", Arrays.asList("丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "己", "己", "己", "己", "己", "己", "己", "己", "己", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁"));
            put("未", Arrays.asList("丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "乙", "乙", "乙", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己"));
            put("申", Arrays.asList("己", "己", "己", "己", "己", "己", "己", "戊", "戊", "戊", "壬", "壬", "壬", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚"));
            put("酉", Arrays.asList("庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛"));
            put("戌", Arrays.asList("辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "丁", "丁", "丁", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊"));
            put("亥", Arrays.asList("戊", "戊", "戊", "戊", "戊", "戊", "戊", "甲", "甲", "甲", "甲", "甲", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬"));
            put("子", Arrays.asList("壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸"));
            put("丑", Arrays.asList("癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "辛", "辛", "辛", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己"));
        }
    };

    /**
     * 《渊海子平》十二月令人元司令分野
     */
    public static final Map<String, List<String>> YUAN_REN_YUAN = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /*
           农历月              值令日
              ↓                   ↓

            寅月→     戊七日，丙七日，甲十六日
            卯月→     甲十日，乙二十日
            辰月→     乙九日，癸三日，戊十八日
            巳月→     戊五日，庚九日，丙十六日
            午月→     丙十日，己十日，丁十日
            未月→     丁九日，乙三日，己十八日
            申月→     己七日，戊三日，壬三日，庚十七日
            酉月→     庚十日，辛二十日
            戌月→     辛九日，丁三日，戊十八日
            亥月→     戊七日，甲五日，壬十八日
            子月→     壬十日，癸二十日
            丑月→     癸九日，辛三日，己十八日

            -----------------------------------------------------------------------------

            例：甲木（年干）生于寅月，立春后的前七日为戊土值令，从第八日到第十四日为丙火值令，从第十五日到第三十日为甲木值令。
         */

        {
            put("寅", Arrays.asList("戊", "戊", "戊", "戊", "戊", "戊", "戊", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲"));
            put("卯", Arrays.asList("甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙"));
            put("辰", Arrays.asList("乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "癸", "癸", "癸", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊"));
            put("巳", Arrays.asList("戊", "戊", "戊", "戊", "戊", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙"));
            put("午", Arrays.asList("丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁"));
            put("未", Arrays.asList("丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "乙", "乙", "乙", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己"));
            put("申", Arrays.asList("己", "己", "己", "己", "己", "己", "己", "戊", "戊", "戊", "壬", "壬", "壬", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚"));
            put("酉", Arrays.asList("庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛"));
            put("戌", Arrays.asList("辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "丁", "丁", "丁", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊"));
            put("亥", Arrays.asList("戊", "戊", "戊", "戊", "戊", "戊", "戊", "甲", "甲", "甲", "甲", "甲", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬"));
            put("子", Arrays.asList("壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸"));
            put("丑", Arrays.asList("癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "辛", "辛", "辛", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己"));
        }
    };

    /**
     * 《星平会海》十二月令人元司令分野
     */
    public static final Map<String, List<String>> XING_REN_YUAN = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /*
           农历月              值令日
              ↓                   ↓

            寅月→     戊七日，丙七日，甲十六日
            卯月→     甲十日，乙二十日
            辰月→     乙九日，癸三日，戊十八日
            巳月→     戊五日，庚九日，丙十六日
            午月→     丙十日，己九日，丁十一日
            未月→     丁九日，乙二日，己十八日
            申月→     己七日，戊三日，壬三日，庚十七日
            酉月→     庚十日，辛二十日
            戌月→     辛九日，丁三日，戊十八日
            亥月→     戊七日，甲五日，壬十八日
            子月→     壬十日，癸二十日
            丑月→     癸九日，辛三日，己十八日

            -----------------------------------------------------------------------------

            例：甲木（年干）生于寅月，立春后的前七日为戊土值令，从第八日到第十四日为丙火值令，从第十五日到第三十日为甲木值令。
         */

        {
            put("寅", Arrays.asList("戊", "戊", "戊", "戊", "戊", "戊", "戊", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲"));
            put("卯", Arrays.asList("甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙"));
            put("辰", Arrays.asList("乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "癸", "癸", "癸", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊"));
            put("巳", Arrays.asList("戊", "戊", "戊", "戊", "戊", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙"));
            put("午", Arrays.asList("丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "己", "己", "己", "己", "己", "己", "己", "己", "己", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁"));
            put("未", Arrays.asList("丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "乙", "乙", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己"));
            put("申", Arrays.asList("己", "己", "己", "己", "己", "己", "己", "戊", "戊", "戊", "壬", "壬", "壬", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚"));
            put("酉", Arrays.asList("庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛"));
            put("戌", Arrays.asList("辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "丁", "丁", "丁", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊"));
            put("亥", Arrays.asList("戊", "戊", "戊", "戊", "戊", "戊", "戊", "甲", "甲", "甲", "甲", "甲", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬"));
            put("子", Arrays.asList("壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸"));
            put("丑", Arrays.asList("癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "辛", "辛", "辛", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己"));
        }
    };

    /**
     * 《三命通会》十二月令人元司令分野
     */
    public static final Map<String, List<String>> SAN_REN_YUAN = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /*
           农历月              值令日
              ↓                   ↓

            寅月→     己七日，丙五日，甲十八日
            卯月→     甲九日，癸三日，乙十八日
            辰月→     乙九日，癸三日，戊十八日
            巳月→     戊七日，庚五日，丙十八日
            午月→     丙九日，乙三日，丁十八日
            未月→     丁七日，乙五日，己十八日
            申月→     己七日，戊三日，壬三日，庚十七日
            酉月→     庚七日，丁三日，辛二十日
            戌月→     辛七日，丁五日，戊十八日
            亥月→     戊七日，甲五日，壬十八日
            子月→     壬九日，辛三日，癸十八日
            丑月→     癸七日，辛五日，己十八日

            -----------------------------------------------------------------------------

            例：甲木（年干）生于寅月，立春后的前七日为己土值令，从第八日到第十二日为丙火值令，从第十三日到第三十日为甲木值令。
         */

        {
            put("寅", Arrays.asList("己", "己", "己", "己", "己", "己", "己", "丙", "丙", "丙", "丙", "丙", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲"));
            put("卯", Arrays.asList("甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "癸", "癸", "癸", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙"));
            put("辰", Arrays.asList("乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "癸", "癸", "癸", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊"));
            put("巳", Arrays.asList("戊", "戊", "戊", "戊", "戊", "戊", "戊", "庚", "庚", "庚", "庚", "庚", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙"));
            put("午", Arrays.asList("丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "乙", "乙", "乙", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁"));
            put("未", Arrays.asList("丁", "丁", "丁", "丁", "丁", "丁", "丁", "乙", "乙", "乙", "乙", "乙", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己"));
            put("申", Arrays.asList("己", "己", "己", "己", "己", "己", "己", "戊", "戊", "戊", "壬", "壬", "壬", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚"));
            put("酉", Arrays.asList("庚", "庚", "庚", "庚", "庚", "庚", "庚", "丁", "丁", "丁", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛"));
            put("戌", Arrays.asList("辛", "辛", "辛", "辛", "辛", "辛", "辛", "丁", "丁", "丁", "丁", "丁", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊"));
            put("亥", Arrays.asList("戊", "戊", "戊", "戊", "戊", "戊", "戊", "甲", "甲", "甲", "甲", "甲", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬"));
            put("子", Arrays.asList("壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "辛", "辛", "辛", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸"));
            put("丑", Arrays.asList("癸", "癸", "癸", "癸", "癸", "癸", "癸", "辛", "辛", "辛", "辛", "辛", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己"));
        }
    };

    /**
     * 《神峰通考》十二月令人元司令分野
     */
    public static final Map<String, List<String>> SHEN_REN_YUAN = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /*
           农历月              值令日
              ↓                   ↓

            寅月→     戊七日，丙七日，甲十六日
            卯月→     甲十日，乙二十日
            辰月→     乙九日，癸三日，戊十八日
            巳月→     戊七日，庚七日，丙十六日
            午月→     丙十日，己七日，丁十三日
            未月→     丁九日，乙三日，己十八日
            申月→     戊七日，壬七日，庚十六日
            酉月→     庚十日，辛二十日
            戌月→     辛九日，丁三日，戊十八日
            亥月→     戊七日，甲七日，壬十六日
            子月→     壬十日，癸二十二日
            丑月→     癸九日，辛三日，己十八日

            -----------------------------------------------------------------------------

            例：甲木（年干）生于寅月，立春后的前七日为戊土值令，从第八日到第十四日为丙火值令，从第十五日到第三十日为甲木值令。
         */

        {
            put("寅", Arrays.asList("戊", "戊", "戊", "戊", "戊", "戊", "戊", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲"));
            put("卯", Arrays.asList("甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙"));
            put("辰", Arrays.asList("乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "癸", "癸", "癸", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊"));
            put("巳", Arrays.asList("戊", "戊", "戊", "戊", "戊", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙"));
            put("午", Arrays.asList("丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "己", "己", "己", "己", "己", "己", "己", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁"));
            put("未", Arrays.asList("丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "乙", "乙", "乙", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己"));
            put("申", Arrays.asList("戊", "戊", "戊", "戊", "戊", "戊", "戊", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚"));
            put("酉", Arrays.asList("庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛"));
            put("戌", Arrays.asList("辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "丁", "丁", "丁", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊"));
            put("亥", Arrays.asList("戊", "戊", "戊", "戊", "戊", "戊", "戊", "甲", "甲", "甲", "甲", "甲", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬"));
            put("子", Arrays.asList("壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸"));
            put("丑", Arrays.asList("癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "辛", "辛", "辛", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己"));
        }
    };

    /**
     * 【万育吾之法决】十二月令人元司令分野
     */
    public static final Map<String, List<String>> WAN_REN_YUAN = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /*
           农历月              值令日
              ↓                   ↓

            寅月→     己五日，丙五日，甲二十日
            卯月→     甲七日，乙二十三日
            辰月→     乙七日，壬五日，戊十八日
            巳月→     戊七日，庚五日，丙十八日
            午月→     丙七日，丁二十三日
            未月→     丁七日，甲五日，己十八日
            申月→     己五日，壬五日，庚二十日
            酉月→     庚七日，辛二十三日
            戌月→     辛七日，丙五日，戊十八日
            亥月→     戊五日，甲五日，壬二十日
            子月→     壬七日，癸二十三日
            丑月→     癸七日，庚五日，己十八日

            -----------------------------------------------------------------------------

            例：甲木（年干）生于寅月，立春后的前七日为己土值令，从第八日到第十四日为丙火值令，从第十五日到第三十日为甲木值令。
         */

        {
            put("寅", Arrays.asList("己", "己", "己", "己", "己", "丙", "丙", "丙", "丙", "丙", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲", "甲"));
            put("卯", Arrays.asList("甲", "甲", "甲", "甲", "甲", "甲", "甲", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙", "乙"));
            put("辰", Arrays.asList("乙", "乙", "乙", "乙", "乙", "乙", "乙", "壬", "壬", "壬", "壬", "壬", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊"));
            put("巳", Arrays.asList("戊", "戊", "戊", "戊", "戊", "戊", "戊", "庚", "庚", "庚", "庚", "庚", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙", "丙"));
            put("午", Arrays.asList("丙", "丙", "丙", "丙", "丙", "丙", "丙", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁", "丁"));
            put("未", Arrays.asList("丁", "丁", "丁", "丁", "丁", "丁", "丁", "甲", "甲", "甲", "甲", "甲", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己"));
            put("申", Arrays.asList("己", "己", "己", "己", "己", "壬", "壬", "壬", "壬", "壬", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚", "庚"));
            put("酉", Arrays.asList("庚", "庚", "庚", "庚", "庚", "庚", "庚", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛", "辛"));
            put("戌", Arrays.asList("辛", "辛", "辛", "辛", "辛", "辛", "辛", "丙", "丙", "丙", "丙", "丙", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊", "戊"));
            put("亥", Arrays.asList("戊", "戊", "戊", "戊", "戊", "甲", "甲", "甲", "甲", "甲", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬", "壬"));
            put("子", Arrays.asList("壬", "壬", "壬", "壬", "壬", "壬", "壬", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸", "癸"));
            put("丑", Arrays.asList("癸", "癸", "癸", "癸", "癸", "癸", "癸", "庚", "庚", "庚", "庚", "庚", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己", "己"));
        }
    };

    /**
     * 天干相生（天干+天干为键）
     */
    public static final Map<String, String> TIAN_GAN_XIANG_SHENG = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("甲丙", "甲丙相生");
            put("甲丁", "甲丁相生");
            put("乙丙", "乙丙相生");
            put("乙丁", "乙丁相生");
            put("丙戊", "丙戊相生");
            put("丙己", "丙己相生");
            put("丁戊", "丁戊相生");
            put("丁己", "丁己相生");
            put("戊庚", "戊庚相生");
            put("戊辛", "戊辛相生");
            put("己庚", "己庚相生");
            put("己辛", "己辛相生");
            put("庚壬", "庚壬相生");
            put("庚癸", "庚癸相生");
            put("辛壬", "辛壬相生");
            put("辛癸", "辛癸相生");
            put("壬甲", "壬甲相生");
            put("壬乙", "壬乙相生");
            put("癸甲", "癸甲相生");
            put("癸乙", "癸乙相生");
        }
    };

    /**
     * 天干相合（天干+天干为键）
     */
    public static final Map<String, String> TIAN_GAN_XIANG_HE = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("甲己", "甲己合化土");
            put("乙庚", "乙庚合化金");
            put("丙辛", "丙辛合化水");
            put("丁壬", "丁壬合化木");
            put("戊癸", "戊癸合化火");
        }
    };

    /**
     * 天干相冲（天干+天干为键）
     */
    public static final Map<String, String> TIAN_GAN_XIANG_CHONG = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("甲庚", "甲庚相冲");
            put("乙辛", "乙辛相冲");
            put("丙壬", "丙壬相冲");
            put("丁癸", "丁癸相冲");
        }
    };

    /**
     * 天干相克（天干+天干为键）
     */
    public static final Map<String, String> TIAN_GAN_XIANG_KE = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("甲戊", "甲戊相克");
            put("乙己", "乙己相克");
            put("丙庚", "丙庚相克");
            put("丁辛", "丁辛相克");
            put("戊壬", "戊壬相克");
            put("己癸", "己癸相克");
            put("庚甲", "庚甲相克");
            put("辛乙", "辛乙相克");
            put("壬丙", "壬丙相克");
            put("癸丁", "癸丁相克");
        }
    };

    /**
     * 地支半合（地支+地支为键）
     */
    public static final Map<String, String> DI_ZHI_BAN_HE = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("寅午", "寅午半合火局");
            put("申子", "申子半合水局");
            put("巳酉", "巳酉半合金局");
            put("亥卯", "亥卯半合木局");
            put("子辰", "子辰半合水局");
            put("午戌", "午戌半合火局");
            put("卯未", "卯未半合木局");
            put("酉丑", "酉丑半合金局");
        }
    };

    /**
     * 地支拱合（地支+地支为键）
     */
    public static final Map<String, String> DI_ZHI_GONG_HE = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("申辰", "申辰拱合子");
            put("亥未", "亥未拱合卯");
            put("寅戌", "寅戌拱合午");
            put("巳丑", "巳丑拱合酉");
        }
    };

    /**
     * 地支暗合（地支+地支为键）
     */
    public static final Map<String, String> DI_ZHI_AN_HE = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("卯申", "卯申暗合");
            put("午亥", "午亥暗合");
            put("丑寅", "丑寅暗合");
            put("寅未", "寅未暗合");
            put("寅午", "寅午暗合");
            put("子戌", "子戌暗合");
            put("子辰", "子辰暗合");
            put("子巳", "子巳暗合");
            put("巳酉", "巳酉暗合");
        }
    };

    /**
     * 地支三合（地支+地支+地支为键）
     */
    public static final Map<String, String> DI_ZHI_SAN_HE = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("申子辰", "申子辰三合水局");
            put("寅午戌", "寅午戌三合火局");
            put("巳酉丑", "巳酉丑三合金局");
            put("亥卯未", "亥卯未三合木局");
        }
    };

    /**
     * 地支六合（地支+地支为键）
     */
    public static final Map<String, String> DI_ZHI_LIU_HE = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("子丑", "子丑合化土");
            put("寅亥", "寅亥合化木");
            put("卯戌", "卯戌合化火");
            put("辰酉", "辰酉合化金");
            put("巳申", "巳申合化水");
            put("午未", "午未合化火");
        }
    };

    /**
     * 地支相刑（地支+地支为键）
     */
    public static final Map<String, String> DI_ZHI_XIANG_XING = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("寅巳申", "寅巳申三刑");
            put("丑戌未", "丑戌未三刑");
            put("寅巳", "寅巳相刑");
            put("巳申", "巳申相刑");
            put("申寅", "申寅相刑");
            put("丑戌", "丑戌相刑");
            put("戌未", "戌未相刑");
            put("未丑", "未丑相刑");
            put("子卯", "子卯相刑");
            put("酉酉", "酉酉自刑");
            put("亥亥", "亥亥自刑");
            put("午午", "午午自刑");
            put("辰辰", "辰辰自刑");
        }
    };

    /**
     * 地支三刑（地支+地支+地支为键）
     */
    public static final Map<String, String> DI_ZHI_SAN_XING = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("寅巳申", "寅巳申三刑");
            put("丑戌未", "丑戌未三刑");
        }
    };

    /**
     * 地支相冲（地支+地支为键）
     */
    public static final Map<String, String> DI_ZHI_XIANG_CHONG = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("子午", "子午相冲");
            put("丑未", "丑未相冲");
            put("寅申", "寅申相冲");
            put("卯酉", "卯酉相冲");
            put("辰戌", "辰戌相冲");
            put("巳亥", "巳亥相冲");
        }
    };

    /**
     * 地支相破（地支+地支为键）
     */
    public static final Map<String, String> DI_ZHI_XIANG_PO = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("子酉", "子酉相破");
            put("寅亥", "寅亥相破");
            put("卯午", "卯午相破");
            put("辰丑", "辰丑相破");
            put("巳申", "巳申相破");
            put("戌未", "戌未相破");
        }
    };

    /**
     * 地支相害（地支+地支为键）
     */
    public static final Map<String, String> DI_ZHI_XIANG_HAI = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("子未", "子未相害");
            put("丑午", "丑午相害");
            put("寅巳", "寅巳相害");
            put("卯辰", "卯辰相害");
            put("申亥", "申亥相害");
            put("酉戌", "酉戌相害");
        }
    };

    /**
     * 天干和干支的五行生克关系（天干+干支为键）
     */
    public static final Map<String, String> GAN_ZHI_WU_XING_SHENG_KE = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        /*
            一、身强：同我、生我。

            二、身弱：克我、我生、我克。
               1、克我为：克。
               2、我生为：泄。
               3、我克为：耗。
         */ {
            put("甲甲", "身强"); // 甲+甲：身强（同我。甲同甲→木同木）
            put("甲乙", "身强"); // 甲+乙：身强（同我。甲同乙→木同木）
            put("甲丙", "身弱"); // 甲+丙：身弱（我生。甲生丙→木生火）
            put("甲丁", "身弱"); // 甲+丁：身弱（我生。甲生丁→木生火）
            put("甲戊", "身弱"); // 甲+戊：身弱（我克。甲克戌→木克土）
            put("甲己", "身弱"); // 甲+己：身弱（我克。甲克己→木克土）
            put("甲庚", "身弱"); // 甲+庚：身弱（克我。庚克甲→金克木）
            put("甲辛", "身弱"); // 甲+辛：身弱（克我。辛克甲→金克木）
            put("甲壬", "身强"); // 甲+壬：身强（生我。壬生甲→水生木）
            put("甲癸", "身强"); // 甲+癸：身强（生我。癸生甲→水生木）
            put("甲子", "身强"); // 甲+子：身强（生我。子生甲→水生木）
            put("甲丑", "身弱"); // 甲+丑：身弱（我克。甲克丑→木克土）
            put("甲寅", "身强"); // 甲+寅：身强（同我。甲同寅→木同木）
            put("甲卯", "身强"); // 甲+卯：身强（同我。甲同卯→木同木）
            put("甲辰", "身弱"); // 甲+辰：身弱（我克。甲克辰→木克土）
            put("甲巳", "身弱"); // 甲+巳：身弱（我生。甲生巳→木生火）
            put("甲午", "身弱"); // 甲+午：身弱（我生。甲生午→木生火）
            put("甲未", "身弱"); // 甲+未：身弱（我克。甲克未→木克土）
            put("甲申", "身弱"); // 甲+申：身弱（克我。申克甲→金克木）
            put("甲酉", "身弱"); // 甲+酉：身弱（克我。酉克甲→金克木）
            put("甲戌", "身弱"); // 甲+戌：身弱（我克。甲克戌→木克土）
            put("甲亥", "身强"); // 甲+亥：身强（生我。亥生甲→水生木）
            put("乙甲", "身强"); // 乙+甲：身强（同我。乙同甲→木同木）
            put("乙乙", "身强"); // 乙+乙：身强（同我。乙同乙→木同木）
            put("乙丙", "身弱"); // 乙+丙：身弱（我生。乙生丙→木生火）
            put("乙丁", "身弱"); // 乙+丁：身弱（我生。乙生丁→木生火）
            put("乙戊", "身弱"); // 乙+戊：身弱（我克。乙克戌→木克土）
            put("乙己", "身弱"); // 乙+己：身弱（我克。乙克己→木克土）
            put("乙庚", "身弱"); // 乙+庚：身弱（克我。庚克乙→金克木）
            put("乙辛", "身弱"); // 乙+辛：身弱（克我。辛克乙→金克木）
            put("乙壬", "身强"); // 乙+壬：身强（生我。壬生乙→水生木）
            put("乙癸", "身强"); // 乙+癸：身强（生我。癸生乙→水生木）
            put("乙子", "身强"); // 乙+子：身强（生我。子生乙→水生木）
            put("乙丑", "身弱"); // 乙+丑：身弱（我克。乙克丑→木克土）
            put("乙寅", "身强"); // 乙+寅：身强（同我。乙同寅→木同木）
            put("乙卯", "身强"); // 乙+卯：身强（同我。乙同卯→木同木）
            put("乙辰", "身弱"); // 乙+辰：身弱（我克。乙克辰→木克土）
            put("乙巳", "身弱"); // 乙+巳：身弱（我生。乙生巳→木生火）
            put("乙午", "身弱"); // 乙+午：身弱（我生。乙生午→木生火）
            put("乙未", "身弱"); // 乙+未：身弱（我克。乙克未→木克土）
            put("乙申", "身弱"); // 乙+申：身弱（克我。申克乙→金克木）
            put("乙酉", "身弱"); // 乙+酉：身弱（克我。酉克乙→金克木）
            put("乙戌", "身弱"); // 乙+戌：身弱（我克。乙克戌→木克土）
            put("乙亥", "身强"); // 乙+亥：身强（生我。亥生乙→水生木）
            put("丙甲", "身强"); // 丙+甲：身强（生我。甲生丙→木生火）
            put("丙乙", "身强"); // 丙+乙：身强（生我。乙生丙→木生火）
            put("丙丙", "身强"); // 丙+丙：身强（同我。丙同丙→火同火）
            put("丙丁", "身强"); // 丙+丁：身强（同我。丙同丁→火同火）
            put("丙戊", "身弱"); // 丙+戊：身弱（我生。丙生戊→火生土）
            put("丙己", "身弱"); // 丙+己：身弱（我生。丙生己→火生土）
            put("丙庚", "身弱"); // 丙+庚：身弱（我克。丙克庚→火克金）
            put("丙辛", "身弱"); // 丙+辛：身弱（我克。丙克辛→火克金）
            put("丙壬", "身弱"); // 丙+壬：身弱（克我。壬克丙→水克火）
            put("丙癸", "身弱"); // 丙+癸：身弱（克我。癸克丙→水克火）
            put("丙子", "身弱"); // 丙+子：身弱（克我。子克丙→水克火）
            put("丙丑", "身弱"); // 丙+丑：身弱（我生。丙生丑→火生土）
            put("丙寅", "身强"); // 丙+寅：身强（生我。丙生寅→木生火）
            put("丙卯", "身强"); // 丙+卯：身强（生我。丙生卯→木生火）
            put("丙辰", "身弱"); // 丙+辰：身弱（我生。丙生辰→火生土）
            put("丙巳", "身强"); // 丙+巳：身强（同我。丙同巳→火同火）
            put("丙午", "身强"); // 丙+午：身强（同我。丙同午→火同火）
            put("丙未", "身弱"); // 丙+未：身弱（我生。丙生未→火生土）
            put("丙申", "身弱"); // 丙+申：身弱（我克。丙克申→火克金）
            put("丙酉", "身弱"); // 丙+酉：身弱（我克。丙克酉→火克金）
            put("丙戌", "身弱"); // 丙+戌：身弱（我生。丙生戌→火生土）
            put("丙亥", "身弱"); // 丙+亥：身弱（克我。亥克丙→水克火）
            put("丁甲", "身强"); // 丁+甲：身强（生我。甲生丁→木生火）
            put("丁乙", "身强"); // 丁+乙：身强（生我。乙生丁→木生火）
            put("丁丙", "身强"); // 丁+丙：身强（同我。丁同丙→火同火）
            put("丁丁", "身强"); // 丁+丁：身强（同我。丁同丁→火同火）
            put("丁戊", "身弱"); // 丁+戊：身弱（我生。丁生戊→火生土）
            put("丁己", "身弱"); // 丁+己：身弱（我生。丁生己→火生土）
            put("丁庚", "身弱"); // 丁+庚：身弱（我克。丁克庚→火克金）
            put("丁辛", "身弱"); // 丁+辛：身弱（我克。丁克辛→火克金）
            put("丁壬", "身弱"); // 丁+壬：身弱（克我。壬克丁→水克火）
            put("丁癸", "身弱"); // 丁+癸：身弱（克我。癸克丁→水克火）
            put("丁子", "身弱"); // 丁+子：身弱（克我。子克丁→水克火）
            put("丁丑", "身弱"); // 丁+丑：身弱（我生。丁生丑→火生土）
            put("丁寅", "身强"); // 丁+寅：身强（生我。丁生寅→木生火）
            put("丁卯", "身强"); // 丁+卯：身强（生我。丁生卯→木生火）
            put("丁辰", "身弱"); // 丁+辰：身弱（我生。丁生辰→火生土）
            put("丁巳", "身强"); // 丁+巳：身强（同我。丁同巳→火同火）
            put("丁午", "身强"); // 丁+午：身强（同我。丁同午→火同火）
            put("丁未", "身弱"); // 丁+未：身弱（我生。丁生未→火生土）
            put("丁申", "身弱"); // 丁+申：身弱（我克。丁克申→火克金）
            put("丁酉", "身弱"); // 丁+酉：身弱（我克。丁克酉→火克金）
            put("丁戌", "身弱"); // 丁+戌：身弱（我生。丁生戌→火生土）
            put("丁亥", "身弱"); // 丁+亥：身弱（克我。亥克丁→水克火）
            put("戊甲", "身弱"); // 戊+甲：身弱（克我。甲克戊→木克土）
            put("戊乙", "身弱"); // 戊+乙：身弱（克我。乙克戊→木克土）
            put("戊丙", "身强"); // 戊+丙：身强（生我。丙生戊→火生土）
            put("戊丁", "身强"); // 戊+丁：身强（生我。丁生戊→火生土）
            put("戊戊", "身强"); // 戊+戊：身强（同我。戊同戊→土同土）
            put("戊己", "身强"); // 戊+己：身强（同我。戊同己→土同土）
            put("戊庚", "身弱"); // 戊+庚：身弱（我生。戊生庚→土生金）
            put("戊辛", "身弱"); // 戊+辛：身弱（我生。戊生辛→土生金）
            put("戊壬", "身弱"); // 戊+壬：身弱（我克。戊克壬→土克水）
            put("戊癸", "身弱"); // 戊+癸：身弱（我克。戊克癸→土克水）
            put("戊子", "身弱"); // 戊+子：身弱（我克。戊克子→土克水）
            put("戊丑", "身强"); // 戊+丑：身强（同我。戊同丑→土同土）
            put("戊寅", "身弱"); // 戊+寅：身弱（克我。寅克戊→木克土）
            put("戊卯", "身弱"); // 戊+卯：身弱（克我。卯克戊→木克土）
            put("戊辰", "身强"); // 戊+辰：身强（同我。戊同辰→土同土）
            put("戊巳", "身强"); // 戊+巳：身强（生我。巳生戊→火生土）
            put("戊午", "身强"); // 戊+午：身强（生我。午生戊→火生土）
            put("戊未", "身强"); // 戊+未：身强（同我。戊同未→土同土）
            put("戊申", "身弱"); // 戊+申：身弱（我生。戊生申→土生金）
            put("戊酉", "身弱"); // 戊+酉：身弱（我生。戊生酉→土生金）
            put("戊戌", "身强"); // 戊+戌：身强（同我。戊同戌→土同土）
            put("戊亥", "身弱"); // 戊+亥：身弱（我克。戊克亥→土克水）
            put("己甲", "身弱"); // 己+甲：身弱（克我。甲克己→木克土）
            put("己乙", "身弱"); // 己+乙：身弱（克我。乙克己→木克土）
            put("己丙", "身强"); // 己+丙：身强（生我。丙生己→火生土）
            put("己丁", "身强"); // 己+丁：身强（生我。丁生己→火生土）
            put("己戊", "身强"); // 己+戊：身强（同我。己同戊→土同土）
            put("己己", "身强"); // 己+己：身强（同我。己同己→土同土）
            put("己庚", "身弱"); // 己+庚：身弱（我生。己生庚→土生金）
            put("己辛", "身弱"); // 己+辛：身弱（我生。己生辛→土生金）
            put("己壬", "身弱"); // 己+壬：身弱（我克。己克壬→土克水）
            put("己癸", "身弱"); // 己+癸：身弱（我克。己克癸→土克水）
            put("己子", "身弱"); // 己+子：身弱（我克。己克子→土克水）
            put("己丑", "身强"); // 己+丑：身强（同我。己同丑→土同土）
            put("己寅", "身弱"); // 己+寅：身弱（克我。寅克己→木克土）
            put("己卯", "身弱"); // 己+卯：身弱（克我。卯克己→木克土）
            put("己辰", "身强"); // 己+辰：身强（同我。己同辰→土同土）
            put("己巳", "身强"); // 己+巳：身强（生我。巳生己→火生土）
            put("己午", "身强"); // 己+午：身强（生我。午生己→火生土）
            put("己未", "身强"); // 己+未：身强（同我。己同未→土同土）
            put("己申", "身弱"); // 己+申：身弱（我生。己生申→土生金）
            put("己酉", "身弱"); // 己+酉：身弱（我生。己生酉→土生金）
            put("己戌", "身强"); // 己+戌：身强（同我。己同戌→土同土）
            put("己亥", "身弱"); // 己+亥：身弱（我克。己克亥→土克水）
            put("庚甲", "身弱"); // 庚+甲：身弱（我克。庚克甲→金克木）
            put("庚乙", "身弱"); // 庚+乙：身弱（我克。庚克乙→金克木）
            put("庚丙", "身弱"); // 庚+丙：身弱（克我。丙克庚→火克金）
            put("庚丁", "身弱"); // 庚+丁：身弱（克我。丁克庚→火克金）
            put("庚戊", "身强"); // 庚+戊：身强（生我。戊生庚→土生金）
            put("庚己", "身强"); // 庚+己：身强（生我。己生庚→土生金）
            put("庚庚", "身强"); // 庚+庚：身强（同我。庚同庚→金同金）
            put("庚辛", "身强"); // 庚+辛：身强（同我。庚同辛→金同金）
            put("庚壬", "身弱"); // 庚+壬：身弱（我生。庚生壬→金生水）
            put("庚癸", "身弱"); // 庚+癸：身弱（我生。庚生癸→金生水）
            put("庚子", "身弱"); // 庚+子：身弱（我生。庚生子→金生水）
            put("庚丑", "身强"); // 庚+丑：身强（生我。丑生庚→土生金）
            put("庚寅", "身弱"); // 庚+寅：身弱（我克。庚克寅→金克木）
            put("庚卯", "身弱"); // 庚+卯：身弱（我克。庚克卯→金克木）
            put("庚辰", "身强"); // 庚+辰：身强（生我。辰生庚→土生金）
            put("庚巳", "身弱"); // 庚+巳：身弱（克我。巳克庚→火克金）
            put("庚午", "身弱"); // 庚+午：身弱（克我。午克庚→火克金）
            put("庚未", "身强"); // 庚+未：身强（生我。未生庚→土生金）
            put("庚申", "身强"); // 庚+申：身强（同我。庚同申→金同金）
            put("庚酉", "身强"); // 庚+酉：身强（同我。庚同酉→金同金）
            put("庚戌", "身强"); // 庚+戌：身强（生我。戌生庚→土生金）
            put("庚亥", "身弱"); // 庚+亥：身弱（我生。庚生亥→金生水）
            put("辛甲", "身弱"); // 辛+甲：身弱（我克。辛克甲→金克木）
            put("辛乙", "身弱"); // 辛+乙：身弱（我克。辛克乙→金克木）
            put("辛丙", "身弱"); // 辛+丙：身弱（克我。丙克辛→火克金）
            put("辛丁", "身弱"); // 辛+丁：身弱（克我。丁克辛→火克金）
            put("辛戊", "身强"); // 辛+戊：身强（生我。戊生辛→土生金）
            put("辛己", "身强"); // 辛+己：身强（生我。己生辛→土生金）
            put("辛庚", "身强"); // 辛+庚：身强（同我。辛同庚→金同金）
            put("辛辛", "身强"); // 辛+辛：身强（同我。辛同辛→金同金）
            put("辛壬", "身弱"); // 辛+壬：身弱（我生。辛生壬→金生水）
            put("辛癸", "身弱"); // 辛+癸：身弱（我生。辛生癸→金生水）
            put("辛子", "身弱"); // 辛+子：身弱（我生。辛生子→金生水）
            put("辛丑", "身强"); // 辛+丑：身强（生我。丑生辛→土生金）
            put("辛寅", "身弱"); // 辛+寅：身弱（我克。辛克寅→金克木）
            put("辛卯", "身弱"); // 辛+卯：身弱（我克。辛克卯→金克木）
            put("辛辰", "身强"); // 辛+辰：身强（生我。辰生辛→土生金）
            put("辛巳", "身弱"); // 辛+巳：身弱（克我。巳克辛→火克金）
            put("辛午", "身弱"); // 辛+午：身弱（克我。午克辛→火克金）
            put("辛未", "身强"); // 辛+未：身强（生我。未生辛→土生金）
            put("辛申", "身强"); // 辛+申：身强（同我。辛同申→金同金）
            put("辛酉", "身强"); // 辛+酉：身强（同我。辛同酉→金同金）
            put("辛戌", "身强"); // 辛+戌：身强（生我。戌生辛→土生金）
            put("辛亥", "身弱"); // 辛+亥：身弱（我生。辛生亥→金生水）
            put("壬甲", "身弱"); // 壬+甲：身弱（我生。壬生甲→水生木）
            put("壬乙", "身弱"); // 壬+乙：身弱（我生。壬生乙→水生木）
            put("壬丙", "身弱"); // 壬+丙：身弱（我克。壬克丙→水克火）
            put("壬丁", "身弱"); // 壬+丁：身弱（我克。壬克丁→水克火）
            put("壬戊", "身弱"); // 壬+戊：身弱（克我。戊克壬→土克水）
            put("壬己", "身弱"); // 壬+己：身弱（克我。己克壬→土克水）
            put("壬庚", "身强"); // 壬+庚：身强（生我。庚生壬→金生水）
            put("壬辛", "身强"); // 壬+辛：身强（生我。辛生壬→金生水）
            put("壬壬", "身强"); // 壬+壬：身强（同我。壬同壬→水同水）
            put("壬癸", "身强"); // 壬+癸：身强（同我。壬同癸→水同水）
            put("壬子", "身强"); // 壬+子：身强（同我。壬同子→水同水）
            put("壬丑", "身弱"); // 壬+丑：身弱（克我。丑克壬→土克水）
            put("壬寅", "身弱"); // 壬+寅：身弱（我生。寅生壬→水生木）
            put("壬卯", "身弱"); // 壬+卯：身弱（我生。卯生壬→水生木）
            put("壬辰", "身弱"); // 壬+辰：身弱（克我。辰克壬→土克水）
            put("壬巳", "身弱"); // 壬+巳：身弱（我克。壬克巳→水克火）
            put("壬午", "身弱"); // 壬+午：身弱（我克。壬克午→水克火）
            put("壬未", "身弱"); // 壬+未：身弱（克我。未克壬→土克水）
            put("壬申", "身强"); // 壬+申：身强（生我。申生壬→金生水）
            put("壬酉", "身强"); // 壬+酉：身强（生我。酉生壬→金生水）
            put("壬戌", "身弱"); // 壬+戌：身弱（克我。戌克壬→土克水）
            put("壬亥", "身强"); // 壬+亥：身强（同我。壬同亥→水同水）
            put("癸甲", "身弱"); // 癸+甲：身弱（我生。癸生甲→水生木）
            put("癸乙", "身弱"); // 癸+乙：身弱（我生。癸生乙→水生木）
            put("癸丙", "身弱"); // 癸+丙：身弱（我克。癸克丙→水克火）
            put("癸丁", "身弱"); // 癸+丁：身弱（我克。癸克丁→水克火）
            put("癸戊", "身弱"); // 癸+戊：身弱（克我。戊克癸→土克水）
            put("癸己", "身弱"); // 癸+己：身弱（克我。己克癸→土克水）
            put("癸庚", "身强"); // 癸+庚：身强（生我。庚生癸→金生水）
            put("癸辛", "身强"); // 癸+辛：身强（生我。辛生癸→金生水）
            put("癸壬", "身强"); // 癸+壬：身强（同我。癸同壬→水同水）
            put("癸癸", "身强"); // 癸+癸：身强（同我。癸同癸→水同水）
            put("癸子", "身强"); // 癸+子：身强（同我。癸同子→水同水）
            put("癸丑", "身弱"); // 癸+丑：身弱（克我。丑克癸→土克水）
            put("癸寅", "身弱"); // 癸+寅：身弱（我生。寅生癸→水生木）
            put("癸卯", "身弱"); // 癸+卯：身弱（我生。卯生癸→水生木）
            put("癸辰", "身弱"); // 癸+辰：身弱（克我。辰克癸→土克水）
            put("癸巳", "身弱"); // 癸+巳：身弱（我克。癸克巳→水克火）
            put("癸午", "身弱"); // 癸+午：身弱（我克。癸克午→水克火）
            put("癸未", "身弱"); // 癸+未：身弱（克我。未克癸→土克水）
            put("癸申", "身强"); // 癸+申：身强（生我。申生癸→金生水）
            put("癸酉", "身强"); // 癸+酉：身强（生我。酉生癸→金生水）
            put("癸戌", "身弱"); // 癸+戌：身弱（克我。戌克癸→土克水）
            put("癸亥", "身强"); // 癸+亥：身强（同我。癸同亥→水同水）
        }
    };

    /**
     * 具体法获取喜用神（天干+季节为键）
     */
    public static final Map<String, List<String>> XI_YONG_SHEN = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /*
            1、日干是'甲'或者'乙'：春天出生，以火土金为喜用神；夏天出生，以水木为喜用神；秋天出生，以水木为喜用神；冬天出生，以火金为喜用神；生于四季末，以水土为喜用神。
        　　2、日干是'丙'或者'丁'：春天出生，以水土为喜用神；夏天出生，以金水为喜用神；秋天出生，以木火为喜用神；冬天出生，以木火土为喜用神；生于四季末，以木火金为喜用神。
        　　3、日干是'戊'或者'己'：春天出生，以火土为喜用神；夏天出生，以金水木为喜用神；秋天出生，以火土为喜用神；冬天出生，以火土为喜用神；生于四季末，以木金水为喜用神。
        　　4、日干是'庚'或者'辛'：春天出生，以土金为喜用神；夏天出生，以土水金为喜用神；秋天出生，以火木水为喜用神；冬天出生，以火土金为喜用神；生于四季末，以水木火为喜用神。
        　　5、日干是'壬'或者'癸'：春天出生，以金水为喜用神；夏天出生，以金水为喜用神；秋天出生，以木火土为喜用神；冬天出生，以木火土为喜用神；生于四季末，以金水为喜用神。

            ☞ 注：四季月一般为农历三月、六月、九月、十二月。
         */ {
            put("甲春", Arrays.asList("火", "土", "金"));
            put("甲夏", Arrays.asList("水", "木"));
            put("甲秋", Arrays.asList("水", "木"));
            put("甲冬", Arrays.asList("火", "金"));
            put("甲四季末", Arrays.asList("水", "土"));
            put("乙春", Arrays.asList("火", "土", "金"));
            put("乙夏", Arrays.asList("水", "木"));
            put("乙秋", Arrays.asList("水", "木"));
            put("乙冬", Arrays.asList("火", "金"));
            put("乙四季末", Arrays.asList("水", "土"));
            put("丙春", Arrays.asList("水", "土"));
            put("丙夏", Arrays.asList("金", "水"));
            put("丙秋", Arrays.asList("木", "火"));
            put("丙冬", Arrays.asList("木", "火", "土"));
            put("丙四季末", Arrays.asList("木", "火", "金"));
            put("丁春", Arrays.asList("水", "土"));
            put("丁夏", Arrays.asList("金", "水"));
            put("丁秋", Arrays.asList("木", "火"));
            put("丁冬", Arrays.asList("木", "火", "土"));
            put("丁四季末", Arrays.asList("木", "火", "金"));
            put("戊春", Arrays.asList("火", "土"));
            put("戊夏", Arrays.asList("金", "水", "木"));
            put("戊秋", Arrays.asList("火", "土"));
            put("戊冬", Arrays.asList("火", "土"));
            put("戊四季末", Arrays.asList("木", "金", "水"));
            put("己春", Arrays.asList("火", "土"));
            put("己夏", Arrays.asList("金", "水", "木"));
            put("己秋", Arrays.asList("火", "土"));
            put("己冬", Arrays.asList("火", "土"));
            put("己四季末", Arrays.asList("木", "金", "水"));
            put("庚春", Arrays.asList("土", "金"));
            put("庚夏", Arrays.asList("土", "水", "金"));
            put("庚秋", Arrays.asList("火", "木", "水"));
            put("庚冬", Arrays.asList("火", "土", "金"));
            put("庚四季末", Arrays.asList("水", "木", "火"));
            put("辛春", Arrays.asList("土", "金"));
            put("辛夏", Arrays.asList("土", "水", "金"));
            put("辛秋", Arrays.asList("火", "木", "水"));
            put("辛冬", Arrays.asList("火", "土", "金"));
            put("辛四季末", Arrays.asList("水", "木", "火"));
            put("壬春", Arrays.asList("金", "水"));
            put("壬夏", Arrays.asList("金", "水"));
            put("壬秋", Arrays.asList("木", "火", "土"));
            put("壬冬", Arrays.asList("木", "火", "土"));
            put("壬四季末", Arrays.asList("金", "水"));
            put("癸春", Arrays.asList("金", "水"));
            put("癸夏", Arrays.asList("金", "水"));
            put("癸秋", Arrays.asList("木", "火", "土"));
            put("癸冬", Arrays.asList("木", "火", "土"));
            put("癸四季末", Arrays.asList("金", "水"));
        }
    };

    /**
     * 五行对应方位（五行为键）
     */
    public static final Map<String, List<String>> WU_XING_FANG_WEI = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        {
            put("木", Arrays.asList("东", "东南"));     // 木：东、东南
            put("火", Collections.singletonList("南")); // 火：南
            put("土", Arrays.asList("西南", "东北"));   // 土：西南、东北
            put("金", Arrays.asList("西", "西北"));     // 金：西、西北
            put("水", Collections.singletonList("北")); // 水：北
        }
    };

    /**
     * 命卦（命卦宫位为键）
     */
    public static final Map<Integer, List<String>> MING_GUA = new HashMap<Integer, List<String>>() {
        private static final long serialVersionUID = -1;

        {
            put(1, Arrays.asList("坎卦（东四命）", "一白坎水贪狼星", "北斗第一星。紫微斗数，甲级主星。北斗天枢，智星吉星。象征强有力的统治管理。主欲望，为人多外向感性，情感极为浓烈，对爱情的要求很强烈；生活中多表现为多才多艺、有表演欲、风趣幽默、受异性欢迎的类型；感情方面对另一半有强烈的不安全感，自我防卫心强，越爱对方，越想掌控，冲突越大，因此容易一言不和突然分手。性格灵敏机巧，善于交际，喜欢认识朋友，人缘很好，常得异性助力。但态度有点随性，肢体语言过当，有时会被误认为随便、轻佻。不喜欢平淡的爱情，贪欢享受，欲望强烈，婚姻很难和谐，尤其女性更为辛苦。因在传统价值观下，不论男女都很难接受另一半的好人缘与多应酬。但男性的风流，可以用事业上的逢场作戏来当借口，女性则会被视为“花痴”、难受公婆喜欢。贪多骛得，自我强烈，喜欢享受被多人簇拥的感觉，爱情上易爆发多角纠纷。")); // 一白坎水贪狼星
            put(2, Arrays.asList("坤卦（西四命）", "二黑坤土巨门星", "北斗第二星。吉星，爱恨分明。思维活跃，直具辩才。主口才好，个性耿直，直言不讳，爱恨分明。多适合以口为主的职业。为人多心思细密，耿直明快，理解力强，善于处公共关系。随风转舵，见人说话，比较符合传统价值观。分析力与联想力出色，直具辩才。但在性格的反面，又有不安于现状，性格较为顽固，自信，猜疑心强，不大相信别人，凡事都要打破砂锅问到底的隐忧，喜欢自己通过实践而出真知。感觉较严肃，话出如风，舌利如刀，易招口舌是非，言语直截了当，往往容易得罪人。也容易恃才傲物，不能服人，逞口舌之快。刀子嘴，豆腐心；人际关系常常过犹不及。这是古来论命，称巨门星“化暗”、恃才傲物，不易服人。主口舌纠纷的原因。好在辛劳奋斗后方能有成就可言。巨门的另一个主要特质是个性多疑。多疑不是坏事，因为人性如果不多疑，就会鲁莽。")); // 二黑坤土巨门星
            put(3, Arrays.asList("震卦（东四命）", "三碧震木禄存星", "北斗第三星。大吉星，十二宫中皆为福论，并有解厄制化之功。禄存星不入辰、戌、丑、未四墓宫，免除天罗地网的限制，主人一生运势甚平顺，波折较小。禄存星又名天禄星，为原财星，化气为富贵，主天禄。顾名思义，禄存星是既能得到“禄”，又可获得积“存”，关系着人一生的荣华富贵，是最标准的财星。主自信心，工作能稳扎稳打，不疾不徐，专注安定，保守拘谨，可独善己身，独立性强，凡事多靠自己打拼，不依赖别人，不强求埋怨，能获得重任。具有节制、储蓄、稳固性质，积德之财。有德者财源不绝，无德者劳碌挣财。禄存为羊陀所夹，亦代表要赚取钱财，必同时带有是非与劳累；内心常感孤寂，常为保守吝啬守财奴，不懂享受财富乐趣，从事管理钱财或货物可趋吉避凶。禄存星前后二邻宫有擎羊、陀罗双煞星相夹纹，以保护天禄，使得禄存星必须瞻前顾后，前冲后扯，行事因而保守稳重，宽厚节约，往往在富有之后，容易遭人嫉妒排挤，并产生纠纷，对此须多加注意。")); // 三碧震木禄存星
            put(4, Arrays.asList("巽卦（东四命）", "四绿巽木文曲星", "北斗第四星。天权伐星，管科甲名声、文墨官场、功名文雅。文曲与文昌同样是聪明才智的象征，文昌偏重于刀笔功名、典章制度、文喜契据方面，较属于经世致用的理性学术追求；文曲则偏重于诗词歌赋、琴棋书画等方面的才艺研讨，较属于生活情趣的感性内涵充实。儒雅浪暖，对喜爱的学术较为感性，俊雅磊落、口才便佞是文曲的特性。主在文学艺术方面较有天赋；对怡情养性方面的学术，较有用心投入的意愿和兴趣。诗书满腹，心性灵巧，手足多能，具备良好的天分及悟性，是为少年勤奋有功名利禄之格。学术上较易有良好的表现。像是文艺中的琴曲、书画等多须靠美感赢得世人的肯定，所以靠文曲的模仿能力，很容易能学习到这类美的感受，进而展现其成果。艺术方面，有擅长艺术的特点，对于文艺，音乐或者绘画等方面享有天赋。也有优异的记忆能力，只是这种的优秀能力多是用在感受的记忆上，不似文昌是用在实质可见的文字图象记忆。且因为文曲很容易记忆情境与感受，所以文曲会极易触景生情而有了强说愁的现象，让人觉得多愁善感。")); // 四绿巽木文曲星
            put(5, Arrays.asList("中卦", "五黄中土廉贞星", "北斗第五星。化气为囚，桃花畅旺。是非分明，敢做敢当。廉贞星与北斗第一星贪狼星被并称为“廉贞贪狼两大桃花”；其中桃花级别都是甲级星系；贪狼为正，廉贞则为偏，一阴一阳；廉贞为隐性的星系，主桃花非常旺。但它同样是一颗让人捉摸不透的星系，有着反复多变的性格预示。主人负责尽职，见识不凡，思想新颖，是非分明，敢做敢当，积极进取。从阴面来说，又主人心高气傲，情绪多变，自视过高，一意孤行，锋芒太露，逞强好胜，要求过严，心狂性暴。“自古廉贞最难辨”就是指的古人对于这颗星非常难以理解。且廉贞星古书称之为“杀星”与“囚星”。此星入命，心高气傲，个性冲动，所以行事常带邪气。但这个“邪”，是不信邪，是只要我喜欢，有什么不可以的我行我素，宁可闯得头破血流，也绝对相信自己，爱与恨都很容易走极端。傲气凌人，有优越感，有施舍对方、下嫁（娶）的感觉。一旦对方强过自己，会在心理上抗拒，自我防卫心很强。因此常造成爱情上的两难；对方不强不爱，对方太强也不爱，择偶的弹性很低，常会有高不成低不就的感慨。因为个人意识太强，常常否定社会主流价值观或道德观，容易走偏锋，在古时候是非常不讨喜的一颗星，但是在多元价值观的现代，廉贞的高行动力及意志力就很有发挥的空间，需要加强的则是耐力及情绪的控制，自然容易有所表现。")); // 五黄中土廉贞星
            put(6, Arrays.asList("乾卦（西四命）", "六白乾金武曲星", "北斗第六星。化气为财，财帛宫主。司掌财富、武勇，主财旺。武曲刚直坚韧，毅力惊人，是十四颗主星中之最。主刚毅果决，自立自强，吃苦耐劳，勇于任事，不畏挫折，负责尽职。武曲勇于任事，不畏挫折，身先士卒，同甘共苦，严肃刚直，不怒而威，让人不易亲近。当部属时，倔强固执，待人欠缺圆通，不会争功诿过，默默做事，对拍马逢迎颇为不屑，也因此不易受到赏识，换句话说，贵人运势较弱，这也是为何少年不易发达的原因。但当上领导者后，发挥天赋领导能力的武曲，则常能一鸣惊人，一飞冲天。只是太过理性，不重人和的结果，或是太过追逐名利，满身铜臭；处事略嫌严苛，自我要求过高，权力欲望太大。或是刚愎孤直，仇人比朋友多，中晚年后，孤独难免。举手投足之间，少了紫微、天府的帝王之气，却又比天相沉稳。眼神坚毅慈和，声调有力，动作敏捷，活动力强，凡事操心，重视秩序整洁，举止沉稳威严。")); // 六白乾金武曲星
            put(7, Arrays.asList("兑卦（西四命）", "七赤兑金破军星", "北斗第七星。化气为耗，司夫妻子女、领导管理。破军星古书称之为“耗星”。这个“耗”，代表破坏力、消耗力。在十四颗主星之中，个性最冲动，变化性最强。由于破军具备“耗”的特性，人生自然变化多端，成败难论。和七杀坐命的人一样，我行我素，喜新厌旧，个性倔强，反抗心重，不易合作，欠缺弹性，遇事每多辩驳，翻脸六亲不认。颇难管理。近朱者赤，近墨者黑，碰到不好的朋友或主管，很容易变坏；但是碰到好的朋友或主管，则常能发挥所长，身体力行，求新求变，吃苦耐劳，勇于任事，不畏横暴，善恶分明，反应迅捷，坦白直率。破军带有强烈的傲气，对自己非常有自信，落在夫妻宫里，代表此人对爱情期望很高，对感情的要求很强烈。爱上一个人的结果，就会完全投入，可是投入越深，得到的失望可能会越大。由于优越感作祟，廉贞会用“爱”做理由，对另一半颐指气使；用“我会让你更好”当借口，来不断要求对方。如果对方做不到，破军星又难以妥协，由于自信太强，反而很难抽身，无法接受分手，强烈的话，可能演变成极端的报复行动。爱情因此而消逝。破军适合坐落在事业宫，其主做事态度认真，一丝不苟，外柔内刚，又喜欢交朋友，初与人接触时，很容易从生变熟；但变熟之后，也会因一言不和，不遂己意而产生冲突，让外人以为他们人际关系复杂多变，对此也得多加注意。")); // 七赤兑金破军星
            put(8, Arrays.asList("艮卦（西四命）", "八白艮土左辅星", "北斗第八星。匡扶紫微，辅助天府。贵人辅佐，辅助善星。左辅星入命宫，主人面目清秀端正，面色黄白，稳重慷慨，大多随和，有上进心、有抱负。为人厚道，仁慈耿直，随和大方，心怀宽恕，有容人之量。做事颇有条理，稳健谨慎，计划性强。性好助人，乐善好施，信义卓著，一生亦多贵人扶持，并得异性之助力尤多。左辅星、右弼星的特性：左辅、右弼又称贵人星，辅佐(辅助)星。此处的贵人是属于同辈，而且是双向的，辅、弼都有〝帮助〞之意；亦即你帮助了别人也会得到别人的帮助。二颗星皆主〝善〞。")); // 八白艮土左辅星
            put(9, Arrays.asList("离卦（东四命）", "九紫离火右弼星", "北斗第九星。北斗之助，在数为善。右弼之宿，号为吉庆。其色紫红，性最燥，吉者遇之立刻发福，凶者值之勃然大祸，故术数家称为赶煞催贵之神，但火性刚不能容邪，宜吉不宜凶。主精晓文墨，文采风骚，个性娩转，好善乐施，亲和力佳。能提升个人的良善的本质。女命贤淑端庄善解人意，遇事能够往好的方面想，也能够多行好事，和谐善良，乐于助人，只是冲劲少了些。而右弼又较左辅多了些不受约束的本质，也所以多了些异性桃花缘。特征为平和宽容，可能让企图心少了些，要获致成功，则须多加些冲劲才行。多了些不受约束的本质，有时会有稍微偏差的行为出现，甚至有桃花纠纷，则是要留意的地方。")); // 九紫离火右弼星
        }
    };

    /**
     * 五行旺衰（月支为键）
     */
    public static final Map<String, List<String>> WU_XING_WANG_SHUAI = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        {
            put("子", Arrays.asList("水旺", "木相", "金休", "土囚", "火死"));
            put("丑", Arrays.asList("土旺", "金相", "火休", "木囚", "水死"));
            put("寅", Arrays.asList("木旺", "火相", "水休", "金囚", "土死"));
            put("卯", Arrays.asList("木旺", "火相", "水休", "金囚", "土死"));
            put("辰", Arrays.asList("土旺", "金相", "火休", "木囚", "水死"));
            put("巳", Arrays.asList("火旺", "土相", "木休", "水囚", "金死"));
            put("午", Arrays.asList("火旺", "土相", "木休", "水囚", "金死"));
            put("未", Arrays.asList("土旺", "金相", "火休", "木囚", "水死"));
            put("申", Arrays.asList("金旺", "水相", "土休", "火囚", "木死"));
            put("酉", Arrays.asList("金旺", "水相", "土休", "火囚", "木死"));
            put("戌", Arrays.asList("土旺", "金相", "火休", "木囚", "水死"));
            put("亥", Arrays.asList("水旺", "木相", "金休", "土囚", "火死"));
        }
    };

    /**
     * 出生年骨重（年干支为键）
     */
    public static final Map<String, Integer> YEAR_GU_ZHONG = new HashMap<String, Integer>() {
        private static final long serialVersionUID = -1;

        {
            put("甲子", 12); // 甲子年：一两二钱
            put("乙丑", 9);  // 乙丑年：九钱
            put("丙寅", 6);  // 丙寅年：六钱
            put("丁卯", 7);  // 丁卯年：七钱
            put("戊辰", 12); // 戊辰年：一两二钱
            put("己巳", 5);  // 己巳年：五钱
            put("庚午", 9);  // 庚午年：九钱
            put("辛未", 8);  // 辛未年：八钱
            put("壬申", 7);  // 壬申年：七钱
            put("癸酉", 8);  // 癸酉年：八钱
            put("甲戌", 15); // 甲戌年：一两五钱
            put("乙亥", 9);  // 乙亥年：九钱
            put("丙子", 16); // 丙子年：一两六钱
            put("丁丑", 8);  // 丁丑年：八钱
            put("戊寅", 8);  // 戊寅年：八钱
            put("己卯", 19); // 己卯年：一两九钱
            put("庚辰", 12); // 庚辰年：一两二钱
            put("辛巳", 6);  // 辛巳年：六钱
            put("壬午", 18); // 壬午年：八钱
            put("癸未", 7);  // 癸未年：七钱
            put("甲申", 5);  // 甲申年：五钱
            put("乙酉", 15); // 乙酉年：一两五钱
            put("丙戌", 6);  // 丙戌年：六钱
            put("丁亥", 16); // 丁亥年：一两六钱
            put("戊子", 15); // 戊子年：一两五钱
            put("己丑", 7);  // 己丑年：七钱
            put("庚寅", 9);  // 庚寅年：九钱
            put("辛卯", 12); // 辛卯年：一两二钱
            put("壬辰", 10); // 壬辰年：一两
            put("癸巳", 7);  // 癸巳年：七钱
            put("甲午", 15); // 甲午年：一两五钱
            put("乙未", 6);  // 乙未年：六钱
            put("丙申", 5);  // 丙申年：五钱
            put("丁酉", 14); // 丁酉年：一两四钱
            put("戊戌", 14); // 戊戌年：一两四钱
            put("己亥", 9);  // 己亥年：九钱
            put("庚子", 7);  // 庚子年：七钱
            put("辛丑", 7);  // 辛丑年：七钱
            put("壬寅", 9);  // 壬寅年：九钱
            put("癸卯", 12); // 癸卯年：一两二钱
            put("甲辰", 8);  // 甲辰年：八钱
            put("乙巳", 7);  // 乙巳年：七钱
            put("丙午", 13); // 丙午年：一两三钱
            put("丁未", 5);  // 丁未年：五钱
            put("戊申", 14); // 戊申年：一两四钱
            put("己酉", 5);  // 己酉年：五钱
            put("庚戌", 9);  // 庚戌年：九钱
            put("辛亥", 17); // 辛亥年：一两七钱
            put("壬子", 5);  // 壬子年：五钱
            put("癸丑", 7);  // 癸丑年：七钱
            put("甲寅", 12); // 甲寅年：一两二钱
            put("乙卯", 8);  // 乙卯年：八钱
            put("丙辰", 8);  // 丙辰年：八钱
            put("丁巳", 6);  // 丁巳年：六钱
            put("戊午", 19); // 戊午年：一两九钱
            put("己未", 6);  // 己未年：六钱
            put("庚申", 8);  // 庚申年：八钱
            put("辛酉", 16); // 辛酉年：一两六钱
            put("壬戌", 10); // 壬戌年：一两
            put("癸亥", 6);  // 癸亥年：六钱
        }
    };

    /**
     * 出生月骨重（农历月为键）
     */
    public static final Map<String, Integer> MONTH_GU_ZHONG = new HashMap<String, Integer>() {
        private static final long serialVersionUID = -1;

        {
            put("寅", 6);  // 寅月（正月）：六钱
            put("卯", 7);  // 卯月（二月）：七钱
            put("辰", 18); // 辰月（三月）：一两八钱
            put("巳", 9);  // 巳月（四月）：九钱
            put("午", 5);  // 午月（五月）：五钱
            put("未", 16); // 未月（六月）：一两六钱
            put("申", 9);  // 申月（七月）：九钱
            put("酉", 15); // 酉月（八月）：一两五钱
            put("戌", 18); // 戌月（九月）：一两八钱
            put("亥", 8);  // 亥月（十月）：八钱
            put("子", 9);  // 子月（冬月）：九钱
            put("丑", 5);  // 丑月（腊月）：五钱
        }
    };

    /**
     * 出生日骨重（农历日为键）
     */
    public static final Map<String, Integer> DAY_GU_ZHONG = new HashMap<String, Integer>() {
        private static final long serialVersionUID = -1;

        {
            put("初一", 5);  // 初一：五钱
            put("初二", 10); // 初二：一两
            put("初三", 8);  // 初三：八钱
            put("初四", 15); // 初四：一两五钱
            put("初五", 16); // 初五：一两六钱
            put("初六", 15); // 初六：一两五钱
            put("初七", 8);  // 初七：八钱
            put("初八", 16); // 初八：一两六钱
            put("初九", 8);  // 初九：八钱
            put("初十", 16); // 初十：一两六钱
            put("十一", 9);  // 十一：九钱
            put("十二", 17); // 十二：一两七钱
            put("十三", 8);  // 十三：八钱
            put("十四", 17); // 十四：一两七钱
            put("十五", 10); // 十五：一两
            put("十六", 8);  // 十六：八钱
            put("十七", 9);  // 十七：九钱
            put("十八", 18); // 十八：一两八钱
            put("十九", 5);  // 十九：五钱
            put("二十", 15); // 二十：一两五钱
            put("廿一", 10); // 二十一：一两
            put("廿二", 9);  // 二十二：九钱
            put("廿三", 8);  // 二十三：八钱
            put("廿四", 9);  // 二十四：九钱
            put("廿五", 15); // 二十五：一两五钱
            put("廿六", 18); // 二十六：一两八钱
            put("廿七", 7);  // 二十七：七钱
            put("廿八", 8);  // 二十八：八钱
            put("廿九", 16); // 二十九：一两六钱
            put("三十", 6);  // 三十：六钱
        }
    };

    /**
     * 出生时辰骨重（农历时为键）
     */
    public static final Map<String, Integer> HOUR_GU_ZHONG = new HashMap<String, Integer>() {
        private static final long serialVersionUID = -1;

        {
            put("子", 16); // 子时：一两六钱
            put("丑", 6);  // 丑时：六钱
            put("寅", 7);  // 寅时：七钱
            put("卯", 10); // 卯时：一两
            put("辰", 9);  // 辰时：九钱
            put("巳", 16); // 巳时：一两六钱
            put("午", 10); // 午时：一两
            put("未", 8);  // 未时：八钱
            put("申", 8);  // 申时：八钱
            put("酉", 9);  // 酉时：九钱
            put("戌", 6);  // 戌时：六钱
            put("亥", 6);  // 亥时：六钱
        }
    };

    /**
     * 男命骨重批注（骨重为键）
     */
    public static final Map<Integer, String> YUN_CHENG_WAN = new HashMap<Integer, String>() {
        private static final long serialVersionUID = -1;

        {
            put(21, "短命非业谓大空，平生灾难事重重，凶祸频临陷逆境，终世困苦事不成。");
            put(22, "身寒骨冷苦伶仃，此命推来行乞人，劳劳碌碌无度日，终年打拱过平生。");
            put(23, "此命推来骨肉轻，求谋做事事难成，妻儿兄弟实难靠，外出他乡做散人。");
            put(24, "此命推来福禄无，门庭困苦总难荣，六亲骨肉皆无靠，流浪他乡作老翁。");
            put(25, "此命推来祖业微，门庭营度似稀奇，六亲骨肉如冰炭，一世勤劳自把持。");
            put(26, "平生衣禄苦中求，独自营谋事不休，离祖出门宜早计，晚来衣禄自无休。");
            put(27, "一生作事少商量，难靠祖宗作主张，独马单枪空做去，早年晚岁总无长。");
            put(28, "一生行事似飘蓬，祖宗产业在梦中，若不过房改名姓，也当移徒二三通。");
            put(29, "初年运限未曾亨，纵有功名在后成，须过四旬才可立，移居改姓始为良。");
            put(30, "劳劳碌碌苦中求，东奔西走何日休，若使终身勤与俭，老来稍可免忧愁。");
            put(31, "忙忙碌碌苦中求，何日云开见日头，难得祖基家可立，中年衣食渐能周。");
            put(32, "初年运蹇事难谋，渐有财源如水流，到得中年衣食旺，那时名利一齐收。");
            put(33, "早年作事事难成，百计徒劳枉费心，半世自如流水去，后来运到得黄金。");
            put(34, "此命福气果如何，僧道门中衣禄多，离祖出家方为妙，朝晚拜佛念弥陀。");
            put(35, "生平福量不周全，祖业根基觉少传，营事生涯宜守旧，时来衣食胜从前。");
            put(36, "此命终身运不通，劳劳作事尽皆空，苦心竭力成家计，到得那时在梦中。");
            put(37, "此命般般事不成、弟兄少力自孤行。虽然祖业须微有，来得明时去不明。");
            put(38, "一身骨肉最清高，早入簧门姓氏标。待到年将三十六，蓝衫脱去换红袍。");
            put(39, "不须劳碌过平生，独自成家福不轻，早有福星常照命，任君行去百般成。");
            put(40, "平生衣禄是绵长，件件心中自主张。前面风霜多受过，后来必定享安康。");
            put(41, "此命推来事不同，为人能干异凡庸，中年还有逍遥福，不比前时运未通。");
            put(42, "得宽怀处且宽怀，何用双眉皱不开，若使中年命运济，那时名利一齐来。");
            put(43, "为人心性最聪明，作事轩昂近贵人，衣禄一生天数定，不须劳碌过平生。");
            put(44, "万事由天莫苦求，须知福禄命里收，少壮名利难如意，晚景欣然更不忧。");
            put(45, "名利推来竟若何，前番辛苦后奔波。命中难养男与女，骨肉扶持也不多。");
            put(46, "东西南北尽皆通，出姓移居更觉隆，衣禄无亏天数定，中年晚景一般同。");
            put(47, "此命推为旺末年，妻荣子贵自怡然，平生原有滔滔福，财源滚滚似水流。");
            put(48, "初年运道未曾亨，若是蹉跎再不兴，兄弟六亲皆无靠，一身事业晚年成。");
            put(49, "此命推来福不轻，自成自立显门庭，从来富贵人钦敬，使婢差奴过一生。");
            put(50, "为利为名终日劳，中年福禄也多遭，老来是有财星照，不比前番目下高。");
            put(51, "一世荣华事事通，不须劳碌自亨通，弟兄叔侄皆如意，家业成时福禄宏。");
            put(52, "一世荣华事事能，不须劳思自然宁，宗族欣然心皆好，家业丰亨自称心。");
            put(53, "此格推为气量真，兴家发达在其中，一生福禄安排定，却是人间一富翁。");
            put(54, "此命推来厚且清，诗书满腹看功成，丰衣足食自然稳，正是人间有福人。");
            put(55, "走马扬鞭争利名，少年作事费筹论，一朝福禄源源至，富贵荣华显六亲。");
            put(56, "此格推来礼义通，一身福禄用无穷，甜酸苦辣皆尝过，滚滚财源稳且丰。");
            put(57, "福禄丰盈万事全，一身荣耀乐天年。名扬威震人争羡，此世逍遥宛似仙。");
            put(58, "平生福禄自然来，名利兼全福寿偕，金榜题名为贵客，紫袍玉带走金阶。");
            put(59, "细推此格妙且清，必定才高礼义通，甲第之中应有分，扬鞭走马显威荣。");
            put(60, "一朝金榜快题名，显祖荣宗大器成，衣禄定然原裕足，田园财帛更丰盈。");
            put(61, "不作朝中金榜客，定为世上大财翁，聪明天赋经书熟，名显高科自是荣。");
            put(62, "此命生来福不穷，读书必定显亲宗，紫衣金带为卿相，富贵荣华熟与同。");
            put(63, "命主为官福禄长，得来富贵实丰常，名题雁塔传金榜，大显门庭天下扬。");
            put(64, "此格威权不可当，紫袍金带尘高堂。荣华富贵谁能及？万古留名姓氏扬。");
            put(65, "细推此命福非轻，富贵荣华孰与争？定国安邦人极品，威声显赫震寰瀛。");
            put(66, "此格人间一福人，堆金积玉满堂春，从来富贵由天定，金榜题名更显亲。");
            put(67, "此命生来福自宏，田园家业最高隆，平生衣禄盈丰足，一世荣华万事通。");
            put(68, "富贵由天莫苦求，万金家计不须谋，如今不比前翻事，祖业根基千古留。");
            put(69, "君是人间衣禄星，一生富贵众人钦，总然福禄由天定，安享荣华过一生。");
            put(70, "此命推来福禄宏，不须愁虑苦劳心，荣华富贵已天定，正笏垂绅拜紫宸。");
            put(71, "此命生成大不同，公侯卿相在其中。一生自有逍遥福，富贵荣华极品隆。");
            put(72, "此格世界罕有生，十代积善产此人。天上紫微来照命，统治万民乐太平。");
        }
    };

    /**
     * 女命骨重批注（骨重为键）
     */
    public static final Map<Integer, String> YUN_CHENG_WOMAN = new HashMap<Integer, String>() {
        private static final long serialVersionUID = -1;

        {
            put(21, "短命非业谓大空，平生灾难事重重，凶祸频临陷逆境，终世困苦事不成。");
            put(22, "此命孤冷有凄伶，此命推来路乞人，操心烦脑度平日，一生育苦度光阴。");
            put(23, "此命推来骨肉轻，求财谋事事难成。弟妹六亲无有靠，繁绱家事难以持。");
            put(24, "此命推来福禄无，家务辛苦难以扶。丈夫儿女皆无靠，流落他乡作游孤。");
            put(25, "此命推来八字轻，六庭艰辛多苦凄。娘家六友冷如灰，一生操劳多忧心。");
            put(26, "平生衣禄苦寻求，女命生来带忧愁。辛酸苦辣他尝过，晚年衣钵本无忧。");
            put(27, "竹平做事少间量，难靠夫君做主张。心问口来口问心，自做主张过光阴。");
            put(28, "女命生来八字经，得善做事一无情。你把别人当亲生，别人对你假殷情。");
            put(29, "花枝要来性情硬，自奔自立不求人。若向求财方可止，有苦有甜度光阴。");
            put(30, "此命推来比郎强，婚姻大事碍无防。中年走过坎坷运，末年渐比先前强。");
            put(31, "早年行运在忙碌，劳碌奔波苦中求。自奔自愁把家立，后来晚景无忧愁。");
            put(32, "时逢吉神在运中，纵有凶处不为凶。真变假来假变真，结拜弟妹当亲生。");
            put(33, "八字命来张张薄，勤俭持家皆可过。年华巴如流水过，末年运至受福禄。");
            put(34, "矮巴勾枣难捞枝，好人寻命不投机。谋望献身最费力，婚姻同移总是虚。");
            put(35, "女子走冰怕冰薄，交易出行犯琢磨。婚姻周郎休此意，官司口舌须要知。");
            put(36, "忧愁常锁两眉间，女命万绪挂心头。从今以后防口角，任意行而不相天。");
            put(37, "此命来时运费多，此作推车受折磨。山路崎岖吊下耳，左插右安安不着。");
            put(38, "凤鸣岐山闻四方，女命逢之大吉昌。走失夫君音有信，晚年衣禄人财多。");
            put(39, "此命推来运不通，劳碌奔波一场空。好似俊鸟关笼中，中年末限起秋风。");
            put(40, "目上月令如运关，千心万苦受煎熬。女子苦难受过来，晚年福康比花艳。");
            put(41, "此命推来一般般，女子为人很非凡。中年逍遥多自在，晚年更比中年强。");
            put(42, "枯井破废已多年，一朝泉水出来鲜。资生济竭人称美，来运转喜自然时。");
            put(43, "推车靠涯道路赶，女命求财也费难。婚姻出行无阴碍，疾病口舌多安宁。");
            put(44, "夜梦金银醒来空，女子谋事运不能。婚姻难成交易获，夫君走失不见踪。");
            put(45, "此命终身驳杂多，六亲骨肉不相助。命中男妇都难养，劳碌辛苦还奔波。");
            put(46, "孤舟得水离沙滩，女命出外早远家。是非口舌皆无碍，婚姻合伙更不差。");
            put(47, "时来运转吉气发，多年枯木又开花。枝叶重生多茂盛，凡人见了凡人夸。");
            put(48, "一朵鲜花镜中开，看着极好取不来。劝你休把镜花想，女命推业主可怪。");
            put(49, "此命推来福不轻，女子随君显门庭。容貌美满热人爱，银钱富足过一生。");
            put(50, "马氏太公不相和，好命逢之尤凝多。恩人无义反成怨，是非平地起风波。");
            put(51, "肥羊失群入山岗，饿虎逢之把口张。适口充肠心欢喜，女名八安大吉昌。");
            put(52, "顺风行舟扯起棚，上天又助一顺风。不用费力逍遥去，任意而行大亨通。");
            put(53, "此命相貌眉活秀，文武双全功名成。一生衣禄皆无愁，可算世上有福人。");
            put(54, "学问满腹运气强，谋望求财大吉祥。交易出行大得意，是非口舌皆无妨。");
            put(55, "吉祥平安志量高，女命求财任逍遥。交易婚姻大有意，夫君在外有音耗。");
            put(56, "明珠书士离埃来，女命口角消散开。走失郎君当两归，交易有成水无灾。");
            put(57, "游鱼戏水被网惊，跳过龙门秧化龙。三根杨柳垂金线，万朵桃花显价能。");
            put(58, "此命推来转悠悠，时运未来莫强求。幸得今日重反点，自有好运在后头。");
            put(59, "雨雪载途泥泞至，交易不定难出行。疾病还拉慢婚姻，谋望求财事不成。");
            put(60, "女命八字喜气和，谋望求财吉庆多。口舌渐消疾病少，夫君走失归老窝。");
            put(61, "缘木求鱼事多端，虽不得鱼无后害。若是行险弄巧地，事不遂心枉安排。");
            put(62, "指日升高气象新，走失待人有音信。好命遇事遂心好，伺病口舌皆除根。");
            put(63, "五官脱运难抬头，女命须当把财求。交易少行有人助，疾病口舌不须愁。");
            put(64, "俊鸟曾得出笼中，脱离灾难显威风。一朝得意福立至，东南西北任意行。");
            put(65, "此命推来福不轻，慈善为事受人敬。天降文王开基业，富贵荣华八百年。");
            put(66, "时来运转锐气周，贤惠淑女君子求。钏鼓乐之大吉庆，女名逢之吉悠悠。");
            put(67, "乱丝无头定有头，碰着闲磨且暂推。交易出有无好处，谋事求财心不遂。");
            put(68, "水庭明月不可捞，女命早限命不高。交易出行难获得，末限命运渐渐好。");
            put(69, "太公封祖不非凡，女子求财稳如山。交易合伙大吉庆，疾病口角消除安。");
            put(70, "此命推来喜气新，郎君遇着多遂心。女命交了顺当运，富贵衣禄平乐生。");
            put(71, "此命推来鸿运交，再不需愁来苦劳。一生身有衣禄福，按享荣华在其中。");
        }
    };

    /**
     * 日柱论命（日柱为键）
     */
    public static final Map<String, String> DAY_ZHU_LUN_MING = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("甲子", "多学少成，防止有始无终；心性暴躁，幼年多灾。重拜父母，饮食保养；兄弟骨肉少靠，有子多刑。男人妻大，女人夫长；伶俐聪明贤能；受人尊重，人际关系需权衡得当。");
            put("乙丑", "慷慨大方，喜爱春风，多学少成，幼年现灾，父母重拜；九流中人，夫妻无刑，儿女不孤，六亲少靠；女人贤良纯和，但求财心切，要关照家庭。");
            put("丙寅", "多学少成，心性不定；口快舌硬，身闲心直，手足不停，利官近贵，女人贤良，聪明伶俐，辛勤收获。");
            put("丁卯", "手足不停，身心不闲，内外有劳，衣禄不少，性巧聪明，做事有头少尾，男人福份之命，女人企图宁静，操劳中有幸而获，防不知足。");
            put("戊辰", "喜气春风，利官近贵，骨肉刑伤，儿女不孤，女人温良贤达，有口无心，乌鸦嘴豆腐心，主招好夫，需防止他人误会。");
            put("己巳", "聪明才智，手足伶俐，小有功名，做事如意；夫妻和睦相处，诸事可为；女人衣食住行不缺，善良待人；男人多出风头，小有计谋，资性英敏，福厚之命。");
            put("庚午", "口快心直，利官近贵，小有殊荣，衣禄丰盈。男人权柄，辛勤持家，外面固执，有人钦佩；女人秀气，略显美感，荣夫益子，邻里钦佩。");
            put("辛未", "生有志气，心性宽容尚仁义，少年灾病；中年略好，头胎见女更吉，生男有刑克；夫妻和睦相处，事业顺利；女人持家方兴旺，男人顾家多利业。");
            put("壬申", "性巧聪明，才智少显，计谋多变，诚信待人接物功名有份；有贤妻子，为人英敏，受人喜欢；女人美丽，博得人助！阶段富贵，荣华自珍！福气子孙。");
            put("癸酉", "心直公平，伶牙俐齿，防言多失；衣禄自俭，有备无患，平稳足用；六亲冷淡，自谋求生；社会交往，多有人爱，虽事不成，少取勿贪，百业可为；女人助夫，勤俭持家，晚年享福。");
            put("甲戌", "口快舌伶，身闲心不空，少年贫民，中年奔波，略有权职，心机谋略，多学少成，小有名声；晚景福禄延年，自珍自爱益寿；助人惹怨，妒嫉常伴随而来。");
            put("乙亥", "为人和顺，幼年多灾，父母有刑，诚实待人接物，出外稍好；女人夫好，和睦相处、偕老，存心中正，中年财旺，防劳致病；婚迟子晚，子女防克，重拜义母，严教成才。");
            put("丙子", "胆大言广，有权谋，心机诡诈，早年平平，中年成就，晚景大好，女人饶舌，言多有失，安分守己，避免麻烦，福气自到。");
            put("丁丑", "为人和睦，衣禄不愁，初年有旺禄常在，晚景大好，更有结余，婚迟子晚，夫妻和睦相处，则百事顺利。女人旺夫爱子，孝敬，持家贤良。");
            put("戊寅", "性格猛烈，刚强招祸，易快易冷，交往闲杂，小人无情；早年勤俭，离祖发达，聪明才智，手足伶俐；晚年大有良机，不可莽撞，得安宁，有幸福。");
            put("己卯", "为人交往广泛，风流倜倘、豪杰，衣禄丰足，闲情逸致，嬉戏娱乐，有人遵从；六亲冷淡，自立为上，骨肉难为，妻招年长，配偶和睦；女人注意邻居关系，自我解脱，旷达胸怀，长寿，亲族贤达。");
            put("庚辰", "春风和气，劳禄霜雪一生，利官近贵，名利双全，衣食足用；中年平顺，晚年大兴，女人勤俭，持家有方，受人敬仰，坎坷风波。个别命运身体衰弱，注意健康，追求安全的人生。");
            put("辛巳", "友善机谋，能应变，志气过人，衣食足用；有贵人扶助，中年和顺，老有福运，心安理得，知足常乐，有长寿命。");
            put("壬午", "为人勤俭，父母刑伤，早年财物不顺利，存储不多，中年劳心，稍有所得，晚景旺子，事应积蓄，防备缺乏。女人兴家，贤能自达，恒心有获。");
            put("癸未", "心急口快，为人伶俐；救人无恩，反招是非，曲直难辩，多有误会。有财无库，过眼烟云，财来财去，精打细算，可免祸灾；女人贤慧，勤俭持家；晚景平安。");
            put("甲申", "衣禄不少，心性温柔，出入有为，技压群芳，初年不顺，中年劳神，晚景利达；夫妻和顺，儿女迟到，女人操劳，忍耐莫逆，家事平安。");
            put("乙酉", "口快心直，轩昂大方，乐观，衣禄足用，兄弟虽有，难为助力；与人为善，事业成功；女人兴旺，小有蓄财，平稳，追寻安康生活。");
            put("丙戌", "性情温和，技艺护身，衣禄不愁，豪杰和顺，自能有财；独立家业；前运不周，利益不顺，中年操劳，钱财有进；晚年荣华。女人中年奔波，辛苦求财，老年稍微聚财发旺，珍惜为盼。");
            put("丁亥", "性巧聪明，小有才智；独立自营，奔波操劳，儿女有利，见迟方好；好为善事，稍微旺财；女人贞节自爱，得贵人帮助，幸有财运，衣禄平稳。");
            put("戊子", "计算聪明，才智有余，文武两通，儿女早产防克；结婚当晚，财运良好，运气颇佳，用情不专，家花、野花，警慎为佳！女人自珍，贤慧发福。");
            put("己丑", "口快心直，通技艺人，能掌实权，诚意待人，博得众望，衣禄不少；男人防再娶，家庭当和睦相处，女人晚年发福。");
            put("庚寅", "心性急快，有口无心，大事有藏；易好易怒，性情反复无常，伤人还不知道；易结人缘，也易失人缘，衣禄足用；早年不聚财物，中年奔波，晚景丰隆，女人内助，理事发达，勤俭有得。");
            put("辛卯", "口快心直，有志气，有权柄，利官近贵，身闲心不空；六亲少靠，自立家业，少年劳禄，晚年大利；女人持家，操劳，勤俭节约可兴隆。");
            put("壬辰", "劳禄之人，手脚停不下来；早年难守，财来财去，晚景发达。女人操家，勤奋兴旺。");
            put("癸巳", "聪明伶俐，近贵人，中年风霜，春风之徒；慎重守护已成之事业，可暂发福；好心对人，却被人反目，晚景渐佳。");
            put("甲午", "为人和气，好娱乐；青春好风荡，交往朋友，利官近贵，逢凶化吉，骨肉少靠。女人口快，心直能言；爱情上需防祸不单行。");
            put("乙未", "少年勤俭，初年平顺；兄弟少靠；子息不孤；晚年聚财，可以兴旺；女人持家，恩夫旺子，精打细算。");
            put("丙申", "衣食丰足，利路亨通，技能生财；早年劳禄，女人持家，勤俭节约，旺相发达。");
            put("丁酉", "喜好面子，多情重恩，缘份奇特；利官近贵，初年劳累，身闲心苦。晚年兴隆，子女有为；女人清秀吉昌，半夫半财，自立成功。");
            put("戊戌", "为人和气，独立自营；早年颠倒运程，是非耗财；中年贪求，欲望高昂，遭人妒嫉。自重；苦学技艺，学有工夫之命。女人育养，中平之命运。");
            put("己亥", "计巧伶俐，衣食安稳，骨肉少力，六亲冷淡，儿女早见刑克，迟到稍好；夫妻和顺。女人清闲，晚景顺遂。");
            put("庚子", "持重，安稳，衣禄无亏，妻子贤慧，持家有方，建交贵人，提拔机遇，逢凶化吉。女人兴旺，福荫家庭。");
            put("辛丑", "心性温和，早年须防惊恐，意外之厄；虽有衣禄，骨肉少力，晚年好，女人旺家，事多发达。");
            put("壬寅", "为人口快心直，男女都不能早结婚，若早婚夫妻便相克。儿女宜迟，初年颠倒，中年兴旺。女人如意，发福之命。");
            put("癸卯", "衣食不少，凶中化吉，早年不顺，财来财去，晚年好。女人操持之命，遭遇妒嫉，好事难全，少年弱，中年劳禄，老有欢乐。");
            put("甲辰", "衣食丰足，清闲心情；早年弱，中年勤奋，情感有波折，事业发达，晚结婚，子女宜迟，帮助多。");
            put("乙巳", "为人好面子，救人无功；好事莫望；早年子女刑克，晚年安宁。女人助夫爱子。");
            put("丙午", "身闲心不空，初年耗财，宜做基础，技能工夫，求名不利，事多争端，兄弟各方。女人清秀，思维奇妙。");
            put("丁未", "喜怒无常，口舌能言，名利不缺，骨肉疏远，子息迟到；衣禄丰足；女人有旺运，勤俭能兴家。");
            put("戊申", "为人性急，易反复无常；一生劳禄，利官见贵，儿女刑伤，财库富足，女人有旺运，勤俭能兴家。");
            put("己酉", "为人聪明，衣禄丰足，六亲少靠，儿女早见；凡事宽量，百事通达，女人技巧多变，少灾之命。");
            put("庚戌", "为人快活，丑未防灾；利官近贵，敏捷聪明，福气晚年，救人无义；女人心贤，勤奋方能兴家。");
            put("辛亥", "不惹闲事，清静守中；早年不聚财，晚景荣华富贵。女人勤俭，福丰，助立家业。");
            put("壬子", "幼年显灾，中年衣食丰足，男有好妻，身闲心劳，多喜多忧，奔波事业；兄弟少力，六亲冷淡，勤奋自立；女人贤能，提防嫉妒之心。");
            put("癸丑", "衣禄不少，一生得人尊重；莫惹是非，父母难为。骨肉少靠，夫妇和睦相处，顺利，儿女见迟；财物早年难聚，波折。");
            put("甲寅", "为人诚实，利官见贵。家道兴盛，父母有利，重拜双亲。男人怕妻，命硬三分。女人管夫，子息兴旺。");
            put("乙卯", "志气轩昂，衣禄丰足，计巧精妙，言行有诈，有成有败；文武两业，坎坷难免，金石为开，一事可成，女人福气，勤俭添寿。");
            put("丙辰", "聪明才智，手足伶俐，衣禄无亏；身闲心劳，爱交朋友，中年兴隆，女人贤能，勤俭建家。");
            put("丁巳", "为人刚强，利官近贵，个性乖巧，不易顺从；兄弟居长，事业显荣。女人贤慧，勤奋发达，热诚事业，防有头无尾，宜精益求精，专心致志，精打细算。");
            put("戊午", "为人温良，志气宽宏，衣禄丰足；少年多灾，骨肉有刑，夫妻显克；女人亲属少帮；中年勤奋，有收获；夫妻性格欠协调，影响健康，老可无忧，子女孝顺。");
            put("己未", "口快舌硬，衣禄小有，得贵人帮，勤奋敬业，精打细算，财源不绝。交往朋友，避免闲杂。");
            put("庚申", "手足不停，为人清高，利官近贵。夫妻口角，烦多爱少。做好事难得好报，救人无功，遭遇妒忌。女人兴家，六亲冷淡。");
            put("辛酉", "为人伶俐，面气清爽，艰辛创业，自谋高见，口舌能言，高人敬重，财力广大，六亲冷淡，骨肉情疏，女人贤达。");
            put("壬戌", "好行善事，四处不停，心劳尤多，衣食不缺，结交贵人，提拔事业，早年平，中年劳，晚年好;女人贤慧，操劳兴家。");
            put("癸亥", "为人刚直，言语坦然，不顺人情；六亲疏远，交往淡淡；自立家业，女人缘旺，家庭坎坷，诚信待人，慎重欺诈；多注意自身健康。");
        }
    };

    /**
     * 姻缘（日干支纳音+农历月份为键）
     */
    public static final Map<String, String> YIN_YUAN = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("木1", "夫妻在年轻时就认识，二人心有灵犀，可惜中年过后，丈夫忍不住，见异思迁。");
            put("木2", "夫妻心连心，有长远的人生计划，喜欢互相比拼，晚年的时候，二人是富裕的。");
            put("木3", "在错误的选择下结了婚，婚后二人常常争吵，丈夫会在中年时候离开家庭，老来痛苦。");
            put("木4", "结婚过程中己有人出来相劝，可是男方花言巧语令女方下嫁，这段婚姻是为凶。");
            put("木5", "二人一生恩爱，少争吵，可惜缘份薄弱，常常分开两地，丈夫难以送自己最后一程。");
            put("木6", "丈夫必须年长十五岁或以上，夫妇有共同的兴趣，互相尊重，晚年二人死亡的差距不足五年。");
            put("木7", "离婚，夫妇成仇，子女成祸根，没有共同的心，大家都不愿维系这段婚姻。");
            put("木8", "孤独一人，即使结了婚，二人都常分离，而配偶年龄相进，会晚年丧偶。");
            put("木9", "夫妻二人年龄相若，可以平安步入晚年，老来互相照顾，一生无悔。");
            put("木10", "夫妻没有形克，二人幸福，有商有量，能够旺夫，结婚后可以白头，但丈夫赚钱不及妻子多。");
            put("木11", "丈夫脾气暴躁，没有忍耐，二十七岁前结婚的话将在三十岁后出现婚姻危机，二人难以白头。");
            put("木12", "夫妻可以白头到老（八字相冲的例外）二人互相照顾，没有家事烦恼，丈夫是中产人仕。");
            put("火1", "夫妻没有形克，二人幸福，有商有量，能够旺夫，结婚后可以白头，但丈夫赚钱不及妻子多。");
            put("火2", "丈夫脾气暴躁，没有忍耐，二十七岁前结婚的话将在三十岁后出现婚姻危机，二人难以白头。");
            put("火3", "夫妻可以白头到老（八字相冲的例外）二人互相照顾，没有家事烦恼，丈夫是中产人仕。");
            put("火4", "夫妻在年轻时就认识，二人心有灵犀，可惜中年过后，丈夫忍不住，见异思迁。");
            put("火5", "夫妻心连心，有长远的人生计划，喜欢互相比拼，晚年的时候，二人是富裕的。");
            put("火6", "在错误的选择下结了婚，婚后二人常常争吵，丈夫会在中年时候离开家庭，老来痛苦。");
            put("火7", "结婚过程中己有人出来相劝，可是男方花言巧语令女方下嫁，这段婚姻是为凶。");
            put("火8", "二人一生恩爱，少争吵，可惜缘份薄弱，常常分开两地，丈夫难以送自己最后一程。");
            put("火9", "丈夫必须年长十五岁或以上，夫妇有共同的兴趣，互相尊重，晚年二人死亡的差距不足五年。");
            put("火10", "离婚，夫妇成仇，子女成祸根，没有共同的心，大家都不愿维系这段婚姻。");
            put("火11", "孤独一人，即使结了婚，二人都常分离，而配偶年龄相进，会晚年丧偶。");
            put("火12", "夫妻二人年龄相若，可以平安步入晚年，老来互相照顾，一生无悔。");
            put("土1", "结婚过程中己有人出来相劝，可是男方花言巧语令女方下嫁，这段婚姻是为凶。");
            put("土2", "二人一生恩爱，少争吵，可惜缘份薄弱，常常分开两地，丈夫难以送自己最后一程。");
            put("土3", "丈夫必须年长十五岁或以上，夫妇有共同的兴趣，互相尊重，晚年二人死亡的差距不足五年。");
            put("土4", "离婚，夫妇成仇，子女成祸根，没有共同的心，大家都不愿维系这段婚姻。");
            put("土5", "孤独一人，即使结了婚，二人都常分离，而配偶年龄相进，会晚年丧偶。");
            put("土6", "夫妻二人年龄相若，可以平安步入晚年，老来互相照顾，一生无悔。");
            put("土7", "夫妻没有形克，二人幸福，有商有量，能够旺夫，结婚后可以白头，但丈夫赚钱不及妻子多。");
            put("土8", "丈夫脾气暴躁，没有忍耐，二十七岁前结婚的话将在三十岁后出现婚姻危机，二人难以白头。");
            put("土9", "夫妻可以白头到老（八字相冲的例外）二人互相照顾，没有家事烦恼，丈夫是中产人仕。");
            put("土10", "夫妻在年轻时就认识，二人心有灵犀，可惜中年过后，丈夫忍不住，见异思迁。");
            put("土11", "夫妻心连心，有长远的人生计划，喜欢互相比拼，晚年的时候，二人是富裕的。");
            put("土12", "在错误的选择下结了婚，婚后二人常常争吵，丈夫会在中年时候离开家庭，老来痛苦。");
            put("金1", "离婚，夫妇成仇，子女成祸根，没有共同的心，大家都不愿维系这段婚姻。");
            put("金2", "孤独一人，即使结了婚，二人都常分离，而配偶年龄相进，会晚年丧偶。");
            put("金3", "夫妻二人年龄相若，可以平安步入晚年，老来互相照顾，一生无悔。");
            put("金4", "夫妻没有形克，二人幸福，有商有量，能够旺夫，结婚后可以白头，但丈夫赚钱不及妻子多。");
            put("金5", "丈夫脾气暴躁，没有忍耐，二十七岁前结婚的话将在三十岁后出现婚姻危机，二人难以白头。");
            put("金6", "夫妻可以白头到老（八字相冲的例外）二人互相照顾，没有家事烦恼，丈夫是中产人仕。");
            put("金7", "夫妻在年轻时就认识，二人心有灵犀，可惜中年过后，丈夫忍不住，见异思迁。");
            put("金8", "夫妻心连心，有长远的人生计划，喜欢互相比拼，晚年的时候，二人是富裕的。");
            put("金9", "在错误的选择下结了婚，婚后二人常常争吵，丈夫会在中年时候离开家庭，老来痛苦。");
            put("金10", "结婚过程中己有人出来相劝，可是男方花言巧语令女方下嫁，这段婚姻是为凶。");
            put("金11", "二人一生恩爱，少争吵，可惜缘份薄弱，常常分开两地，丈夫难以送自己最后一程。");
            put("金12", "丈夫必须年长十五岁或以上，夫妇有共同的兴趣，互相尊重，晚年二人死亡的差距不足五年。");
            put("水1", "结婚过程中己有人出来相劝，可是男方花言巧语令女方下嫁，这段婚姻是为凶。");
            put("水2", "二人一生恩爱，少争吵，可惜缘份薄弱，常常分开两地，丈夫难以送自己最后一程。");
            put("水3", "丈夫必须年长十五岁或以上，夫妇有共同的兴趣，互相尊重，晚年二人死亡的差距不足五年。");
            put("水4", "离婚，夫妇成仇，子女成祸根，没有共同的心，大家都不愿维系这段婚姻。");
            put("水5", "孤独一人，即使结了婚，二人都常分离，而配偶年龄相进，会晚年丧偶。");
            put("水6", "夫妻二人年龄相若，可以平安步入晚年，老来互相照顾，一生无悔。");
            put("水7", "夫妻没有形克，二人幸福，有商有量，能够旺夫，结婚后可以白头，但丈夫赚钱不及妻子多。");
            put("水8", "丈夫脾气暴躁，没有忍耐，二十七岁前结婚的话将在三十岁后出现婚姻危机，二人难以白头。");
            put("水9", "夫妻可以白头到老（八字相冲的例外）二人互相照顾，没有家事烦恼，丈夫是中产人仕。");
            put("水10", "夫妻在年轻时就认识，二人心有灵犀，可惜中年过后，丈夫忍不住，见异思迁。");
            put("水11", "夫妻心连心，有长远的人生计划，喜欢互相比拼，晚年的时候，二人是富裕的。");
            put("水12", "在错误的选择下结了婚，婚后二人常常争吵，丈夫会在中年时候离开家庭，老来痛苦。");
        }
    };

    /**
     * 五行分析（年柱纳音五行为键）
     */
    public static final Map<String, String> WU_XING_FEN_XI = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("木", "木主仁，温柔随和，待人友善，富有同情心，有很强的正义感，能够结识到比较不错的朋友。在事业上认真踏实，但事业心并不强，为人处事乐观豁达，但是有时候会太过固执，而在无形中得罪人。");
            put("火", "火主礼，热情乐观，开朗大度，永远都精神饱满，遇事总会冲在最前方，待人谦虚淳朴，讲礼貌，也因此而受到大家的尊敬。但是也会因为太过于冲动，而失去理智，做事不计较后果，得罪人。");
            put("土", "土主信，为人厚道，信守承诺，处事大度，懂得知恩图报，对待父母非常孝顺。在生活中哪怕自己吃亏，也不会去占别人的便宜，常常受到大家的信赖。不过有时也固执己见，八匹马也拉不回来，缺乏变通。");
            put("金", "金主义，爱憎分明，嫉恶如仇，做事果断认真，具有远见，且有较强的组织能力。很重视情义，朋友有事，定会竭尽所能的去帮助。不过遇事会过于急躁，喜欢以自我为中心。");
            put("水", "水主智，足智多谋，聪明好学，深谋远虑。无论做什么事情都是非常有计划性的，绝不会盲目的去做。在生活中见多识广，人缘好，朋友多，对待感情很专一。不过由于做事太细致，会出现犹豫不决的情况，不敢去冒风险。");
        }
    };

    /**
     * 男女日主正财（日干为键）
     */
    public static final Map<String, List<String>> RI_ZHU_ZHENG_CAI = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /*
            我克者为财（男女通用）

                甲的正财为：己、未、丑
                乙的正财为：戊、辰、戌
                丙的正财为：辛、酉
                丁的正财为：庚、申
                戊的正财为：癸、亥
                己的正财为：壬、子
                庚的正财为：乙、卯
                辛的正财为：甲、寅
                壬的正财为：丁、巳
                癸的正财为：丙、午
        */ {
            put("甲", Arrays.asList("己", "丑", "未"));
            put("乙", Arrays.asList("戊", "辰", "戌"));
            put("丙", Arrays.asList("辛", "酉"));
            put("丁", Arrays.asList("庚", "申"));
            put("戊", Arrays.asList("癸", "亥"));
            put("己", Arrays.asList("壬", "子"));
            put("庚", Arrays.asList("乙", "卯"));
            put("辛", Arrays.asList("甲", "寅"));
            put("壬", Arrays.asList("丁", "巳"));
            put("癸", Arrays.asList("丙", "午"));
        }
    };

    /**
     * 男女日主偏财（日干为键）
     */
    public static final Map<String, List<String>> RI_ZHU_PIAN_CAI = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /*
            我克者为财（男女通用）

                甲的偏财为：戊、辰、戌
                乙的偏财为：己、未、丑
                丙的偏财为：庚、申
                丁的偏财为：辛、酉
                戊的偏财为：壬、子
                己的偏财为：癸、亥
                庚的偏财为：甲、寅
                辛的偏财为：乙、卯
                壬的偏财为：丙、午
                癸的偏财为：丁、巳
        */ {
            put("甲", Arrays.asList("戊", "辰", "戌"));
            put("乙", Arrays.asList("己", "丑", "未"));
            put("丙", Arrays.asList("庚", "申"));
            put("丁", Arrays.asList("辛", "酉"));
            put("戊", Arrays.asList("壬", "子"));
            put("己", Arrays.asList("癸", "亥"));
            put("庚", Arrays.asList("甲", "寅"));
            put("辛", Arrays.asList("乙", "卯"));
            put("壬", Arrays.asList("丙", "午"));
            put("癸", Arrays.asList("丁", "巳"));
        }
    };

    /**
     * 男日主正财（日干为键）
     */
    public static final Map<String, List<String>> NAN_RI_ZHU_ZHENG_TAO_HUA = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /*
            我克者为桃花

                甲的正桃花为：己、未、丑
                乙的正桃花为：戊、辰、戌
                丙的正桃花为：辛、酉
                丁的正桃花为：庚、申
                戊的正桃花为：癸、亥
                己的正桃花为：壬、子
                庚的正桃花为：乙、卯
                辛的正桃花为：甲、寅
                壬的正桃花为：丁、巳
                癸的正桃花为：丙、午
        */ {
            put("甲", Arrays.asList("己", "丑", "未"));
            put("乙", Arrays.asList("戊", "辰", "戌"));
            put("丙", Arrays.asList("辛", "酉"));
            put("丁", Arrays.asList("庚", "申"));
            put("戊", Arrays.asList("癸", "亥"));
            put("己", Arrays.asList("壬", "子"));
            put("庚", Arrays.asList("乙", "卯"));
            put("辛", Arrays.asList("甲", "寅"));
            put("壬", Arrays.asList("丁", "巳"));
            put("癸", Arrays.asList("丙", "午"));
        }
    };

    /**
     * 男日主偏财（日干为键）
     */
    public static final Map<String, List<String>> NAN_RI_ZHU_PIAN_TAO_HUA = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /*
            我克者为桃花

                甲的偏桃花为：戊、辰、戌
                乙的偏桃花为：己、未、丑
                丙的偏桃花为：庚、申
                丁的偏桃花为：辛、酉
                戊的偏桃花为：壬、子
                己的偏桃花为：癸、亥
                庚的偏桃花为：甲、寅
                辛的偏桃花为：乙、卯
                壬的偏桃花为：丙、午
                癸的偏桃花为：丁、巳
        */ {
            put("甲", Arrays.asList("戊", "辰", "戌"));
            put("乙", Arrays.asList("己", "丑", "未"));
            put("丙", Arrays.asList("庚", "申"));
            put("丁", Arrays.asList("辛", "酉"));
            put("戊", Arrays.asList("壬", "子"));
            put("己", Arrays.asList("癸", "亥"));
            put("庚", Arrays.asList("甲", "寅"));
            put("辛", Arrays.asList("乙", "卯"));
            put("壬", Arrays.asList("丙", "午"));
            put("癸", Arrays.asList("丁", "巳"));
        }
    };

    /**
     * 女日主正财（日干为键）
     */
    public static final Map<String, List<String>> NV_RI_ZHU_ZHENG_TAO_HUA = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /*
            克我者为桃花

                甲的正桃花为：辛、酉
                乙的正桃花为：庚、申
                丙的正桃花为：癸、亥
                丁的正桃花为：壬、子
                戊的正桃花为：乙、卯
                己的正桃花为：甲、寅
                庚的正桃花为：丁、午
                辛的正桃花为：丙、巳
                壬的正桃花为：己、丑、未
                癸的正桃花为：戊、辰、戌
        */ {
            put("甲", Arrays.asList("辛", "酉"));
            put("乙", Arrays.asList("庚", "申"));
            put("丙", Arrays.asList("癸", "亥"));
            put("丁", Arrays.asList("壬", "子"));
            put("戊", Arrays.asList("乙", "卯"));
            put("己", Arrays.asList("甲", "寅"));
            put("庚", Arrays.asList("丁", "午"));
            put("辛", Arrays.asList("丙", "巳"));
            put("壬", Arrays.asList("己", "丑", "未"));
            put("癸", Arrays.asList("戊", "辰", "戌"));
        }
    };

    /**
     * 女日主偏财（日干为键）
     */
    public static final Map<String, List<String>> NV_RI_ZHU_PIAN_TAO_HUA = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /*
            克我者为桃花

                甲的偏桃花为：庚、申
                乙的偏桃花为：辛、酉
                丙的偏桃花为：壬、子
                丁的偏桃花为：癸、亥
                戊的偏桃花为：甲、寅
                己的偏桃花为：乙、卯
                庚的偏桃花为：丙、巳
                辛的偏桃花为：丁、午
                壬的偏桃花为：戊、辰、戌
                癸的偏桃花为：己、丑、未
        */ {
            put("甲", Arrays.asList("庚", "申"));
            put("乙", Arrays.asList("辛", "酉"));
            put("丙", Arrays.asList("壬", "子"));
            put("丁", Arrays.asList("癸", "亥"));
            put("戊", Arrays.asList("甲", "寅"));
            put("己", Arrays.asList("乙", "卯"));
            put("庚", Arrays.asList("丙", "巳"));
            put("辛", Arrays.asList("丁", "午"));
            put("壬", Arrays.asList("戊", "辰", "戌"));
            put("癸", Arrays.asList("己", "丑", "未"));
        }
    };

    /**
     * 地支藏干（地支为键）
     */
    public static final Map<String, List<String>> DI_ZHI_CANG_GAN = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /*
                 《十二地支藏干口诀》

            子宫壬癸在其中，丑癸辛金己土同；
            寅宫甲木兼丙戊，卯宫甲乙木相逢，
            辰藏乙戊三分癸，己中庚金丙戊丛；
            午宫丙丁火己土，未宫乙己丁共宗；
            申位庚金壬水戊，酉宫庚辛金丰隆；
            戌宫辛金及丁戊，亥藏壬甲戊真踪。

            =========================================================================

            ★解释：
                1、 子：藏干为癸水。
                2、 丑：藏干为己土、癸水、辛金。（己土为本气，癸水为中气，辛金为余气）
                3、 寅：藏干为甲木、丙火、戊土。（甲木为本气，丙火为中气，戊土为余气）
                4、 卯：藏干为乙木。
                5、 辰：藏干为戊土、乙木、癸水。（戊土为本气，乙木为中气，癸水为余气）
                6、 巳：藏干为丙火、庚金、戊土。（丙火为本气，庚金为中气，戊土为余气）
                7、 午：藏干为丁火、己土。（丁火为本气，己土为中气）
                8、 未：藏干为己土、丁火、乙木。（己土为本气，丁火为中气，乙木为余气）
                9、 申：藏干为庚金、壬水、戊土。（庚金为本气，壬水为中气，戊土为余气）
                10、酉：藏干为辛金。
                11、戌：藏干为戊土、辛金、丁火。（戊土为本气，辛金为中气，丁火为余气）
                12、亥：藏干为壬水、甲木。（壬水为本气，甲木为中气）
        */

        {
            put("子", Collections.singletonList("癸"));
            put("丑", Arrays.asList("己", "癸", "辛"));
            put("寅", Arrays.asList("甲", "丙", "戊"));
            put("卯", Collections.singletonList("乙"));
            put("辰", Arrays.asList("戊", "乙", "癸"));
            put("巳", Arrays.asList("丙", "庚", "戊"));
            put("午", Arrays.asList("丁", "己"));
            put("未", Arrays.asList("己", "丁", "乙"));
            put("申", Arrays.asList("庚", "壬", "戊"));
            put("酉", Collections.singletonList("辛"));
            put("戌", Arrays.asList("戊", "辛", "丁"));
            put("亥", Arrays.asList("壬", "甲"));
        }
    };

    /**
     * 十二长生（日干+地支为键）
     */
    public static final Map<String, String> SHI_ER_ZHANG_SHENG = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        /*
            甲：长生在亥，沐浴在子，冠带在丑，临官在寅，帝旺在卯，衰于辰，病于巳，死于午，墓于未，绝于申，胎于酉，养于戌。
            乙：长生在午，沐浴在巳，冠带在辰，临官在卯，帝旺在寅，衰于丑，病于子，死于亥，墓于戌，绝于酉，胎于申，养于未。
            丙：长生在寅，沐浴在卯，冠带在辰，临官在巳，帝旺在午，衰于未，病于申，死于酉，墓于戌，绝于亥，胎于子，养于丑。
            丁：长生在酉，沐浴在申，冠带在未，临官在午，帝旺在巳，衰于辰，病于卯，死于寅，墓于丑，绝于子，胎于亥，养于戌。
            戊：长生在寅，沐浴在卯，冠带在辰，临官在巳，帝旺在午，衰于未，病于申，死于酉，墓于戌，绝于亥，胎于子，养于丑。
            己：长生在酉，沐浴在申，冠带在未，临官在午，帝旺在巳，衰于辰，病于卯，死于寅，墓于丑，绝于子，胎于亥，养于戌。
            庚：长生在巳，沐浴在午，冠带在未，临官在申，帝旺在酉，衰于戌，病于亥，死于子，墓于丑，绝于寅，胎于卯，养于辰。
            辛：长生在子，沐浴在亥，冠带在戌，临官在酉，帝旺在申，衰于未，病于午，死于巳，墓于辰，绝于卯，胎于寅，养于丑。
            壬：长生在申，沐浴在酉，冠带在戌，临官在亥，帝旺在子，衰于丑，病于寅，死于卯，墓于辰，绝于巳，胎于午，养于未。
            癸：长生在卯，沐浴在寅，冠带在丑，临官在子，帝旺在亥，衰于戌，病于酉，死于申，墓于未，绝于午，胎于巳，养于辰。
        */

        {
            put("甲子", "沐浴");
            put("甲丑", "冠带");
            put("甲寅", "临官");
            put("甲卯", "帝旺");
            put("甲辰", "衰");
            put("甲巳", "病");
            put("甲午", "死");
            put("甲未", "墓");
            put("甲申", "绝");
            put("甲酉", "胎");
            put("甲戌", "养");
            put("甲亥", "长生");

            put("乙子", "病");
            put("乙丑", "衰");
            put("乙寅", "帝旺");
            put("乙卯", "临官");
            put("乙辰", "冠带");
            put("乙巳", "沐浴");
            put("乙午", "长生");
            put("乙未", "养");
            put("乙申", "胎");
            put("乙酉", "绝");
            put("乙戌", "墓");
            put("乙亥", "死");

            put("丙子", "胎");
            put("丙丑", "养");
            put("丙寅", "长生");
            put("丙卯", "沐浴");
            put("丙辰", "冠带");
            put("丙巳", "临官");
            put("丙午", "帝旺");
            put("丙未", "衰");
            put("丙申", "病");
            put("丙酉", "死");
            put("丙戌", "墓");
            put("丙亥", "绝");

            put("丁子", "绝");
            put("丁丑", "墓");
            put("丁寅", "死");
            put("丁卯", "病");
            put("丁辰", "衰");
            put("丁巳", "帝旺");
            put("丁午", "临官");
            put("丁未", "冠带");
            put("丁申", "沐浴");
            put("丁酉", "长生");
            put("丁戌", "养");
            put("丁亥", "胎");

            put("戊子", "胎");
            put("戊丑", "养");
            put("戊寅", "长生");
            put("戊卯", "沐浴");
            put("戊辰", "冠带");
            put("戊巳", "临官");
            put("戊午", "帝旺");
            put("戊未", "衰");
            put("戊申", "病");
            put("戊酉", "死");
            put("戊戌", "墓");
            put("戊亥", "绝");

            put("己子", "绝");
            put("己丑", "墓");
            put("己寅", "死");
            put("己卯", "病");
            put("己辰", "衰");
            put("己巳", "帝旺");
            put("己午", "临官");
            put("己未", "冠带");
            put("己申", "沐浴");
            put("己酉", "长生");
            put("己戌", "养");
            put("己亥", "胎");

            put("庚子", "死");
            put("庚丑", "墓");
            put("庚寅", "绝");
            put("庚卯", "胎");
            put("庚辰", "养");
            put("庚巳", "长生");
            put("庚午", "沐浴");
            put("庚未", "冠带");
            put("庚申", "临官");
            put("庚酉", "帝旺");
            put("庚戌", "衰");
            put("庚亥", "病");

            put("辛子", "长生");
            put("辛丑", "养");
            put("辛寅", "胎");
            put("辛卯", "绝");
            put("辛辰", "墓");
            put("辛巳", "死");
            put("辛午", "病");
            put("辛未", "衰");
            put("辛申", "帝旺");
            put("辛酉", "临官");
            put("辛戌", "冠带");
            put("辛亥", "沐浴");

            put("壬子", "帝旺");
            put("壬丑", "衰");
            put("壬寅", "病");
            put("壬卯", "死");
            put("壬辰", "墓");
            put("壬巳", "绝");
            put("壬午", "胎");
            put("壬未", "养");
            put("壬申", "长生");
            put("壬酉", "沐浴");
            put("壬戌", "冠带");
            put("壬亥", "临官");

            put("癸子", "临官");
            put("癸丑", "冠带");
            put("癸寅", "沐浴");
            put("癸卯", "长生");
            put("癸辰", "养");
            put("癸巳", "胎");
            put("癸午", "绝");
            put("癸未", "墓");
            put("癸申", "死");
            put("癸酉", "病");
            put("癸戌", "衰");
            put("癸亥", "帝旺");
        }
    };

    /**
     * 空亡（干支为键）
     */
    public static final Map<String, String> KONG_WANG = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        /*
            甲子旬：甲子、乙丑、丙寅、丁卯、戊辰、己巳、庚午、辛未、壬申、癸酉，戌亥空。
            甲戌旬：甲戌、乙亥、丙子、丁丑、戊寅、己卯、庚辰、辛巳、壬午、癸未，申酉空。
            甲申旬：甲申、乙酉、丙戌、丁亥、戊子、己丑、庚寅、辛卯、壬辰、癸巳，午未空。
            甲午旬：甲午、乙未、丙申、丁酉、戊戌、己亥、庚子、辛丑、壬寅、癸卯，辰巳空。
            甲辰旬：甲辰、乙巳、丙午、丁未、戊申、己酉、庚戌、辛亥、壬子、癸丑，寅卯空。
            甲寅旬：甲寅、乙卯、丙辰、丁巳、戊午、己未、庚申、辛酉、壬戌、癸亥，子丑空。
        */

        {
            put("甲子", "戌亥");
            put("乙丑", "戌亥");
            put("丙寅", "戌亥");
            put("丁卯", "戌亥");
            put("戊辰", "戌亥");
            put("己巳", "戌亥");
            put("庚午", "戌亥");
            put("辛未", "戌亥");
            put("壬申", "戌亥");
            put("癸酉", "戌亥");

            put("甲戌", "申酉");
            put("乙亥", "申酉");
            put("丙子", "申酉");
            put("丁丑", "申酉");
            put("戊寅", "申酉");
            put("己卯", "申酉");
            put("庚辰", "申酉");
            put("辛巳", "申酉");
            put("壬午", "申酉");
            put("癸未", "申酉");

            put("甲申", "午未");
            put("乙酉", "午未");
            put("丙戌", "午未");
            put("丁亥", "午未");
            put("戊子", "午未");
            put("己丑", "午未");
            put("庚寅", "午未");
            put("辛卯", "午未");
            put("壬辰", "午未");
            put("癸巳", "午未");

            put("甲午", "辰巳");
            put("乙未", "辰巳");
            put("丙申", "辰巳");
            put("丁酉", "辰巳");
            put("戊戌", "辰巳");
            put("己亥", "辰巳");
            put("庚子", "辰巳");
            put("辛丑", "辰巳");
            put("壬寅", "辰巳");
            put("癸卯", "辰巳");

            put("甲辰", "寅卯");
            put("乙巳", "寅卯");
            put("丙午", "寅卯");
            put("丁未", "寅卯");
            put("戊申", "寅卯");
            put("己酉", "寅卯");
            put("庚戌", "寅卯");
            put("辛亥", "寅卯");
            put("壬子", "寅卯");
            put("癸丑", "寅卯");

            put("甲寅", "子丑");
            put("乙卯", "子丑");
            put("丙辰", "子丑");
            put("丁巳", "子丑");
            put("戊午", "子丑");
            put("己未", "子丑");
            put("庚申", "子丑");
            put("辛酉", "子丑");
            put("壬戌", "子丑");
            put("癸亥", "子丑");
        }
    };

    /**
     * 纳音（干支为键）
     */
    public static final Map<String, String> NA_YIN = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        /*
            甲子、乙丑：海中金
            丙寅、丁卯：炉中火
            戊辰、己巳：大林木
            庚午、辛未：路旁土
            壬申、癸酉：剑锋金
            甲戌、乙亥：山头火
            丙子、丁丑：涧下水
            戊寅、己卯：城头土
            庚辰、辛巳：白蜡金
            壬午、癸未：杨柳木
            甲申、乙酉：泉中水
            丙戌、丁亥：屋上土
            戊子、己丑：霹雳火
            庚寅、辛卯：松柏木
            壬辰、癸巳：长流水
            甲午、乙未：沙中金
            丙申、丁酉：山下火
            戊戌、己亥：平地木
            庚子、辛丑：壁上土
            壬寅、癸卯：金箔金
            甲辰、乙巳：覆灯火
            丙午、丁未：天河水
            戊申、己酉：大驿土
            庚戌、辛亥：钗钏金
            壬子、癸丑：桑柘木
            甲寅、乙卯：大溪水
            丙辰、丁巳：沙中土
            戊午、己未：天上火
            庚申、辛酉：石榴木
            壬戌、癸亥：大海水
        */

        {
            put("甲子", "海中金");
            put("丙寅", "炉中火");
            put("戊辰", "大林木");
            put("庚午", "路旁土");
            put("壬申", "剑锋金");
            put("甲戌", "山头火");
            put("丙子", "涧下水");
            put("戊寅", "城头土");
            put("庚辰", "白蜡金");
            put("壬午", "杨柳木");
            put("甲申", "泉中水");
            put("丙戌", "屋上土");
            put("戊子", "霹雳火");
            put("庚寅", "松柏木");
            put("壬辰", "长流水");
            put("甲午", "沙中金");
            put("丙申", "山下火");
            put("戊戌", "平地木");
            put("庚子", "壁上土");
            put("壬寅", "金箔金");
            put("甲辰", "覆灯火");
            put("丙午", "天河水");
            put("戊申", "大驿土");
            put("庚戌", "钗钏金");
            put("壬子", "桑柘木");
            put("甲寅", "大溪水");
            put("丙辰", "沙中土");
            put("戊午", "天上火");
            put("庚申", "石榴木");
            put("壬戌", "大海水");

            put("乙丑", "海中金");
            put("丁卯", "炉中火");
            put("己巳", "大林木");
            put("辛未", "路旁土");
            put("癸酉", "剑锋金");
            put("乙亥", "山头火");
            put("丁丑", "涧下水");
            put("己卯", "城头土");
            put("辛巳", "白蜡金");
            put("癸未", "杨柳木");
            put("乙酉", "泉中水");
            put("丁亥", "屋上土");
            put("己丑", "霹雳火");
            put("辛卯", "松柏木");
            put("癸巳", "长流水");
            put("乙未", "沙中金");
            put("丁酉", "山下火");
            put("己亥", "平地木");
            put("辛丑", "壁上土");
            put("癸卯", "金箔金");
            put("乙巳", "覆灯火");
            put("丁未", "天河水");
            put("己酉", "大驿土");
            put("辛亥", "钗钏金");
            put("癸丑", "桑柘木");
            put("乙卯", "大溪水");
            put("丁巳", "沙中土");
            put("己未", "天上火");
            put("辛酉", "石榴木");
            put("癸亥", "大海水");
        }
    };

    /**
     * 十神，用于大运流年（日干+其他干支为键）
     */
    public static final Map<String, String> SHI_SHEN_YUN_NIAN_MAP = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        /*
            十神的生克关系：

                1、生我者为印缓（五行阴阳属性相同则为：枭神(偏印)，五行阴阳属性不同则为：正印）
                2、我生者为子孙（五行阴阳属性相同则为：食神，五行阴阳属性不同则为：伤官）
                3、克我者为官鬼（五行阴阳属性相同则为：七杀(偏官)，五行阴阳属性不同则为：正官）
                4、我克者为妻财（五行阴阳属性相同则为：才财(偏财)，五行阴阳属性不同则为：正财）
                5、同我者为兄弟（五行阴阳属性相同则为：比肩，五行阴阳属性不同则为：劫财）
         */ {                    // 我+者
            put("甲甲", "比肩"); // 甲+甲（阳木+阳木）：比肩
            put("甲乙", "劫财"); // 甲+乙（阳木+阴木）：劫财
            put("甲丙", "食神"); // 甲+丙（阳木+阳火）：食神
            put("甲丁", "伤官"); // 甲+丁（阳木+阴火）：伤官
            put("甲戊", "才财"); // 甲+戊（阳木+阳土）：才财(偏财)
            put("甲己", "正财"); // 甲+己（阳木+阴土）：正财
            put("甲庚", "七杀"); // 甲+庚（阳木+阳金）：七杀(偏官)
            put("甲辛", "正官"); // 甲+辛（阳木+阴金）：正官
            put("甲壬", "枭神"); // 甲+壬（阳木+阳水）：枭神(偏印)
            put("甲癸", "正印"); // 甲+癸（阳木+阴水）：正印
            put("甲子", "枭神"); // 甲+子（阳木+阳水）：枭神(偏印)
            put("甲丑", "正财"); // 甲+丑（阳木+阴土）：正财
            put("甲寅", "比肩"); // 甲+寅（阳木+阳木）：比肩
            put("甲卯", "劫财"); // 甲+卯（阳木+阴木）：劫财
            put("甲辰", "才财"); // 甲+辰（阳木+阳土）：才财(偏财)
            put("甲巳", "伤官"); // 甲+巳（阳木+阴火）：伤官
            put("甲午", "食神"); // 甲+午（阳木+阳火）：食神
            put("甲未", "正财"); // 甲+未（阳木+阴土）：正财
            put("甲申", "七杀"); // 甲+申（阳木+阳金）：七杀(偏官)
            put("甲酉", "正官"); // 甲+酉（阳木+阴金）：正官
            put("甲戌", "才财"); // 甲+戌（阳木+阳土）：才财(偏财)
            put("甲亥", "正印"); // 甲+亥（阳木+阴水）：正印
            put("乙甲", "劫财"); // 乙+甲（阴木+阳木）：劫财
            put("乙乙", "比肩"); // 乙+乙（阴木+阴木）：比肩
            put("乙丙", "伤官"); // 乙+丙（阴木+阳火）：伤官
            put("乙丁", "食神"); // 乙+丁（阴木+阴火）：食神
            put("乙戊", "正财"); // 乙+戊（阴木+阳土）：正财
            put("乙己", "才财"); // 乙+己（阴木+阴土）：才财(偏财)
            put("乙庚", "正官"); // 乙+庚（阴木+阳金）：正官
            put("乙辛", "七杀"); // 乙+辛（阴木+阴金）：七杀(偏官)
            put("乙壬", "正印"); // 乙+壬（阴木+阳水）：正印
            put("乙癸", "枭神"); // 乙+癸（阴木+阴水）：枭神(偏印)
            put("乙子", "正印"); // 乙+子（阴木+阳水）：正印
            put("乙丑", "才财"); // 乙+丑（阴木+阴土）：才财(偏财)
            put("乙寅", "劫财"); // 乙+寅（阴木+阳木）：劫财
            put("乙卯", "比肩"); // 乙+卯（阴木+阴木）：比肩
            put("乙辰", "正财"); // 乙+辰（阴木+阳土）：正财
            put("乙巳", "食神"); // 乙+巳（阴木+阴火）：食神
            put("乙午", "伤官"); // 乙+午（阴木+阳火）：伤官
            put("乙未", "才财"); // 乙+未（阴木+阴土）：才财(偏财)
            put("乙申", "正官"); // 乙+申（阴木+阳金）：正官
            put("乙酉", "七杀"); // 乙+酉（阴木+阴金）：七杀(偏官)
            put("乙戌", "正财"); // 乙+戌（阴木+阳土）：正财
            put("乙亥", "枭神"); // 乙+亥（阴木+阴水）：枭神(偏印)
            put("丙甲", "枭神"); // 丙+甲（阳火+阳木）：枭神(偏印)
            put("丙乙", "正印"); // 丙+乙（阳火+阴木）：正印
            put("丙丙", "比肩"); // 丙+丙（阳火+阳火）：比肩
            put("丙丁", "劫财"); // 丙+丁（阳火+阴火）：劫财
            put("丙戊", "食神"); // 丙+戊（阳火+阳土）：食神
            put("丙己", "伤官"); // 丙+己（阳火+阴土）：伤官
            put("丙庚", "才财"); // 丙+庚（阳火+阳金）：才财(偏财)
            put("丙辛", "正财"); // 丙+辛（阳火+阴金）：正财
            put("丙壬", "七杀"); // 丙+壬（阳火+阳水）：七杀(偏官)
            put("丙癸", "正官"); // 丙+癸（阳火+阴水）：正官
            put("丙子", "七杀"); // 丙+子（阳火+阳水）：七杀(偏官)
            put("丙丑", "伤官"); // 丙+丑（阳火+阴土）：伤官
            put("丙寅", "枭神"); // 丙+寅（阳火+阳木）：枭神(偏印)
            put("丙卯", "正印"); // 丙+卯（阳火+阴木）：正印
            put("丙辰", "食神"); // 丙+辰（阳火+阳土）：食神
            put("丙巳", "劫财"); // 丙+巳（阳火+阴火）：劫财
            put("丙午", "比肩"); // 丙+午（阳火+阳火）：比肩
            put("丙未", "伤官"); // 丙+未（阳火+阴土）：伤官
            put("丙申", "才财"); // 丙+申（阳火+阳金）：才财(偏财)
            put("丙酉", "正财"); // 丙+酉（阳火+阴金）：正财
            put("丙戌", "食神"); // 丙+戌（阳火+阳土）：食神
            put("丙亥", "正官"); // 丙+亥（阳火+阴水）：正官
            put("丁甲", "正印"); // 丁+甲（阴火+阳木）：正印
            put("丁乙", "枭神"); // 丁+乙（阴火+阴木）：枭神(偏印)
            put("丁丙", "劫财"); // 丁+丙（阴火+阳火）：劫财
            put("丁丁", "比肩"); // 丁+丁（阴火+阴火）：比肩
            put("丁戊", "伤官"); // 丁+戊（阴火+阳土）：伤官
            put("丁己", "食神"); // 丁+己（阴火+阴土）：食神
            put("丁庚", "正财"); // 丁+庚（阴火+阳金）：正财
            put("丁辛", "才财"); // 丁+辛（阴火+阴金）：才财(偏财)
            put("丁壬", "正官"); // 丁+壬（阴火+阳水）：正官
            put("丁癸", "七杀"); // 丁+癸（阴火+阴水）：七杀(偏官)
            put("丁子", "正官"); // 丁+子（阴火+阳水）：正官
            put("丁丑", "食神"); // 丁+丑（阴火+阴土）：食神
            put("丁寅", "正印"); // 丁+寅（阴火+阳木）：正印
            put("丁卯", "枭神"); // 丁+卯（阴火+阴木）：枭神(偏印)
            put("丁辰", "伤官"); // 丁+辰（阴火+阳土）：伤官
            put("丁巳", "比肩"); // 丁+巳（阴火+阴火）：比肩
            put("丁午", "劫财"); // 丁+午（阴火+阳火）：劫财
            put("丁未", "食神"); // 丁+未（阴火+阴土）：食神
            put("丁申", "正财"); // 丁+申（阴火+阳金）：正财
            put("丁酉", "才财"); // 丁+酉（阴火+阴金）：才财(偏财)
            put("丁戌", "伤官"); // 丁+戌（阴火+阳土）：伤官
            put("丁亥", "七杀"); // 丁+亥（阴火+阴水）：七杀(偏官)
            put("戊甲", "七杀"); // 戊+甲（阳土+阳木）：七杀(偏官)
            put("戊乙", "正官"); // 戊+乙（阳土+阴木）：正官
            put("戊丙", "枭神"); // 戊+丙（阳土+阳火）：枭神(偏印)
            put("戊丁", "正印"); // 戊+丁（阳土+阴火）：正印
            put("戊戊", "比肩"); // 戊+戊（阳土+阳土）：比肩
            put("戊己", "劫财"); // 戊+己（阳土+阴土）：劫财
            put("戊庚", "食神"); // 戊+庚（阳土+阳金）：食神
            put("戊辛", "伤官"); // 戊+辛（阳土+阴金）：伤官
            put("戊壬", "才财"); // 戊+壬（阳土+阳水）：才财(偏财)
            put("戊癸", "正财"); // 戊+癸（阳土+阴水）：正财
            put("戊子", "才财"); // 戊+子（阳土+阳水）：才财(偏财)
            put("戊丑", "劫财"); // 戊+丑（阳土+阴土）：劫财
            put("戊寅", "七杀"); // 戊+寅（阳土+阳木）：七杀(偏官)
            put("戊卯", "正官"); // 戊+卯（阳土+阴木）：正官
            put("戊辰", "比肩"); // 戊+辰（阳土+阳土）：比肩
            put("戊巳", "正印"); // 戊+巳（阳土+阴火）：正印
            put("戊午", "枭神"); // 戊+午（阳土+阳火）：枭神(偏印)
            put("戊未", "劫财"); // 戊+未（阳土+阴土）：劫财
            put("戊申", "食神"); // 戊+申（阳土+阳金）：食神
            put("戊酉", "伤官"); // 戊+酉（阳土+阴金）：伤官
            put("戊戌", "比肩"); // 戊+戌（阳土+阳土）：比肩
            put("戊亥", "正财"); // 戊+亥（阳土+阴水）：正财
            put("己甲", "正官"); // 己+甲（阴土+阳木）：正官
            put("己乙", "七杀"); // 己+乙（阴土+阴木）：七杀(偏官)
            put("己丙", "正印"); // 己+丙（阴土+阳火）：正印
            put("己丁", "枭神"); // 己+丁（阴土+阴火）：枭神(偏印)
            put("己戊", "劫财"); // 己+戊（阴土+阳土）：劫财
            put("己己", "比肩"); // 己+己（阴土+阴土）：比肩
            put("己庚", "伤官"); // 己+庚（阴土+阳金）：伤官
            put("己辛", "食神"); // 己+辛（阴土+阴金）：食神
            put("己壬", "正财"); // 己+壬（阴土+阳水）：正财
            put("己癸", "才财"); // 己+癸（阴土+阴水）：才财(偏财)
            put("己子", "正财"); // 己+子（阴土+阳水）：正财
            put("己丑", "比肩"); // 己+丑（阴土+阴土）：比肩
            put("己寅", "正官"); // 己+寅（阴土+阳木）：正官
            put("己卯", "七杀"); // 己+卯（阴土+阴木）：七杀(偏官)
            put("己辰", "劫财"); // 己+辰（阴土+阳土）：劫财
            put("己巳", "枭神"); // 己+巳（阴土+阴火）：枭神(偏印)
            put("己午", "正印"); // 己+午（阴土+阳火）：正印
            put("己未", "比肩"); // 己+未（阴土+阴土）：比肩
            put("己申", "伤官"); // 己+申（阴土+阳金）：伤官
            put("己酉", "食神"); // 己+酉（阴土+阴金）：食神
            put("己戌", "劫财"); // 己+戌（阴土+阳土）：劫财
            put("己亥", "才财"); // 己+亥（阴土+阴水）：才财(偏财)
            put("庚甲", "才财"); // 庚+甲（阳金+阳木）：才财(偏财)
            put("庚乙", "正财"); // 庚+乙（阳金+阴木）：正财
            put("庚丙", "七杀"); // 庚+丙（阳金+阳火）：七杀(偏官)
            put("庚丁", "正官"); // 庚+丁（阳金+阴火）：正官
            put("庚戊", "枭神"); // 庚+戊（阳金+阳土）：枭神(偏印)
            put("庚己", "正印"); // 庚+己（阳金+阴土）：正印
            put("庚庚", "比肩"); // 庚+庚（阳金+阳金）：比肩
            put("庚辛", "劫财"); // 庚+辛（阳金+阴金）：劫财
            put("庚壬", "食神"); // 庚+壬（阳金+阳水）：食神
            put("庚癸", "伤官"); // 庚+癸（阳金+阴水）：伤官
            put("庚子", "食神"); // 庚+子（阳金+阳水）：食神
            put("庚丑", "正印"); // 庚+丑（阳金+阴土）：正印
            put("庚寅", "才财"); // 庚+寅（阳金+阳木）：才财(偏财)
            put("庚卯", "正财"); // 庚+卯（阳金+阴木）：正财
            put("庚辰", "枭神"); // 庚+辰（阳金+阳土）：枭神(偏印)
            put("庚巳", "正官"); // 庚+巳（阳金+阴火）：正官
            put("庚午", "七杀"); // 庚+午（阳金+阳火）：七杀(偏官)
            put("庚未", "正印"); // 庚+未（阳金+阴土）：正印
            put("庚申", "比肩"); // 庚+申（阳金+阳金）：比肩
            put("庚酉", "劫财"); // 庚+酉（阳金+阴金）：劫财
            put("庚戌", "枭神"); // 庚+戌（阳金+阳土）：枭神(偏印)
            put("庚亥", "伤官"); // 庚+亥（阳金+阴水）：伤官
            put("辛甲", "正财"); // 辛+甲（阴金+阳木）：正财
            put("辛乙", "才财"); // 辛+乙（阴金+阴木）：才财(偏财)
            put("辛丙", "正官"); // 辛+丙（阴金+阳火）：正官
            put("辛丁", "七杀"); // 辛+丁（阴金+阴火）：七杀(偏官)
            put("辛戊", "正印"); // 辛+戊（阴金+阳土）：正印
            put("辛己", "枭神"); // 辛+己（阴金+阴土）：枭神(偏印)
            put("辛庚", "劫财"); // 辛+庚（阴金+阳金）：劫财
            put("辛辛", "比肩"); // 辛+辛（阴金+阴金）：比肩
            put("辛壬", "伤官"); // 辛+壬（阴金+阳水）：伤官
            put("辛癸", "食神"); // 辛+癸（阴金+阴水）：食神
            put("辛子", "伤官"); // 辛+子（阴金+阳水）：伤官
            put("辛丑", "枭神"); // 辛+丑（阴金+阴土）：枭神(偏印)
            put("辛寅", "正财"); // 辛+寅（阴金+阳木）：正财
            put("辛卯", "才财"); // 辛+卯（阴金+阴木）：才财(偏财)
            put("辛辰", "正印"); // 辛+辰（阴金+阳土）：正印
            put("辛巳", "七杀"); // 辛+巳（阴金+阴火）：七杀(偏官)
            put("辛午", "正官"); // 辛+午（阴金+阳火）：正官
            put("辛未", "枭神"); // 辛+未（阴金+阴土）：枭神(偏印)
            put("辛申", "劫财"); // 辛+申（阴金+阳金）：劫财
            put("辛酉", "比肩"); // 辛+酉（阴金+阴金）：比肩
            put("辛戌", "正印"); // 辛+戌（阴金+阳土）：正印
            put("辛亥", "食神"); // 辛+亥（阴金+阴水）：食神
            put("壬甲", "食神"); // 壬+甲（阳水+阳木）：食神
            put("壬乙", "伤官"); // 壬+乙（阳水+阴木）：伤官
            put("壬丙", "才财"); // 壬+丙（阳水+阳火）：才财(偏财)
            put("壬丁", "正财"); // 壬+丁（阳水+阴火）：正财
            put("壬戊", "七杀"); // 壬+戊（阳水+阳土）：七杀(偏官)
            put("壬己", "正官"); // 壬+己（阳水+阴土）：正官
            put("壬庚", "枭神"); // 壬+庚（阳水+阳金）：枭神(偏印)
            put("壬辛", "正印"); // 壬+辛（阳水+阴金）：正印
            put("壬壬", "比肩"); // 壬+壬（阳水+阳水）：比肩
            put("壬癸", "劫财"); // 壬+癸（阳水+阴水）：劫财
            put("壬子", "比肩"); // 壬+子（阳水+阳水）：比肩
            put("壬丑", "正官"); // 壬+丑（阳水+阴土）：正官
            put("壬寅", "食神"); // 壬+寅（阳水+阳木）：食神
            put("壬卯", "伤官"); // 壬+卯（阳水+阴木）：伤官
            put("壬辰", "七杀"); // 壬+辰（阳水+阳土）：七杀(偏官)
            put("壬巳", "正财"); // 壬+巳（阳水+阴火）：正财
            put("壬午", "才财"); // 壬+午（阳水+阳火）：才财(偏财)
            put("壬未", "正官"); // 壬+未（阳水+阴土）：正官
            put("壬申", "枭神"); // 壬+申（阳水+阳金）：枭神(偏印)
            put("壬酉", "正印"); // 壬+酉（阳水+阴金）：正印
            put("壬戌", "七杀"); // 壬+戌（阳水+阳土）：七杀(偏官)
            put("壬亥", "劫财"); // 壬+亥（阳水+阴水）：劫财
            put("癸甲", "伤官"); // 癸+甲（阴水+阳木）：伤官
            put("癸乙", "食神"); // 癸+乙（阴水+阴木）：食神
            put("癸丙", "正财"); // 癸+丙（阴水+阳火）：正财
            put("癸丁", "才财"); // 癸+丁（阴水+阴火）：才财(偏财)
            put("癸戊", "正官"); // 癸+戊（阴水+阳土）：正官
            put("癸己", "七杀"); // 癸+己（阴水+阴土）：七杀(偏官)
            put("癸庚", "正印"); // 癸+庚（阴水+阳金）：正印
            put("癸辛", "枭神"); // 癸+辛（阴水+阴金）：枭神(偏印)
            put("癸壬", "劫财"); // 癸+壬（阴水+阳水）：劫财
            put("癸癸", "比肩"); // 癸+癸（阴水+阴水）：比肩
            put("癸子", "劫财"); // 癸+子（阴水+阳水）：劫财
            put("癸丑", "七杀"); // 癸+丑（阴水+阴土）：七杀(偏官)
            put("癸寅", "伤官"); // 癸+寅（阴水+阳木）：伤官
            put("癸卯", "食神"); // 癸+卯（阴水+阴木）：食神
            put("癸辰", "正官"); // 癸+辰（阴水+阳土）：正官
            put("癸巳", "才财"); // 癸+巳（阴水+阴火）：才财(偏财)
            put("癸午", "正财"); // 癸+午（阴水+阳火）：正财
            put("癸未", "七杀"); // 癸+未（阴水+阴土）：七杀(偏官)
            put("癸申", "正印"); // 癸+申（阴水+阳金）：正印
            put("癸酉", "枭神"); // 癸+酉（阴水+阴金）：枭神(偏印)
            put("癸戌", "正官"); // 癸+戌（阴水+阳土）：正官
            put("癸亥", "比肩"); // 癸+亥（阴水+阴水）：比肩
        }
    };

    /**
     * 十神，用于命盘（日干+其他干支为键）
     */
    public static final Map<String, String> SHI_SHEN_MING_PAN_MAP = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        /*
            十神的生克关系：

                1、生我者为印缓（五行阴阳属性相同则为：枭神(偏印)，五行阴阳属性不同则为：正印）
                2、我生者为子孙（五行阴阳属性相同则为：食神，五行阴阳属性不同则为：伤官）
                3、克我者为官鬼（五行阴阳属性相同则为：七杀(偏官)，五行阴阳属性不同则为：正官）
                4、我克者为妻财（五行阴阳属性相同则为：偏财(才财)，五行阴阳属性不同则为：正财）
                5、同我者为兄弟（五行阴阳属性相同则为：比肩，五行阴阳属性不同则为：劫财）
         */ {                    // 我+者
            put("甲甲", "比肩"); // 甲+甲（阳木+阳木）：比肩
            put("甲乙", "劫财"); // 甲+乙（阳木+阴木）：劫财
            put("甲丙", "食神"); // 甲+丙（阳木+阳火）：食神
            put("甲丁", "伤官"); // 甲+丁（阳木+阴火）：伤官
            put("甲戊", "偏财"); // 甲+戊（阳木+阳土）：偏财(才财)
            put("甲己", "正财"); // 甲+己（阳木+阴土）：正财
            put("甲庚", "七杀"); // 甲+庚（阳木+阳金）：七杀(偏官)
            put("甲辛", "正官"); // 甲+辛（阳木+阴金）：正官
            put("甲壬", "偏印"); // 甲+壬（阳木+阳水）：枭神(偏印)
            put("甲癸", "正印"); // 甲+癸（阳木+阴水）：正印
            put("甲子", "偏印"); // 甲+子（阳木+阳水）：枭神(偏印)
            put("甲丑", "正财"); // 甲+丑（阳木+阴土）：正财
            put("甲寅", "比肩"); // 甲+寅（阳木+阳木）：比肩
            put("甲卯", "劫财"); // 甲+卯（阳木+阴木）：劫财
            put("甲辰", "偏财"); // 甲+辰（阳木+阳土）：偏财(才财)
            put("甲巳", "伤官"); // 甲+巳（阳木+阴火）：伤官
            put("甲午", "食神"); // 甲+午（阳木+阳火）：食神
            put("甲未", "正财"); // 甲+未（阳木+阴土）：正财
            put("甲申", "七杀"); // 甲+申（阳木+阳金）：七杀(偏官)
            put("甲酉", "正官"); // 甲+酉（阳木+阴金）：正官
            put("甲戌", "偏财"); // 甲+戌（阳木+阳土）：偏财(才财)
            put("甲亥", "正印"); // 甲+亥（阳木+阴水）：正印
            put("乙甲", "劫财"); // 乙+甲（阴木+阳木）：劫财
            put("乙乙", "比肩"); // 乙+乙（阴木+阴木）：比肩
            put("乙丙", "伤官"); // 乙+丙（阴木+阳火）：伤官
            put("乙丁", "食神"); // 乙+丁（阴木+阴火）：食神
            put("乙戊", "正财"); // 乙+戊（阴木+阳土）：正财
            put("乙己", "偏财"); // 乙+己（阴木+阴土）：偏财(才财)
            put("乙庚", "正官"); // 乙+庚（阴木+阳金）：正官
            put("乙辛", "七杀"); // 乙+辛（阴木+阴金）：七杀(偏官)
            put("乙壬", "正印"); // 乙+壬（阴木+阳水）：正印
            put("乙癸", "偏印"); // 乙+癸（阴木+阴水）：枭神(偏印)
            put("乙子", "正印"); // 乙+子（阴木+阳水）：正印
            put("乙丑", "偏财"); // 乙+丑（阴木+阴土）：偏财(才财)
            put("乙寅", "劫财"); // 乙+寅（阴木+阳木）：劫财
            put("乙卯", "比肩"); // 乙+卯（阴木+阴木）：比肩
            put("乙辰", "正财"); // 乙+辰（阴木+阳土）：正财
            put("乙巳", "食神"); // 乙+巳（阴木+阴火）：食神
            put("乙午", "伤官"); // 乙+午（阴木+阳火）：伤官
            put("乙未", "偏财"); // 乙+未（阴木+阴土）：偏财(才财)
            put("乙申", "正官"); // 乙+申（阴木+阳金）：正官
            put("乙酉", "七杀"); // 乙+酉（阴木+阴金）：七杀(偏官)
            put("乙戌", "正财"); // 乙+戌（阴木+阳土）：正财
            put("乙亥", "偏印"); // 乙+亥（阴木+阴水）：枭神(偏印)
            put("丙甲", "偏印"); // 丙+甲（阳火+阳木）：枭神(偏印)
            put("丙乙", "正印"); // 丙+乙（阳火+阴木）：正印
            put("丙丙", "比肩"); // 丙+丙（阳火+阳火）：比肩
            put("丙丁", "劫财"); // 丙+丁（阳火+阴火）：劫财
            put("丙戊", "食神"); // 丙+戊（阳火+阳土）：食神
            put("丙己", "伤官"); // 丙+己（阳火+阴土）：伤官
            put("丙庚", "偏财"); // 丙+庚（阳火+阳金）：偏财(才财)
            put("丙辛", "正财"); // 丙+辛（阳火+阴金）：正财
            put("丙壬", "七杀"); // 丙+壬（阳火+阳水）：七杀(偏官)
            put("丙癸", "正官"); // 丙+癸（阳火+阴水）：正官
            put("丙子", "七杀"); // 丙+子（阳火+阳水）：七杀(偏官)
            put("丙丑", "伤官"); // 丙+丑（阳火+阴土）：伤官
            put("丙寅", "偏印"); // 丙+寅（阳火+阳木）：枭神(偏印)
            put("丙卯", "正印"); // 丙+卯（阳火+阴木）：正印
            put("丙辰", "食神"); // 丙+辰（阳火+阳土）：食神
            put("丙巳", "劫财"); // 丙+巳（阳火+阴火）：劫财
            put("丙午", "比肩"); // 丙+午（阳火+阳火）：比肩
            put("丙未", "伤官"); // 丙+未（阳火+阴土）：伤官
            put("丙申", "偏财"); // 丙+申（阳火+阳金）：偏财(才财)
            put("丙酉", "正财"); // 丙+酉（阳火+阴金）：正财
            put("丙戌", "食神"); // 丙+戌（阳火+阳土）：食神
            put("丙亥", "正官"); // 丙+亥（阳火+阴水）：正官
            put("丁甲", "正印"); // 丁+甲（阴火+阳木）：正印
            put("丁乙", "偏印"); // 丁+乙（阴火+阴木）：枭神(偏印)
            put("丁丙", "劫财"); // 丁+丙（阴火+阳火）：劫财
            put("丁丁", "比肩"); // 丁+丁（阴火+阴火）：比肩
            put("丁戊", "伤官"); // 丁+戊（阴火+阳土）：伤官
            put("丁己", "食神"); // 丁+己（阴火+阴土）：食神
            put("丁庚", "正财"); // 丁+庚（阴火+阳金）：正财
            put("丁辛", "偏财"); // 丁+辛（阴火+阴金）：偏财(才财)
            put("丁壬", "正官"); // 丁+壬（阴火+阳水）：正官
            put("丁癸", "七杀"); // 丁+癸（阴火+阴水）：七杀(偏官)
            put("丁子", "正官"); // 丁+子（阴火+阳水）：正官
            put("丁丑", "食神"); // 丁+丑（阴火+阴土）：食神
            put("丁寅", "正印"); // 丁+寅（阴火+阳木）：正印
            put("丁卯", "偏印"); // 丁+卯（阴火+阴木）：枭神(偏印)
            put("丁辰", "伤官"); // 丁+辰（阴火+阳土）：伤官
            put("丁巳", "比肩"); // 丁+巳（阴火+阴火）：比肩
            put("丁午", "劫财"); // 丁+午（阴火+阳火）：劫财
            put("丁未", "食神"); // 丁+未（阴火+阴土）：食神
            put("丁申", "正财"); // 丁+申（阴火+阳金）：正财
            put("丁酉", "偏财"); // 丁+酉（阴火+阴金）：偏财(才财)
            put("丁戌", "伤官"); // 丁+戌（阴火+阳土）：伤官
            put("丁亥", "七杀"); // 丁+亥（阴火+阴水）：七杀(偏官)
            put("戊甲", "七杀"); // 戊+甲（阳土+阳木）：七杀(偏官)
            put("戊乙", "正官"); // 戊+乙（阳土+阴木）：正官
            put("戊丙", "偏印"); // 戊+丙（阳土+阳火）：枭神(偏印)
            put("戊丁", "正印"); // 戊+丁（阳土+阴火）：正印
            put("戊戊", "比肩"); // 戊+戊（阳土+阳土）：比肩
            put("戊己", "劫财"); // 戊+己（阳土+阴土）：劫财
            put("戊庚", "食神"); // 戊+庚（阳土+阳金）：食神
            put("戊辛", "伤官"); // 戊+辛（阳土+阴金）：伤官
            put("戊壬", "偏财"); // 戊+壬（阳土+阳水）：偏财(才财)
            put("戊癸", "正财"); // 戊+癸（阳土+阴水）：正财
            put("戊子", "偏财"); // 戊+子（阳土+阳水）：偏财(才财)
            put("戊丑", "劫财"); // 戊+丑（阳土+阴土）：劫财
            put("戊寅", "七杀"); // 戊+寅（阳土+阳木）：七杀(偏官)
            put("戊卯", "正官"); // 戊+卯（阳土+阴木）：正官
            put("戊辰", "比肩"); // 戊+辰（阳土+阳土）：比肩
            put("戊巳", "正印"); // 戊+巳（阳土+阴火）：正印
            put("戊午", "偏印"); // 戊+午（阳土+阳火）：枭神(偏印)
            put("戊未", "劫财"); // 戊+未（阳土+阴土）：劫财
            put("戊申", "食神"); // 戊+申（阳土+阳金）：食神
            put("戊酉", "伤官"); // 戊+酉（阳土+阴金）：伤官
            put("戊戌", "比肩"); // 戊+戌（阳土+阳土）：比肩
            put("戊亥", "正财"); // 戊+亥（阳土+阴水）：正财
            put("己甲", "正官"); // 己+甲（阴土+阳木）：正官
            put("己乙", "七杀"); // 己+乙（阴土+阴木）：七杀(偏官)
            put("己丙", "正印"); // 己+丙（阴土+阳火）：正印
            put("己丁", "偏印"); // 己+丁（阴土+阴火）：枭神(偏印)
            put("己戊", "劫财"); // 己+戊（阴土+阳土）：劫财
            put("己己", "比肩"); // 己+己（阴土+阴土）：比肩
            put("己庚", "伤官"); // 己+庚（阴土+阳金）：伤官
            put("己辛", "食神"); // 己+辛（阴土+阴金）：食神
            put("己壬", "正财"); // 己+壬（阴土+阳水）：正财
            put("己癸", "偏财"); // 己+癸（阴土+阴水）：偏财(才财)
            put("己子", "正财"); // 己+子（阴土+阳水）：正财
            put("己丑", "比肩"); // 己+丑（阴土+阴土）：比肩
            put("己寅", "正官"); // 己+寅（阴土+阳木）：正官
            put("己卯", "七杀"); // 己+卯（阴土+阴木）：七杀(偏官)
            put("己辰", "劫财"); // 己+辰（阴土+阳土）：劫财
            put("己巳", "偏印"); // 己+巳（阴土+阴火）：枭神(偏印)
            put("己午", "正印"); // 己+午（阴土+阳火）：正印
            put("己未", "比肩"); // 己+未（阴土+阴土）：比肩
            put("己申", "伤官"); // 己+申（阴土+阳金）：伤官
            put("己酉", "食神"); // 己+酉（阴土+阴金）：食神
            put("己戌", "劫财"); // 己+戌（阴土+阳土）：劫财
            put("己亥", "偏财"); // 己+亥（阴土+阴水）：偏财(才财)
            put("庚甲", "偏财"); // 庚+甲（阳金+阳木）：偏财(才财)
            put("庚乙", "正财"); // 庚+乙（阳金+阴木）：正财
            put("庚丙", "七杀"); // 庚+丙（阳金+阳火）：七杀(偏官)
            put("庚丁", "正官"); // 庚+丁（阳金+阴火）：正官
            put("庚戊", "偏印"); // 庚+戊（阳金+阳土）：枭神(偏印)
            put("庚己", "正印"); // 庚+己（阳金+阴土）：正印
            put("庚庚", "比肩"); // 庚+庚（阳金+阳金）：比肩
            put("庚辛", "劫财"); // 庚+辛（阳金+阴金）：劫财
            put("庚壬", "食神"); // 庚+壬（阳金+阳水）：食神
            put("庚癸", "伤官"); // 庚+癸（阳金+阴水）：伤官
            put("庚子", "食神"); // 庚+子（阳金+阳水）：食神
            put("庚丑", "正印"); // 庚+丑（阳金+阴土）：正印
            put("庚寅", "偏财"); // 庚+寅（阳金+阳木）：偏财(才财)
            put("庚卯", "正财"); // 庚+卯（阳金+阴木）：正财
            put("庚辰", "偏印"); // 庚+辰（阳金+阳土）：枭神(偏印)
            put("庚巳", "正官"); // 庚+巳（阳金+阴火）：正官
            put("庚午", "七杀"); // 庚+午（阳金+阳火）：七杀(偏官)
            put("庚未", "正印"); // 庚+未（阳金+阴土）：正印
            put("庚申", "比肩"); // 庚+申（阳金+阳金）：比肩
            put("庚酉", "劫财"); // 庚+酉（阳金+阴金）：劫财
            put("庚戌", "偏印"); // 庚+戌（阳金+阳土）：枭神(偏印)
            put("庚亥", "伤官"); // 庚+亥（阳金+阴水）：伤官
            put("辛甲", "正财"); // 辛+甲（阴金+阳木）：正财
            put("辛乙", "偏财"); // 辛+乙（阴金+阴木）：偏财(才财)
            put("辛丙", "正官"); // 辛+丙（阴金+阳火）：正官
            put("辛丁", "七杀"); // 辛+丁（阴金+阴火）：七杀(偏官)
            put("辛戊", "正印"); // 辛+戊（阴金+阳土）：正印
            put("辛己", "偏印"); // 辛+己（阴金+阴土）：枭神(偏印)
            put("辛庚", "劫财"); // 辛+庚（阴金+阳金）：劫财
            put("辛辛", "比肩"); // 辛+辛（阴金+阴金）：比肩
            put("辛壬", "伤官"); // 辛+壬（阴金+阳水）：伤官
            put("辛癸", "食神"); // 辛+癸（阴金+阴水）：食神
            put("辛子", "伤官"); // 辛+子（阴金+阳水）：伤官
            put("辛丑", "偏印"); // 辛+丑（阴金+阴土）：枭神(偏印)
            put("辛寅", "正财"); // 辛+寅（阴金+阳木）：正财
            put("辛卯", "偏财"); // 辛+卯（阴金+阴木）：偏财(才财)
            put("辛辰", "正印"); // 辛+辰（阴金+阳土）：正印
            put("辛巳", "七杀"); // 辛+巳（阴金+阴火）：七杀(偏官)
            put("辛午", "正官"); // 辛+午（阴金+阳火）：正官
            put("辛未", "偏印"); // 辛+未（阴金+阴土）：枭神(偏印)
            put("辛申", "劫财"); // 辛+申（阴金+阳金）：劫财
            put("辛酉", "比肩"); // 辛+酉（阴金+阴金）：比肩
            put("辛戌", "正印"); // 辛+戌（阴金+阳土）：正印
            put("辛亥", "食神"); // 辛+亥（阴金+阴水）：食神
            put("壬甲", "食神"); // 壬+甲（阳水+阳木）：食神
            put("壬乙", "伤官"); // 壬+乙（阳水+阴木）：伤官
            put("壬丙", "偏财"); // 壬+丙（阳水+阳火）：偏财(才财)
            put("壬丁", "正财"); // 壬+丁（阳水+阴火）：正财
            put("壬戊", "七杀"); // 壬+戊（阳水+阳土）：七杀(偏官)
            put("壬己", "正官"); // 壬+己（阳水+阴土）：正官
            put("壬庚", "偏印"); // 壬+庚（阳水+阳金）：枭神(偏印)
            put("壬辛", "正印"); // 壬+辛（阳水+阴金）：正印
            put("壬壬", "比肩"); // 壬+壬（阳水+阳水）：比肩
            put("壬癸", "劫财"); // 壬+癸（阳水+阴水）：劫财
            put("壬子", "比肩"); // 壬+子（阳水+阳水）：比肩
            put("壬丑", "正官"); // 壬+丑（阳水+阴土）：正官
            put("壬寅", "食神"); // 壬+寅（阳水+阳木）：食神
            put("壬卯", "伤官"); // 壬+卯（阳水+阴木）：伤官
            put("壬辰", "七杀"); // 壬+辰（阳水+阳土）：七杀(偏官)
            put("壬巳", "正财"); // 壬+巳（阳水+阴火）：正财
            put("壬午", "偏财"); // 壬+午（阳水+阳火）：偏财(才财)
            put("壬未", "正官"); // 壬+未（阳水+阴土）：正官
            put("壬申", "偏印"); // 壬+申（阳水+阳金）：枭神(偏印)
            put("壬酉", "正印"); // 壬+酉（阳水+阴金）：正印
            put("壬戌", "七杀"); // 壬+戌（阳水+阳土）：七杀(偏官)
            put("壬亥", "劫财"); // 壬+亥（阳水+阴水）：劫财
            put("癸甲", "伤官"); // 癸+甲（阴水+阳木）：伤官
            put("癸乙", "食神"); // 癸+乙（阴水+阴木）：食神
            put("癸丙", "正财"); // 癸+丙（阴水+阳火）：正财
            put("癸丁", "偏财"); // 癸+丁（阴水+阴火）：偏财(才财)
            put("癸戊", "正官"); // 癸+戊（阴水+阳土）：正官
            put("癸己", "七杀"); // 癸+己（阴水+阴土）：七杀(偏官)
            put("癸庚", "正印"); // 癸+庚（阴水+阳金）：正印
            put("癸辛", "偏印"); // 癸+辛（阴水+阴金）：枭神(偏印)
            put("癸壬", "劫财"); // 癸+壬（阴水+阳水）：劫财
            put("癸癸", "比肩"); // 癸+癸（阴水+阴水）：比肩
            put("癸子", "劫财"); // 癸+子（阴水+阳水）：劫财
            put("癸丑", "七杀"); // 癸+丑（阴水+阴土）：七杀(偏官)
            put("癸寅", "伤官"); // 癸+寅（阴水+阳木）：伤官
            put("癸卯", "食神"); // 癸+卯（阴水+阴木）：食神
            put("癸辰", "正官"); // 癸+辰（阴水+阳土）：正官
            put("癸巳", "偏财"); // 癸+巳（阴水+阴火）：偏财(才财)
            put("癸午", "正财"); // 癸+午（阴水+阳火）：正财
            put("癸未", "七杀"); // 癸+未（阴水+阴土）：七杀(偏官)
            put("癸申", "正印"); // 癸+申（阴水+阳金）：正印
            put("癸酉", "偏印"); // 癸+酉（阴水+阴金）：枭神(偏印)
            put("癸戌", "正官"); // 癸+戌（阴水+阳土）：正官
            put("癸亥", "比肩"); // 癸+亥（阴水+阴水）：比肩
        }
    };


}
