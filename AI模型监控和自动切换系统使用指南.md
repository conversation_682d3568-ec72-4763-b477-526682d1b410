# AI模型监控和自动切换系统使用指南

## 系统概述

本系统为您的六爻AI分析应用添加了全局的Gemini模型使用监控和自动切换功能，确保在模型使用接近限制时能够自动切换到备用模型，避免服务中断。

## 主要功能

### 1. 实时使用监控
- **RPM监控**：实时跟踪每分钟请求数
- **每日限制监控**：跟踪每日请求总数
- **多模型支持**：同时监控多个Gemini模型的使用情况

### 2. 自动模型切换
- **智能切换**：当使用率达到阈值（默认80%）时自动切换
- **优先级管理**：按照预设优先级选择最佳备用模型
- **冷却机制**：防止频繁切换，确保系统稳定

### 3. 手动管理
- **Web界面**：直观的监控面板
- **REST API**：完整的管理接口
- **实时状态**：随时查看当前使用情况

## 模型优先级配置

系统按以下优先级自动切换模型：

1. **gemini-2.5-flash** (主要模型)
   - RPM限制：10
   - 每日限制：500

2. **gemini-2.5-flash-lite** (备用模型1)
   - RPM限制：15
   - 每日限制：500

3. **gemini-2.0-flash** (备用模型2)
   - RPM限制：15
   - 每日限制：1500

4. **gemini-2.0-flash-lite** (备用模型3)
   - RPM限制：30
   - 每日限制：1500

5. **gemini-2.0-flash-preview-image-generation** (最后备用)
   - RPM限制：10
   - 每日限制：1500

## 使用方法

### 1. 启动应用
```bash
mvn clean package -pl xuan-app -DskipTests
java -jar xuan-app/target/xuan-app-1.0-SNAPSHOT.jar
```

### 2. 访问监控界面
打开浏览器访问：`http://localhost:8080/model-monitor.html`

### 3. 主要功能界面
- **实时状态卡片**：显示当前活跃模型和使用情况
- **模型网格**：显示所有模型的详细使用统计
- **手动切换**：选择模型并手动切换
- **操作日志**：查看所有操作记录

## API接口

### 获取当前状态
```http
GET /api/model/status
```

### 获取所有模型使用统计
```http
GET /api/model/usage/all
```

### 手动切换模型
```http
POST /api/model/switch/{modelName}
```

### 获取推荐模型
```http
GET /api/model/next-recommended
```

### 重置使用统计
```http
POST /api/model/reset-stats
```

### 获取简要状态
```http
GET /api/model/status/brief
```

## 配置参数

在 `application.properties` 中可以调整以下配置：

```properties
# 启用模型使用监控
ai.analysis.usage-monitoring-enabled=true

# 启用自动模型切换
ai.analysis.auto-model-switch-enabled=true

# 使用率阈值（百分比），超过此阈值时切换模型
ai.analysis.usage-threshold=80.0

# 监控检查间隔（秒）
ai.analysis.monitoring-interval-seconds=30
```

## 工作原理

### 1. 使用监控
- 每次AI调用后记录使用情况
- 定时重置RPM计数器（每分钟）
- 自动处理日期变更（重置每日计数）

### 2. 自动切换逻辑
- 定时检查当前模型使用情况（默认30秒）
- 当RPM或每日使用率超过阈值时触发切换
- 按优先级选择下一个可用模型
- 实施冷却机制防止频繁切换

### 3. 错误处理
- 检测API限流错误并自动切换
- 重试机制确保请求成功
- 详细日志记录所有操作

## 监控指标

### 使用率颜色编码
- **绿色**：使用率 < 60%（安全）
- **黄色**：使用率 60-80%（警告）
- **红色**：使用率 > 80%（危险）

### 关键指标
- **RPM使用率**：当前分钟内的请求使用百分比
- **每日使用率**：当天的请求使用百分比
- **模型可用性**：是否可以发送新请求
- **切换状态**：是否正在进行模型切换

## 故障排查

### 1. 监控不工作
- 检查配置：`ai.analysis.usage-monitoring-enabled=true`
- 查看日志：检查是否有初始化错误

### 2. 自动切换不生效
- 检查配置：`ai.analysis.auto-model-switch-enabled=true`
- 确认阈值设置：`ai.analysis.usage-threshold`

### 3. API调用失败
- 检查应用是否正常启动
- 确认端口8080未被占用
- 查看应用日志获取详细错误信息

## 最佳实践

### 1. 监控建议
- 定期查看监控界面了解使用情况
- 在高峰期前手动切换到高限制模型
- 定期重置统计数据进行测试

### 2. 配置优化
- 根据实际使用情况调整阈值
- 在测试环境中验证切换逻辑
- 备份重要的配置参数

### 3. 运维建议
- 监控应用日志
- 定期检查模型可用性
- 建立告警机制

## 技术架构

### 核心组件
- **ModelConfig**：模型配置管理
- **ModelUsageMonitorService**：使用情况监控
- **ModelSwitchService**：自动切换逻辑
- **ModelManagementController**：REST API接口
- **AIService**：增强的AI调用服务

### 异步处理
- 使用Spring的@Async注解进行异步监控
- 定时任务自动重置计数器
- 线程安全的并发处理

## 总结

本系统为您的AI应用提供了完整的模型使用监控和自动切换解决方案，确保服务的高可用性和稳定性。通过智能的切换逻辑和直观的监控界面，您可以轻松管理多个Gemini模型的使用，避免因限制而导致的服务中断。
