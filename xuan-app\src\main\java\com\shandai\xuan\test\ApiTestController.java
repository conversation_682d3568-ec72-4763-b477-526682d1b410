package com.shandai.xuan.test;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shandai.xuan.service.AIService;
import com.shandai.xuan.service.StreamingAIService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API测试控制器 - 用于调试Gemini API连接问题
 */
@RestController
@RequestMapping("/api/test")
public class ApiTestController {

    private static final Logger logger = LoggerFactory.getLogger(ApiTestController.class);

    @Value("${gemini.api.key}")
    private String apiKey;

    @Value("${gemini.api.url}")
    private String apiUrl;

    @Autowired
    private AIService aiService;

    @Autowired
    private StreamingAIService streamingAIService;
    
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    public ApiTestController(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
        this.objectMapper = new ObjectMapper();
    }
    
    @GetMapping("/simple")
    public Map<String, Object> testSimpleApi() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("开始测试API连接...");
            logger.info("API URL: {}", apiUrl);
            logger.info("API Key前缀: {}...", apiKey.substring(0, Math.min(10, apiKey.length())));
            
            // 构建简单的测试请求
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "gemini-2.5-flash");
            requestBody.put("temperature", 0.7);
            requestBody.put("max_tokens", 100); // 使用较小的token数量进行测试
            
            // 构建简单的消息
            List<Map<String, String>> messages = new ArrayList<>();
            Map<String, String> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", "你好，请简单回复一下。");
            messages.add(message);
            requestBody.put("messages", messages);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + apiKey);
            headers.set("Content-Type", "application/json");
            
            logger.info("发送测试请求...");
            long startTime = System.currentTimeMillis();
            
            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                apiUrl, 
                HttpMethod.POST, 
                requestEntity, 
                String.class
            );
            
            long endTime = System.currentTimeMillis();
            logger.info("API调用完成，耗时：{}ms", endTime - startTime);
            
            // 解析响应
            JsonNode responseJson = objectMapper.readTree(response.getBody());
            
            result.put("success", true);
            result.put("duration", endTime - startTime);
            result.put("statusCode", response.getStatusCode().value());
            result.put("response", responseJson);
            
            logger.info("API测试成功");
            
        } catch (Exception e) {
            logger.error("API测试失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
        }
        
        return result;
    }
    
    @GetMapping("/config")
    public Map<String, Object> getConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("apiUrl", apiUrl);
        config.put("apiKeyPrefix", apiKey.substring(0, Math.min(10, apiKey.length())) + "...");
        config.put("apiKeyLength", apiKey.length());
        return config;
    }

    /**
     * 测试AI服务基础功能
     */
    @PostMapping("/ai-basic")
    public ResponseEntity<String> testAIBasic(@RequestBody Map<String, String> request) {
        try {
            String prompt = request.get("prompt");
            if (prompt == null || prompt.trim().isEmpty()) {
                prompt = "你好，请简单回复一下，确认连接正常。";
            }

            logger.info("开始测试AI基础功能，提示词：{}", prompt);
            String result = aiService.getAnalysis(prompt);
            logger.info("AI基础功能测试成功");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("AI基础功能测试失败", e);
            return ResponseEntity.status(500).body("测试失败：" + e.getMessage());
        }
    }

    /**
     * 测试快速AI分析功能
     */
    @PostMapping("/ai-fast")
    public ResponseEntity<String> testAIFast(@RequestBody Map<String, String> request) {
        try {
            String prompt = request.get("prompt");
            if (prompt == null || prompt.trim().isEmpty()) {
                prompt = "请快速分析一下今日运势，简短回复即可。";
            }

            logger.info("开始测试快速AI分析，提示词：{}", prompt);
            String result = streamingAIService.getFastAnalysis(prompt);
            logger.info("快速AI分析测试成功");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("快速AI分析测试失败", e);
            return ResponseEntity.status(500).body("测试失败：" + e.getMessage());
        }
    }
}
