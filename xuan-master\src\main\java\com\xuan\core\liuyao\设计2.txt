benguainformation数据格式：[旺衰得分，是否是动爻，化回头生/回头克/废/空，是否旬空，是否伏吟，化进还是化退,是世还是应]
注意需要参看上下文。按照组装模式和封装方法进行代码设计。
我需要加强核心服务层的LiuYao.java的六爻断卦功能。流程的每一个判断都要加注释，可供后期筛查检查。
这次加强只加强“考学”的功能。
先判断父母爻和官鬼爻是否全在benguainformation出现，如果全出现则进入流程A否则进入流程B。
流程A：按顺序判断：1、如果本卦第A爻既是动爻六亲又是子孙，并且变卦第A爻六亲是父母并且本卦第A爻不是化废，则批语是：不是很顺利，但还是能取得还不错的成绩。2、如果本卦第A爻是动爻六亲是官鬼，并且父母爻旺衰打分小于30分并且不旬空并且第B爻是世爻旺衰打分大于等于0分，则批语是最终能取得很好的成绩；如果本卦第A爻是动爻六亲是官鬼，并且父母爻旺衰打分大于30分并且父母爻不旬空，并且第B爻是世爻旺衰打分大于等于0分，则批语是卦主如果肯十分努力还是有机会得高分；如果本卦第A爻是动爻六亲是官鬼，并且父母爻旺衰打分大于30分并且父母爻不旬空，并且第B爻是世爻旺衰打分小于0分，则批语是用神衰，并且世爻衰，卦主恐难得高分；如果本卦第A爻是动爻六亲是官鬼，并且父母爻旬空，并且第B爻是世爻旺衰打分大于等于0分，并且世爻旺衰得分大于等于0分，卦主如果是短期只是还是有机会得高分



我正在做六爻占断批语的设计，我需要将所有可能覆盖，但是所有可能太多了，我想先教你你再来帮我枚举所有可能。怎么枚举：按照可能1，兄弟爻发动，世爻是旺还是衰，父母爻是旺还是衰，是否有动化用神父母，父母爻是否旬空2、妻财爻发动，世爻是旺还是衰，父母爻是旺还是衰，是否有动化用神父母，父母爻是否旬空3、子孙爻发动，世爻是旺还是衰，父母爻是旺还是衰，父母爻是否旬空4、父母爻发动，世爻是旺还是衰，父母爻是旺还是衰，父母爻是否旬空5、官鬼爻发动，世爻是旺还是衰，父母爻是旺还是衰，父母爻是否旬空6、兄弟爻，妻财爻同时发动......等等太多了，直到枚举到五个爻都发动的情况，情况可能数百条。判断逻辑我来教你：旬空的爻没办法参与生克，


断卦有一套逻辑，如果我使用枚举法太复杂了我希望你还是能尽力实现：1、除了特殊情况旬空的爻不参与生克因此不加入生克路线，除非旬空的爻跟日辰相冲，则又能参与生克了2、动爻a发动优先去生其他动爻，如果没有其他动爻能被动爻a生，则动爻a去克其他动爻，如果动爻a既不能生其他动爻也不能克其他动爻，则寻找能生的用神爻，如果找不到能生的用神爻则找能克的用神爻，参与生克的动爻只能生或者克一次有点像信息在路由器传播，它的行动次数只有1或者叫生存周期只有1，卦中有可能出现多个动爻连续相生或相克的路线，要能记录一条或者多个条路线的尽头分别是克还是生。3、占事内容为考学时，用神爻指的是六亲为父母的爻，如果卦里有多个父母爻，则如果有两个六亲为父母的爻，则在两个父母爻选是动爻的一个；如果两个父母爻都是动爻或者都是静爻则选是世爻或应爻的那个；如果都不满足，则计算两个爻在以下内容的拥有个数，谁拥有以下信息[旬空，月地支相冲，月地支单独计算的旺衰得分10分，日地支单独计算的旺衰得分10分，与月地支相同，与日地支相同]有的多则用神爻的位置就是哪个并记录它的位置yongshenposition；如果拥有个数相同则分别计算父母爻的爻位置与世爻的爻位置距离的绝对值，哪个近选哪个；如果距离还一样，则随机选第一个。4、如果占事是考学，如果有动爻化出父母爻并且化出的父母爻旺衰评分大于等于-10分，则直接批能取得好成绩。不用进行后续计算，并将批语记录在string变量里返回；如果世爻的旺衰评分小于0分，则评价需十分努力否则恐难取得好成绩；如果六亲是父母的爻或者官鬼爻是化废，则批需十分努力否则恐难取得好成绩5、如果占事为考学有动爻化出父母爻并且化出的父母爻旺衰评分小于-10分或者没有动爻化出父母爻，如果世爻的旺衰评分小于0分批需十分努力否则恐难取得好成绩；如果只有动爻相生的路线，并且尽头是官鬼爻或者用神爻，则批容易取得好成绩；如果动爻的路线只有克的路线，并且尽头是官鬼爻或者用神爻，则批恐难取得好成绩，如果既有动爻路线是生也有路线尽头是克，如果生路线的尽头在用神爻，克在官鬼爻以外的爻位置则批能取得好成绩，反之其他情况则批需十分努力否则恐难取得好成绩；如果动爻路线只有尽头为克的路线，但是尽头不在官鬼爻和用神爻，并且用神爻或者官鬼爻的旺衰评分大于等于0分则批能取得好成绩，否则批需十分努力否则恐难取得好成绩