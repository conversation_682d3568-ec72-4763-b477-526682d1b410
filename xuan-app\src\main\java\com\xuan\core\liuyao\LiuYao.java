package com.xuan.core.liuyao;

import com.nlf.calendar.EightChar;
import com.nlf.calendar.Lunar;
import com.nlf.calendar.Solar;
import com.xuan.utils.CommonUtil;

import lombok.Getter;

import java.util.*;

/**
 * 六爻
 *
 * <AUTHOR>
 */
@Getter
public class LiuYao {

    /**
     * 公历日期
     */
    private Solar solar;
    /**
     * 农历日期
     */
    private Lunar lunar;
    /**
     * 星期
     */
    private String week;

    /**
     * 年干
     */
    private String yearGan;
    /**
     * 月干
     */
    private String monthGan;
    /**
     * 日干
     */
    private String dayGan;
    /**
     * 时干
     */
    private String hourGan;

    /**
     * 年支
     */
    private String yearZhi;
    /**
     * 月支
     */
    private String monthZhi;
    /**
     * 日支
     */
    private String dayZhi;
    /**
     * 时支
     */
    private String hourZhi;

    /**
     * 年干支
     */
    private String yearGanZhi;
    /**
     * 月干支
     */
    private String monthGanZhi;
    /**
     * 日干支
     */
    private String dayGanZhi;
    /**
     * 时干支
     */
    private String hourGanZhi;

    /**
     * 八字
     */
    private List<String> baZi;
    /**
     * 八字五行
     */
    private List<String> baZiWuXing;
    /**
     * 八字旬空
     */
    private List<String> baZiXunKong;
    /**
     * 八字纳音
     */
    private List<String> baZiNaYin;

    /**
     * 六爻爻象（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：[—, --, --, --, --, —]）
     */
    private List<String> liuYaoAs;
    /**
     * 六爻爻象标识（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：[老阳, 老阴, 少阴, 老阴, 少阴, 少阳]）
     */
    private List<String> liuYaoYaoXiangMark;
    /**
     * 六爻爻象标识名称（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：[○, ×, , ×, , ]）
     */
    private List<String> liuYaoYaoXiangMarkName;
    /**
     * 六爻世应（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：[,世, , ,应 , ]）
     */

    /**
     * 上卦（如：乾）
     */
    private String shangGua;
    /**
     * 下卦（如：乾）
     */
    private String xiaGua;

    /**
     * 本卦（如：乾为天）
     */
    private String benGua;
    /**
     * 变卦（如：乾为天）
     */
    private String bianGua;
    /**
     * 互卦（如：乾为天）
     */
    private String huGua;
    /**
     * 错卦（如：乾为天）
     */
    private String cuoGua;
    /**
     * 综卦（如：乾为天）
     */
    private String zongGua;

    /**
     * 上卦卦象（如：☰）
     */
    private String shangGuaAs;
    /**
     * 下卦卦象（如：☰）
     */
    private String xiaGuaAs;
    /**
     * 本卦卦象（如：䷀）
     */
    private String benGuaAs;
    /**
     * 变卦卦象（如：䷀）
     */
    private String bianGuaAs;
    /**
     * 互卦卦象（如：䷀）
     */
    private String huGuaAs;
    /**
     * 错卦卦象（如：䷀）
     */
    private String cuoGuaAs;
    /**
     * 综卦卦象（如：䷀）
     */
    private String zongGuaAs;

    /**
     * 本卦的六爻爻象（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：—, —, —, —, —, —）
     */
    private List<String> benGuaLiuYaoAs;
    /**
     * 变卦的六爻爻象（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：—, —, —, —, —, —）
     */
    private List<String> bianGuaLiuYaoAs;
    /**
     * 互卦的六爻爻象（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：—, —, —, —, —, —）
     */
    private List<String> huGuaLiuYaoAs;
    /**
     * 错卦的六爻爻象（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：—, —, —, —, —, —）
     */
    private List<String> cuoGuaLiuYaoAs;
    /**
     * 综卦的六爻爻象（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：—, —, —, —, —, —）
     */
    private List<String> zongGuaLiuYaoAs;

    /**
     * 本卦的六爻名称（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：初九, 九二, 九三, 九四, 九五, 上九）
     */
    private List<String> benGuaLiuYaoName;
    /**
     * 变卦的六爻名称（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：初九, 九二, 九三, 九四, 九五, 上九）
     */
    private List<String> bianGuaLiuYaoName;
    /**
     * 互卦的六爻名称（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：初九, 九二, 九三, 九四, 九五, 上九）
     */
    private List<String> huGuaLiuYaoName;
    /**
     * 错卦的六爻名称（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：初九, 九二, 九三, 九四, 九五, 上九）
     */
    private List<String> cuoGuaLiuYaoName;
    /**
     * 综卦的六爻名称（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：初九, 九二, 九三, 九四, 九五, 上九）
     */
    private List<String> zongGuaLiuYaoName;

    /**
     * 本卦的六爻世应（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：, , 应, , , 世）
     */
    private List<String> benGuaLiuYaoShiYing;
    /**
     * 变卦的六爻世应（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：, , 应, , , 世）
     */
    private List<String> bianGuaLiuYaoShiYing;
    /**
     * 互卦的六爻世应（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：, , 应, , , 世）
     */
    private List<String> huGuaLiuYaoShiYing;
    /**
     * 错卦的六爻世应（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：, , 应, , , 世）
     */
    private List<String> cuoGuaLiuYaoShiYing;
    /**
     * 综卦的六爻世应（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：, , 应, , , 世）
     */
    private List<String> zongGuaLiuYaoShiYing;

    /**
     * 本卦的六爻六亲（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：子孙, 妻财, 父母, 官鬼, 兄弟, 父母）
     */
    private List<String> benGuaLiuYaoLiuQin;
    /**
     * 本卦的藏爻六爻六亲（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：子孙, 妻财, 父母, 官鬼, 兄弟, 父母）
     */
    private List<String> benGuaCangYaoLiuQin;
    /**
     * 变卦的六爻六亲（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：子孙, 妻财, 父母, 官鬼, 兄弟, 父母）
     */
    private List<String> bianGuaLiuYaoLiuQin;
    /**
     * 互卦的六爻六亲（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：子孙, 妻财, 父母, 官鬼, 兄弟, 父母）
     */
    private List<String> huGuaLiuYaoLiuQin;
    /**
     * 错卦的六爻六亲（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：子孙, 妻财, 父母, 官鬼, 兄弟, 父母）
     */
    private List<String> cuoGuaLiuYaoLiuQin;
    /**
     * 综卦的六爻六亲（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：子孙, 妻财, 父母, 官鬼, 兄弟, 父母）
     */
    private List<String> zongGuaLiuYaoLiuQin;

    /**
     * 本卦的六爻干支（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：甲子, 甲寅, 甲辰, 壬午, 壬申, 壬戌）
     */
    private List<String> benGuaLiuYaoGanZhi;
        /**
     * 本卦的藏爻干支（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：甲子, 甲寅, 甲辰, 壬午, 壬申, 壬戌）
     */
    private List<String> benGuaCangYaoGanZhi;
    /**
     * 变卦的六爻干支（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：甲子, 甲寅, 甲辰, 壬午, 壬申, 壬戌）
     */
    private List<String> bianGuaLiuYaoGanZhi;
    /**
     * 互卦的六爻干支（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：甲子, 甲寅, 甲辰, 壬午, 壬申, 壬戌）
     */
    private List<String> huGuaLiuYaoGanZhi;
    /**
     * 错卦的六爻干支（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：甲子, 甲寅, 甲辰, 壬午, 壬申, 壬戌）
     */
    private List<String> cuoGuaLiuYaoGanZhi;
    /**
     * 综卦的六爻干支（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：甲子, 甲寅, 甲辰, 壬午, 壬申, 壬戌）
     */
    private List<String> zongGuaLiuYaoGanZhi;

    /**
     * 本卦的六爻五行（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：水, 木, 土, 火, 金, 土）
     */
    private List<String> benGuaLiuYaoWuXing;
    /**
     * 变卦的六爻五行（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：水, 木, 土, 火, 金, 土）
     */
    private List<String> bianGuaLiuYaoWuXing;
    /**
     * 互卦的六爻五行（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：水, 木, 土, 火, 金, 土）
     */
    private List<String> huGuaLiuYaoWuXing;
    /**
     * 错卦的六爻五行（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：水, 木, 土, 火, 金, 土）
     */
    private List<String> cuoGuaLiuYaoWuXing;
    /**
     * 综卦的六爻五行（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：水, 木, 土, 火, 金, 土）
     */
    private List<String> zongGuaLiuYaoWuXing;

    /**
     * 本卦的六爻六神（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：青龙, 朱雀, 勾陈, 螣蛇, 白虎, 玄武）
     */
    private List<String> benGuaLiuYaoLiuShen;
    /**
     * 变卦的六爻六神（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：青龙, 朱雀, 勾陈, 螣蛇, 白虎, 玄武）
     */
    private List<String> bianGuaLiuYaoLiuShen;
    /**
     * 互卦的六爻六神（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：青龙, 朱雀, 勾陈, 螣蛇, 白虎, 玄武）
     */
    private List<String> huGuaLiuYaoLiuShen;
    /**
     * 错卦的六爻六神（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：青龙, 朱雀, 勾陈, 螣蛇, 白虎, 玄武）
     */
    private List<String> cuoGuaLiuYaoLiuShen;
    /**
     * 综卦的六爻六神（顺序：初爻、二爻、三爻、四爻、五爻、上爻。如：青龙, 朱雀, 勾陈, 螣蛇, 白虎, 玄武）
     */
    private List<String> zongGuaLiuYaoLiuShen;

    /**
     * 本卦的六爻爻辞（顺序：初爻、二爻、三爻、四爻、五爻、上爻）
     */
    private List<String> benGuaLiuYaoYaoCi;
    /**
     * 变卦的六爻爻辞（顺序：初爻、二爻、三爻、四爻、五爻、上爻）
     */
    private List<String> bianGuaLiuYaoYaoCi;
    /**
     * 互卦的六爻爻辞（顺序：初爻、二爻、三爻、四爻、五爻、上爻）
     */
    private List<String> huGuaLiuYaoYaoCi;
    /**
     * 错卦的六爻爻辞（顺序：初爻、二爻、三爻、四爻、五爻、上爻）
     */
    private List<String> cuoGuaLiuYaoYaoCi;
    /**
     * 综卦的六爻爻辞（顺序：初爻、二爻、三爻、四爻、五爻、上爻）
     */
    private List<String> zongGuaLiuYaoYaoCi;

    /**
     * 本卦卦辞
     */
    private String benGuaGuaCi;

    /**
     * 变卦卦辞
     */
    private String bianGuaGuaCi;
    /**
     * 互卦卦辞
     */
    private String huGuaGuaCi;
    /**
     * 错卦卦辞
     */
    private String cuoGuaGuaCi;
    /**
     * 综卦卦辞
     */
    private String zongGuaGuaCi;

    /**
     * 驿马
     */
    private String yiMa;
    /**
     * 天马
     */
    private String tianMa;
    /**
     * 天元禄
     */
    private String tianYuanLu;
    /**
     * 天乙贵人
     */
    private String tianYiGuiRen;
    /**
     * 太极贵人
     */
    private String taiJiGuiRen;
    /**
     * 天德贵人
     */
    private String tianDeGuiRen;
    /**
     * 月德贵人
     */
    private String yueDeGuiRen;
    /**
     * 唐符国印
     */
    private String tangFuGuoYin;
    /**
     * 咸池（桃花）
     */
    private String xianChi;
    /**
     * 天喜
     */
    private String tianXi;
    /**
     * 皇恩
     */
    private String huangEn;
    /**
     * 文昌
     */
    private String wenChang;
    /**
     * 华盖
     */
    private String huaGai;
    /**
     * 将星
     */
    private String jiangXing;
    /**
     * 灾煞
     */
    private String zaiSha;
    /**
     * 劫煞
     */
    private String jieSha;
    /**
     * 谋星
     */
    private String mouXing;
    /**
     * 天医
     */
    private String tianYi;
    /**
     * 占卜事项，如考学，姻缘，财运等
     */
    private String occupy;

    /**
     * 本卦内部信息 - 六爻详细分析数据
     *
     * 数据结构：List<List<String>>，外层List包含6个元素，对应六爻（初爻到上爻）
     * 内层List包含10个字段，按索引顺序为：
     *
     * [0] 动静状态：
     *     - "动爻"：该爻为动爻（有○或×标记，或与日支相冲且旺衰≥0）
     *     - "静爻"：该爻为静爻
     *
     * [1] 化状态（仅动爻有效）：
     *     - "化回头生"：动爻变化后的五行生本爻五行
     *     - "化回头克"：动爻变化后的五行克本爻五行
     *     - "化废"：变化后的地支与月支相冲，或与日支相冲且月日组合旺衰<0
     *     - "化空"：变化后的地支落入日旬空
     *     - ""：静爻或无特殊化状态
     *
     * [2] 旬空状态：
     *     - "旬空"：该爻地支落入日旬空（根据日干支计算的旬空地支）
     *     - "非空"：该爻地支不在旬空范围内
     *
     * [3] 伏吟状态：
     *     - "伏吟"：本卦该爻地支与变卦该爻地支相同（动而不变）
     *     - ""：非伏吟状态
     *
     * [4] 化进退（仅动爻有效）：
     *     - "化进"：变化后的地支为本爻地支的进神（长生方向）
     *     - "化退"：变化后的地支为本爻地支的退神（死墓方向）
     *     - ""：静爻或无进退关系
     *
     * [5] 世应位置：
     *     - "世"：该爻为世爻（代表求测者自身）
     *     - "应"：该爻为应爻（代表所测事物或他人）
     *     - ""：普通爻位
     *
     * [6] 六亲关系：
     *     - "父母"：代表长辈、文书、房屋等
     *     - "兄弟"：代表同辈、竞争、劫财等
     *     - "子孙"：代表晚辈、技艺、制克官鬼等
     *     - "妻财"：代表财物、妻子、身体等
     *     - "官鬼"：代表官职、疾病、阻碍等
     *
     * [7] 地支：
     *     - 十二地支之一："子"、"丑"、"寅"、"卯"、"辰"、"巳"、"午"、"未"、"申"、"酉"、"戌"、"亥"
     *
     * [8] 五行属性：
     *     - "水"、"木"、"火"、"土"、"金" 之一
     *
     * [9] 旺衰百分比：
     *     - 格式："XX%"，如"85%"、"30%"等
     *     - 计算方式：((旺衰得分 + 30) / 70) * 100，得分范围-40到+30，转换为0%-100%
     *     - 旺衰得分综合考虑月支、日支对该爻地支的生克制化关系
     *
     * 示例数据：
     * [["动爻", "化回头生", "非空", "", "化进", "世", "父母", "子", "水", "85%"],
     *  ["静爻", "", "旬空", "", "", "", "兄弟", "丑", "土", "45%"],
     *  ["静爻", "", "非空", "", "", "", "官鬼", "寅", "木", "70%"],
     *  ["静爻", "", "非空", "", "", "应", "父母", "卯", "木", "60%"],
     *  ["动爻", "化回头克", "非空", "", "化退", "", "妻财", "辰", "土", "30%"],
     *  ["静爻", "", "非空", "", "", "", "子孙", "巳", "火", "25%"]]
     */
    private List<List<String>> benguainformation;

    /**
     * 变卦内部信息 - 变卦六爻详细分析数据
     *
     * 注意：目前此字段在代码中已声明但尚未实现具体的计算逻辑
     *
     * 预期数据结构：与benguainformation相同，List<List<String>>
     * 应包含变卦六爻的详细信息，包括：
     * - 动静状态、化状态、旬空状态、伏吟状态
     * - 化进退、世应位置、六亲关系
     * - 地支、五行属性、旺衰百分比
     *
     * TODO: 需要实现calculateBianGuaInformation()方法来填充此数据
     * 变卦信息主要用于分析动爻变化后的状态和趋势
     */
    private List<List<String>> bianguainformation;



    /**
     * 断卦结果
     */
    private String duangua;

    /**
     * 考学批语（详细断卦说明）
     */
    private String analyzeKaoXue;
    /**
     * 姻缘批语（详细断卦说明）
     */
    private String analyzeLove;
    /**
     * 出行批语（详细断卦说明）
     */
    private String analyzeTravel;
    /**
     * 财运批语（详细断卦说明）
     */
    private String analyzeCaiYun;

    /**
     * 六爻旺衰得分百分比（顺序：初爻、二爻、三爻、四爻、五爻、上爻）
     */
    private List<String> liuYaoWangShuaiPercent;

//************************************************************************************************************************************

    /**
     * 使用默认设置初始化（默认使用当前公历日期）
     */
    public LiuYao() {
        LiuYaoSetting setting = new LiuYaoSetting();
        core(setting); // 获取并设置数据
    }

    /**
     * 使用公历日期初始化
     *
     * @param date 公历日期
     */
    public LiuYao(Date date) {
        LiuYaoSetting setting = new LiuYaoSetting(date);
        core(setting); // 获取并设置数据
    }

    /**
     * 使用日期初始化
     *
     * @param date     公历日期
     * @param dateType 日期类型（0:公历。1:农历）
     */
    public LiuYao(Date date, int dateType) {
        LiuYaoSetting setting = new LiuYaoSetting(date, dateType);
        core(setting); // 获取并设置数据
    }

    /**
     * 使用公历年月日时分秒初始化
     *
     * @param year   公历年（0~9999）
     * @param month  公历月（1~12）
     * @param day    公历日
     * @param hour   公历时（0~24）
     * @param minute 公历分（0~24）
     * @param second 公历秒（0~24）
     */
    public LiuYao(int year, int month, int day, int hour, int minute, int second) {
        LiuYaoSetting setting = new LiuYaoSetting(year, month, day, hour, minute, second);
        core(setting); // 获取并设置数据
    }

    /**
     * 使用年月日时分秒、日期类型初始化
     *
     * @param year     年
     * @param month    月
     * @param day      日
     * @param hour     时
     * @param minute   分
     * @param second   秒
     * @param dateType 日期类型（0:公历。1:农历）
     */
    public LiuYao(int year, int month, int day, int hour, int dateType, int minute, int second) {
        LiuYaoSetting setting = new LiuYaoSetting(year, month, day, hour, minute, second, dateType);
        core(setting); // 获取并设置数据
    }

    /**
     * 使用自定义设置初始化
     *
     * @param setting 设置
     */
    public LiuYao(LiuYaoSetting setting) {
        core(setting); // 获取并设置数据
    }

//====================================================================================================================================

    /**
     * 获取并设置数据（★核心）
     *
     * @param setting 六爻-设置
     */
    private void core(LiuYaoSetting setting) {

        // 初始化自定义数据
        initialize(setting);

        // 计算数据
        baZiData(); // 计算八字、八字五行、八字纳音、八字旬空
        shangGua(); // 计算上卦的卦名、卦象
        xiaGua(); // 计算下卦的卦名、卦象
        benGua(); // 计算本卦的卦名、卦象、爻象、六爻名称、六爻世应、六爻六亲、六爻干支、六爻五行、六爻六神、六爻爻辞
        bianGua(); // 计算变卦的卦名、卦象、爻象、六爻名称、六爻世应、六爻六亲、六爻干支、六爻五行、六爻六神、六爻爻辞
        huGua(); // 计算互卦的卦象、卦名、六爻爻象、名称、世应、六亲、干支、五行、六神、爻辞
        cuoGua(); // 计算错卦的卦象、卦名、六爻爻象、名称、世应、六亲、干支、五行、六神、爻辞
        zongGua(); // 计算综卦的卦象、卦名、六爻爻象、名称、世应、六亲、干支、五行、六神、爻辞
        guaCi(); // 计算本卦、变卦、互卦、错卦、综卦的卦辞
        yiMa(); // 计算驿马
        tianMa(); // 计算天马
        tianYuanLu(); // 计算天元禄
        tianYiGuiRen(); // 计算天乙贵人
        taiJiGuiRen(); // 计算太极贵人
        tianDeGuiRen(); // 计算天德贵人
        yueDeGuiRen(); // 计算月德贵人
        tangFuGuoYin(); // 计算唐符国印
        xianChi(); // 计算咸池（桃花）
        tianXi(); // 计算天喜
        huangEn(); // 计算皇恩
        wenChang(); // 计算文昌
        huaGai(); // 计算华盖
        jiangXing(); // 计算将星
        zaiSha(); // 计算灾煞
        jieSha(); // 计算劫煞
        mouXing(); // 计算谋星
        tianYi(); // 计算天医
        duanGua(); // 断卦

    }

    /**
     * 初始化自定义数据
     *
     * @param setting 六爻设置
     */
    private void initialize(LiuYaoSetting setting) {

        // 1.1、判断日期类型并返回公历日期、农历日期
        Map<String, Object> dateMap = LiuYaoUtil.isDateType(setting);
        // 1.2、设置日期
        this.solar = (Solar) dateMap.get("solar"); // 设置公历日期
        this.lunar = (Lunar) dateMap.get("lunar"); // 设置农历日期

        // 2、设置星期
        this.week = "周" + getLunar().getWeekInChinese();

        // 3.1、判断干支设置并返回干支
        Map<String, List<String>> ganZhiMap = LiuYaoUtil.isGanZhi(setting, getSolar().getLunar());
        // 3.2、设置年干支
        List<String> yearGanZhi = ganZhiMap.get("yearGanZhi");
        this.yearGan = yearGanZhi.get(0); // 年干
        this.yearZhi = yearGanZhi.get(1); // 年支
        this.yearGanZhi = yearGanZhi.get(2); // 年干支
        // 3.3、设置月干支
        List<String> monthGanZhi = ganZhiMap.get("monthGanZhi");
        this.monthGan = monthGanZhi.get(0); // 月干
        this.monthZhi = monthGanZhi.get(1); // 月支
        this.monthGanZhi = monthGanZhi.get(2); // 月干支
        // 3.4、设置日干支
        List<String> dayGanZhi = ganZhiMap.get("dayGanZhi");
        this.dayGan = dayGanZhi.get(0); // 日干
        this.dayZhi = dayGanZhi.get(1); // 日支
        this.dayGanZhi = dayGanZhi.get(2); // 日干支
        // 3.5、设置时干支
        List<String> hourGanZhi = ganZhiMap.get("hourGanZhi");
        this.hourGan = hourGanZhi.get(0); // 时干
        this.hourZhi = hourGanZhi.get(1); // 时支
        this.hourGanZhi = hourGanZhi.get(2); // 时干支

        // 4、判断起卦模式
        if (setting.getQiGuaMode() == 0) {
            // 4.1、日期起卦模式
            dateQiGua(); // 计算六爻爻象、六爻爻象标识、六爻爻象标识名称
        } else {
            // 4.2、其他起卦模式
            List<List<String>> list = LiuYaoUtil.isQiGuaMode(setting);
            if (null != list && list.size() == 3) {
                this.liuYaoAs = list.get(0); // 六爻爻象
                this.liuYaoYaoXiangMark = list.get(1); // 六爻爻象标识
                this.liuYaoYaoXiangMarkName = list.get(2); // 六爻爻象标识名称
            }
        }
        
        //5、设置占事类型
        this.occupy = setting.getOccupy();

    }

    /**
     * 计算八字、八字五行、八字纳音、八字旬空
     */
    private void baZiData() {

        EightChar ec = getLunar().getEightChar();

        this.baZi = Arrays.asList(getYearGanZhi(), getMonthGanZhi(), getDayGanZhi(), getHourGanZhi()); // 八字
        this.baZiWuXing = Arrays.asList(ec.getYearWuXing(), ec.getMonthWuXing(), ec.getDayWuXing(), ec.getTimeWuXing()); // 八字五行
        this.baZiNaYin = Arrays.asList(ec.getYearNaYin(), ec.getMonthNaYin(), ec.getDayNaYin(), ec.getTimeNaYin()); // 八字纳音
        this.baZiXunKong = Arrays.asList(ec.getYearXunKong(), ec.getMonthXunKong(), ec.getDayXunKong(), ec.getTimeXunKong()); // 八字旬空

    }

    /**
     * 计算六爻爻象、六爻爻象标识、六爻爻象标识名称（日期起卦模式）
     */
    private void dateQiGua() {

        /*
            计算方法（以农历计算）↓

                 上卦数：（年数+月数+日数）÷8得出余数，若为0则统一用8表示
                 下卦数：（年数+月数+日数+时数）÷8得出余数，若为0则统一用8表示
                 动爻数：（年数+月数+日数+时数）÷6得出余数，若为0则统一用6表示
         */

        Map<String, Integer> diZhiShu = LiuYaoMap.DI_ZHI_SHU; // 地支对应的数字

        int yearNumber = diZhiShu.get(getYearZhi());  // 年数
        int monthNumber = getLunar().getMonth(); // 月数
        int dayNumber = getLunar().getDay(); // 日数
        int hourNumber = diZhiShu.get(getHourZhi()); // 时数

        // 1、计算上卦数：（年数+月数+日数）÷8得出余数，若除尽则统一用8表示
        int shangGuaNumber = (yearNumber + monthNumber + dayNumber) % 8;
        shangGuaNumber = shangGuaNumber == 0 ? 8 : shangGuaNumber;

        // 2、计算下卦数：（年数+月数+日数+时数）÷8得出余数，若除尽则统一用8表示
        int xiaGuaNumber = (yearNumber + monthNumber + dayNumber + hourNumber) % 8;
        xiaGuaNumber = xiaGuaNumber == 0 ? 8 : xiaGuaNumber;

        // 3、计算动爻数：（年数+月数+日数+时数）÷6得出余数，若除尽则统一用6表示
        int dongYaoNumber = (yearNumber + monthNumber + dayNumber + hourNumber) % 6;
        dongYaoNumber = dongYaoNumber == 0 ? 6 : dongYaoNumber;

        Map<List<Integer>, String> liuShiSiGuaAs = LiuYaoMap.LIU_SHI_SI_GUA_AS; // 六十四卦卦象
        Map<String, List<String>> liuYaoYaoXiang = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_AS; // 六十四卦的六爻爻象
        // 4.1、根据上卦数和下卦数获取本卦卦象
        String benGuaAs = liuShiSiGuaAs.get(Arrays.asList(shangGuaNumber, xiaGuaNumber));
        // 4.2、根据本卦卦象获取六爻爻象
        List<String> liuYaoYaoXiangList = liuYaoYaoXiang.get(benGuaAs);

        // 5、根据动爻数计算六爻标识
        List<String> liuYaoMarkList = CommonUtil.addCharToList(6);
        if ("—".equals(liuYaoYaoXiangList.get(dongYaoNumber - 1))) {
            liuYaoMarkList.set(dongYaoNumber - 1, "○"); // 该爻为阳爻，则在该爻位上添加标识：○
        } else {
            liuYaoMarkList.set(dongYaoNumber - 1, "×"); // 该爻为阴爻，则在该爻位上添加标识：×
        }

        // 6、计算六爻爻象标识名称
        List<String> liuYaoMarkNameList = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            if ("—".equals(liuYaoYaoXiangList.get(i)) && "".equals(liuYaoMarkList.get(i))) {
                liuYaoMarkNameList.add("少阳");
            }
            if ("--".equals(liuYaoYaoXiangList.get(i)) && "".equals(liuYaoMarkList.get(i))) {
                liuYaoMarkNameList.add("少阴");
            }
            if ("—".equals(liuYaoYaoXiangList.get(i)) && "○".equals(liuYaoMarkList.get(i))) {
                liuYaoMarkNameList.add("老阳");
            }
            if ("--".equals(liuYaoYaoXiangList.get(i)) && "×".equals(liuYaoMarkList.get(i))) {
                liuYaoMarkNameList.add("老阴");
            }
        }

        this.liuYaoAs = liuYaoYaoXiangList; // 六爻爻象
        this.liuYaoYaoXiangMark = liuYaoMarkList; // 六爻爻象标识
        this.liuYaoYaoXiangMarkName = liuYaoMarkNameList; // 六爻爻象标识名称

    }

    /**
     * 计算上卦的卦名、卦象
     */
    private void shangGua() {

        // 1、通过[六爻爻象]获取：上卦卦名、上卦卦象
        Map<List<String>, List<String>> map = LiuYaoMap.NAME_AND_AS;

        // 2、设置上卦的卦名、卦象
        this.shangGua = map.get(getLiuYaoAs()).get(0); // 上卦卦名
        this.shangGuaAs = map.get(getLiuYaoAs()).get(1); // 上卦卦象

    }

    /**
     * 计算下卦的卦名、卦象
     */
    private void xiaGua() {

        // 1、通过[六爻爻象]获取：下卦卦名、下卦卦象
        Map<List<String>, List<String>> map = LiuYaoMap.NAME_AND_AS;

        // 2、设置下卦的卦名、卦象
        this.xiaGua = map.get(getLiuYaoAs()).get(2); // 下卦卦名
        this.xiaGuaAs = map.get(getLiuYaoAs()).get(3); // 下卦卦象

    }

    /**
     * 计算本卦的卦名、卦象、六爻爻象、六爻名称、六爻世应、六爻六亲、六爻干支、六爻五行、六爻六神、六爻爻辞
     */
    private void benGua() {

        // 1、六十四卦卦名、六十四卦卦象（六爻爻象为键）
        Map<List<String>, List<String>> sixYaoToSixGuaNameAndAs = LiuYaoMap.NAME_AND_AS;

        // 2、设置本卦的卦名、卦象、爻象
        this.benGua = sixYaoToSixGuaNameAndAs.get(getLiuYaoAs()).get(4); // 本卦卦名
        this.benGuaAs = sixYaoToSixGuaNameAndAs.get(getLiuYaoAs()).get(5); // 本卦卦象
        this.benGuaLiuYaoAs = getLiuYaoAs(); // 本卦爻象（即六爻爻象）

        // 3、设置本卦的六爻藏爻六亲、名称、世应、六亲、干支、五行、六神、爻辞
        this.benGuaCangYaoLiuQin = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_CANG_LIU_QIN.get(getBenGuaAs()); //本卦藏爻六亲
        this.benGuaCangYaoGanZhi = LiuYaoMap.LIU_SHI_SI_GUA_CANG_YAO_GAN_ZHI.get(getBenGuaAs());//本卦藏爻地支
        this.benGuaLiuYaoName = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_YAO_MING.get(getBenGuaAs()); // 名称
        this.benGuaLiuYaoShiYing = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_SHI_YING.get(getBenGuaAs()); // 世应
        this.benGuaLiuYaoLiuQin = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_LIU_QIN.get(getBenGuaAs()); // 六亲
        this.benGuaLiuYaoGanZhi = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_GAN_ZHI.get(getBenGuaAs()); // 干支
        this.benGuaLiuYaoWuXing = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_WU_XING.get(getBenGuaAs()); // 五行
        this.benGuaLiuYaoLiuShen = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_LIU_SHEN.get(getDayGan()); // 六神
        this.benGuaLiuYaoYaoCi = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_YAO_CI.get(getBenGuaAs()); // 爻辞

    }

    /**
     * 计算变卦的卦名、卦象、六爻爻象、六爻名称、六爻世应、六爻六亲、六爻干支、六爻五行、六爻六神、六爻爻辞
     */
    private void bianGua() {

        // 1、六十四卦卦名、六十四卦卦象（六爻爻象为键）
        Map<List<String>, List<String>> sixYaoToSixGuaNameAndAs = LiuYaoMap.NAME_AND_AS;

        // 2、定义数组
        List<String> liuYaoAs = new ArrayList<>(); // 保存六爻爻象（适用于变卦）

        // 3、根据六爻爻象标识判断是否存在变爻，即 × 或 ○
        for (int i = 0; i < getLiuYaoYaoXiangMark().size(); i++) {
            String yaoAs = getLiuYaoAs().get(i); // 六爻爻象
            String yaoMark = getLiuYaoYaoXiangMark().get(i); // 六爻爻象标识
            // 3.1、判断阴阳爻
            if (!"".equals(yaoMark)) {
                if ("—".equals(yaoAs)) {
                    // 3.1.1、阴爻变阳爻
                    liuYaoAs.add(i, "--");
                } else {
                    // 3.1.2、阴爻变阳爻
                    liuYaoAs.add(i, "—");
                }
            } else {
                liuYaoAs.add(i, getLiuYaoAs().get(i));
            }
        }

        // 4、设置变卦的卦名、卦象、爻象
        this.bianGua = sixYaoToSixGuaNameAndAs.get(liuYaoAs).get(4); // 变卦卦名
        this.bianGuaAs = sixYaoToSixGuaNameAndAs.get(liuYaoAs).get(5); // 变卦卦象
        this.bianGuaLiuYaoAs = liuYaoAs; // 变卦爻象

        // 5、设置变卦的六爻名称、世应、六亲、干支、五行、六神、爻辞
        this.bianGuaLiuYaoName = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_YAO_MING.get(getBianGuaAs()); // 名称
        this.bianGuaLiuYaoShiYing = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_SHI_YING.get(getBianGuaAs()); // 世应
        this.bianGuaLiuYaoLiuQin = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_LIU_QIN.get(getBianGuaAs()); // 六亲
        this.bianGuaLiuYaoGanZhi = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_GAN_ZHI.get(getBianGuaAs()); // 干支
        this.bianGuaLiuYaoWuXing = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_WU_XING.get(getBianGuaAs()); // 五行
        this.bianGuaLiuYaoLiuShen = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_LIU_SHEN.get(getDayGan()); // 六神
        this.bianGuaLiuYaoYaoCi = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_YAO_CI.get(getBianGuaAs()); // 爻辞

    }

    /**
     * 计算互卦的卦象、卦名、六爻爻象、六爻名称、六爻世应、六爻六亲、六爻干支、六爻五行、六爻六神、六爻爻辞
     */
    private void huGua() {

        // 设置互卦的卦象、卦名、六爻爻象、名称、世应、六亲、干支、五行、六神、爻辞
        this.huGuaAs = LiuYaoMap.HU_CUO_ZONG.get(getBenGuaAs()).get(0); // 互卦卦象
        this.huGua = LiuYaoMap.LIU_SHI_SI_GUA.get(getHuGuaAs()); // 互卦卦名
        this.huGuaLiuYaoAs = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_AS.get(getHuGuaAs()); // 爻象
        this.huGuaLiuYaoName = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_YAO_MING.get(getHuGuaAs()); // 名称
        this.huGuaLiuYaoShiYing = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_SHI_YING.get(getHuGuaAs()); // 世应
        this.huGuaLiuYaoLiuQin = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_LIU_QIN.get(getHuGuaAs()); // 六亲
        this.huGuaLiuYaoGanZhi = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_GAN_ZHI.get(getHuGuaAs()); // 干支
        this.huGuaLiuYaoWuXing = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_WU_XING.get(getHuGuaAs()); // 五行
        this.huGuaLiuYaoLiuShen = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_LIU_SHEN.get(getDayGan()); // 六神
        this.huGuaLiuYaoYaoCi = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_YAO_CI.get(getHuGuaAs()); // 爻辞

    }

    /**
     * 计算错卦的卦象、卦名、六爻爻象、六爻名称、六爻世应、六爻六亲、六爻干支、六爻五行、六爻六神、六爻爻辞
     */
    private void cuoGua() {

        // 设置错卦的卦象、卦名、六爻爻象、名称、世应、六亲、干支、五行、六神、爻辞
        this.cuoGuaAs = LiuYaoMap.HU_CUO_ZONG.get(getBenGuaAs()).get(1); // 错卦卦象
        this.cuoGua = LiuYaoMap.LIU_SHI_SI_GUA.get(getCuoGuaAs()); // 错卦卦名
        this.cuoGuaLiuYaoAs = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_AS.get(getCuoGuaAs()); // 爻象
        this.cuoGuaLiuYaoName = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_YAO_MING.get(getCuoGuaAs()); // 名称
        this.cuoGuaLiuYaoShiYing = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_SHI_YING.get(getCuoGuaAs()); // 世应
        this.cuoGuaLiuYaoLiuQin = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_LIU_QIN.get(getCuoGuaAs()); // 六亲
        this.cuoGuaLiuYaoGanZhi = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_GAN_ZHI.get(getCuoGuaAs()); // 干支
        this.cuoGuaLiuYaoWuXing = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_WU_XING.get(getCuoGuaAs()); // 五行
        this.cuoGuaLiuYaoLiuShen = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_LIU_SHEN.get(getDayGan()); // 六神
        this.cuoGuaLiuYaoYaoCi = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_YAO_CI.get(getCuoGuaAs()); // 爻辞

    }

    /**
     * 计算综卦的卦象、卦名、六爻爻象、六爻名称、六爻世应、六爻六亲、六爻干支、六爻五行、六爻六神、六爻爻辞
     */
    private void zongGua() {

        // 设置综卦的卦象、卦名、六爻爻象、名称、世应、六亲、干支、五行、六神、爻辞
        this.zongGuaAs = LiuYaoMap.HU_CUO_ZONG.get(getBenGuaAs()).get(2); // 综卦卦象
        this.zongGua = LiuYaoMap.LIU_SHI_SI_GUA.get(getZongGuaAs()); // 综卦卦名
        this.zongGuaLiuYaoAs = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_AS.get(getZongGuaAs()); // 爻象
        this.zongGuaLiuYaoName = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_YAO_MING.get(getZongGuaAs()); // 名称
        this.zongGuaLiuYaoShiYing = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_SHI_YING.get(getZongGuaAs()); // 世应
        this.zongGuaLiuYaoLiuQin = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_LIU_QIN.get(getZongGuaAs()); // 六亲
        this.zongGuaLiuYaoGanZhi = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_GAN_ZHI.get(getZongGuaAs()); // 干支
        this.zongGuaLiuYaoWuXing = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_WU_XING.get(getZongGuaAs()); // 五行
        this.zongGuaLiuYaoLiuShen = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_LIU_SHEN.get(getDayGan()); // 六神
        this.zongGuaLiuYaoYaoCi = LiuYaoMap.LIU_SHI_SI_GUA_LIU_YAO_YAO_CI.get(getZongGuaAs()); // 爻辞

    }

    /**
     * 计算本卦、变卦、互卦、错卦、综卦的卦辞
     */
    private void guaCi() {

        Map<String, String> liuShiSiGuaGuaCi = LiuYaoMap.LIU_SHI_SI_GUA_GUA_CI; // 六十四卦卦辞

        this.benGuaGuaCi = liuShiSiGuaGuaCi.get(getBenGua()); // 本卦卦辞
        this.bianGuaGuaCi = liuShiSiGuaGuaCi.get(getBianGua()); // 变卦卦辞
        this.huGuaGuaCi = liuShiSiGuaGuaCi.get(getHuGua()); // 互卦卦辞
        this.cuoGuaGuaCi = liuShiSiGuaGuaCi.get(getCuoGua()); // 错卦卦辞
        this.zongGuaGuaCi = liuShiSiGuaGuaCi.get(getZongGua()); // 综卦卦辞

    }

    /**
     * 计算驿马
     */
    private void yiMa() {

        this.yiMa = LiuYaoMap.YI_MA.get(getHourZhi());

    }

    /**
     * 计算天马
     */
    private void tianMa() {

        this.tianMa = LiuYaoMap.TIAN_MA.get(getMonthZhi());

    }

    /**
     * 计算天元禄
     */
    private void tianYuanLu() {

        this.tianYuanLu = LiuYaoMap.TIAN_YUAN_LU.get(getDayGan());

    }

    /**
     * 计算天乙贵人
     */
    private void tianYiGuiRen() {

        this.tianYiGuiRen = LiuYaoMap.TIAN_YI_GUI_REN.get(getDayGan());

    }

    /**
     * 计算太极贵人
     */
    private void taiJiGuiRen() {

        List<String> yearZhi = LiuYaoMap.TAI_JI_GUI_REN.get(getYearGan()); // 根据年干获取太极贵人所要落入的地支
        List<String> dayZhi = LiuYaoMap.TAI_JI_GUI_REN.get(getDayGan()); // 根据日干获取太极贵人所要落入的地支
        this.taiJiGuiRen = returnShenSha(yearZhi, dayZhi);

    }

    /**
     * 计算天德贵人
     */
    private void tianDeGuiRen() {

        this.tianDeGuiRen = LiuYaoMap.TIAN_DE_GUI_REN.get(getMonthZhi());

    }

    /**
     * 计算月德贵人
     */
    private void yueDeGuiRen() {

        this.yueDeGuiRen = LiuYaoMap.YUE_DE_GUI_REN.get(getMonthZhi());

    }

    /**
     * 计算唐符国印
     */
    private void tangFuGuoYin() {

        this.tangFuGuoYin = LiuYaoMap.TANG_FU_GUO_YIN.get(getYearZhi());

    }

    /**
     * 计算咸池（桃花）
     */
    private void xianChi() {

        this.xianChi = LiuYaoMap.XIAN_CHI.get(getDayZhi());

    }

    /**
     * 计算天喜
     */
    private void tianXi() {

        this.tianXi = LiuYaoMap.TIAN_XI.get(getMonthZhi());

    }

    /**
     * 计算皇恩
     */
    private void huangEn() {

        this.huangEn = LiuYaoMap.HUANG_EN.get(getMonthZhi());

    }

    /**
     * 计算文昌
     */
    private void wenChang() {

        this.wenChang = LiuYaoMap.WEN_CHANG.get(getDayGan());

    }

    /**
     * 计算华盖
     */
    private void huaGai() {

        List<String> yearZhi = LiuYaoMap.HUA_GAI.get(getYearZhi()); // 根据年干获取华盖所要落入的地支
        List<String> dayZhi = LiuYaoMap.HUA_GAI.get(getDayZhi()); // 根据日干获取华盖所要落入的地支
        this.huaGai = returnShenSha(yearZhi, dayZhi);

    }

    /**
     * 计算将星
     */
    private void jiangXing() {

        List<String> yearZhi = LiuYaoMap.JIANG_XING.get(getYearZhi()); // 根据年干获取将星所要落入的地支
        List<String> dayZhi = LiuYaoMap.JIANG_XING.get(getDayZhi()); // 根据日干获取将星所要落入的地支
        this.jiangXing = returnShenSha(yearZhi, dayZhi);

    }

    /**
     * 计算灾煞
     */
    private void zaiSha() {

        this.zaiSha = LiuYaoMap.ZAI_SHA.get(getDayZhi());

    }

    /**
     * 计算劫煞
     */
    private void jieSha() {

        this.jieSha = LiuYaoMap.JIE_SHA.get(getDayZhi());

    }

    /**
     * 计算谋星
     */
    private void mouXing() {

        this.mouXing = LiuYaoMap.MOU_XING.get(getDayZhi());

    }

    /**
     * 计算天医
     */
    private void tianYi() {

        this.tianYi = LiuYaoMap.TIAN_YI.get(getMonthZhi());

    }

    /**
     * 断卦
     */
    private void duanGua() {
        if (this.occupy == null || this.occupy.isEmpty()) {
            this.duangua = "未指定占事";
            return;
        }
        // 1. 计算本卦信息
        calculateBenGuaInformation();

        String summary;
        switch (this.occupy) {
            case "考学":
                summary = determineKaoXueOutcome();
                break;
            case "财运":
                summary = determineCaiYunOutcome();
                break;
            case "男测感情":
                summary = determineLoveOutcome(true);
                break;
            case "女测感情":
                summary = determineLoveOutcome(false);
                break;
            case "出行":
                summary = determineTravelOutcome();
                break;
            default:
                summary = "暂不支持的占事类型";
                break;
        }
        this.duangua = summary; // 统一在此处写回
    }

    /**
     * 计算本卦内部信息 - 生成benguainformation数据
     *
     * 此方法为每一爻计算10个详细字段的信息，用于断卦分析和AI分析
     * 计算顺序：初爻(0) -> 二爻(1) -> 三爻(2) -> 四爻(3) -> 五爻(4) -> 上爻(5)
     *
     * 计算内容包括：
     * 1. 动静判定：基于爻象标记(○×)和日冲规则
     * 2. 化状态分析：动爻变化后的生克制化关系
     * 3. 旬空判定：基于日干支计算的旬空地支
     * 4. 伏吟判定：本卦与变卦地支是否相同
     * 5. 化进退：变化方向的吉凶判定
     * 6. 世应位置：从卦象映射表获取
     * 7. 六亲关系：从卦象映射表获取
     * 8. 地支五行：从干支中提取和转换
     * 9. 旺衰计算：综合月日对地支的生克影响
     */
    private void calculateBenGuaInformation() {
        this.benguainformation = new ArrayList<>();
        this.liuYaoWangShuaiPercent = new ArrayList<>(6);
        String monthZhi = getMonthGanZhi().substring(1);
        String dayZhi = getDayGanZhi().substring(1);
        String dayXunKongStr = getBaZiXunKong().get(2);
        String[] dayXunKong = new String[]{dayXunKongStr.substring(0, 1), dayXunKongStr.substring(1, 2)};

        // Check if there are any moving lines. If not, bianGua lists can be null.
        boolean hasMovingLines = false;
        for (String mark : getLiuYaoYaoXiangMark()) {
            if ("○".equals(mark) || "×".equals(mark)) {
                hasMovingLines = true;
                break;
            }
        }

        // 遍历六爻，为每一爻计算详细信息
        for (int i = 0; i < 6; i++) {
            List<String> yaoInfo = new ArrayList<>(); // 存储当前爻的10个字段信息
            String benGuaYaoZhi = getBenGuaLiuYaoGanZhi().get(i).substring(1); // 提取本卦该爻的地支

            // 1. 计算旺衰得分（月支 + 日支 对当前爻地支的生克制化评分）
            // 得分范围：-40到+30，用于判断该爻的强弱状态
            int wangShuaiScore = calcWangShuaiScore(benGuaYaoZhi);

            // 默认变卦地支与本卦相同（静爻情况）
            String bianGuaYaoZhi = benGuaYaoZhi;

            // 2. 判定动爻的三种情况：
            // - 传统标记：○(老阳动) 或 ×(老阴动)
            // - 新增规则：与日支相冲且旺衰≥0（暗动）
            boolean isDongYao = "○".equals(getLiuYaoYaoXiangMark().get(i)) ||
                                "×".equals(getLiuYaoYaoXiangMark().get(i)) ||
                                (benGuaYaoZhi.equals(LiuYaoMap.DI_ZHI_XIANG_CHONG.get(dayZhi)) && wangShuaiScore >= 0);

            // 如果是动爻且存在变卦信息，则获取变卦该爻的地支
            if (isDongYao && hasMovingLines) {
                 // 只有在确实存在动爻时才访问变卦信息
                if (getBianGuaLiuYaoGanZhi() != null) {
                    bianGuaYaoZhi = getBianGuaLiuYaoGanZhi().get(i).substring(1);
                }
            }

            // 3. 字段[0]：动静状态
            String dongYao = isDongYao ? "动爻" : "静爻";
            yaoInfo.add(dongYao);

            // 4. 字段[1]：化状态分析（仅对动爻有效）
            String huaState = "";
            if (dongYao.equals("动爻") && hasMovingLines && getBianGuaLiuYaoWuXing() != null) {
                String benGuaWuXing = getBenGuaLiuYaoWuXing().get(i);   // 本卦该爻五行
                String bianGuaWuXing = getBianGuaLiuYaoWuXing().get(i); // 变卦该爻五行

                // 判断化回头生克关系
                if (benGuaWuXing.equals(LiuYaoMap.WU_XING_XIANG_SHENG.get(bianGuaWuXing))) {
                    huaState = "化回头生"; // 变爻五行生本爻五行，为吉
                } else if (benGuaWuXing.equals(LiuYaoMap.WU_XING_XIANG_KE.get(bianGuaWuXing))) {
                    huaState = "化回头克"; // 变爻五行克本爻五行，为凶
                }

                // 判断化废：变爻地支被月支冲破
                if (bianGuaYaoZhi.equals(LiuYaoMap.DI_ZHI_XIANG_CHONG.get(monthZhi))) {
                    huaState = "化废";
                }
                // 判断化废：变爻地支被日支冲破且月日组合旺衰为负
                else if (bianGuaYaoZhi.equals(LiuYaoMap.DI_ZHI_XIANG_CHONG.get(dayZhi))) {
                    String yueDayKey = monthZhi + dayZhi;
                    // 注意：此处代码可能有类型不匹配问题，需要检查LiuYaoMap.YUE_YAO_WANG_SHUAI的键类型
                    if (LiuYaoMap.YUE_YAO_WANG_SHUAI.containsKey(yueDayKey) && LiuYaoMap.YUE_YAO_WANG_SHUAI.get(yueDayKey) < 0) {
                        huaState = "化废";
                    }
                }

                // 判断化空：变爻地支落入日旬空
                if (bianGuaYaoZhi.equals(dayXunKong[0]) || bianGuaYaoZhi.equals(dayXunKong[1])) {
                    huaState = "化空";
                }
            }
            yaoInfo.add(huaState);

            // 5. 字段[2]：旬空状态判定
            // 根据日干支计算的旬空地支，判断该爻是否落空
            String xunKong = (benGuaYaoZhi.equals(dayXunKong[0]) || benGuaYaoZhi.equals(dayXunKong[1])) ? "旬空" : "非空";
            yaoInfo.add(xunKong);

            // 6. 字段[3]：伏吟状态判定
            // 伏吟：动而不变，本卦地支与变卦地支相同
            String fuYin = benGuaYaoZhi.equals(bianGuaYaoZhi) ? "伏吟" : "";
            yaoInfo.add(fuYin);

            // 7. 字段[4]：化进退判定（仅对动爻有效）
            String jinTui = "";
            if (isDongYao && hasMovingLines) {
                // 化进：变化为长生方向，主吉利发展
                if (bianGuaYaoZhi.equals(LiuYaoMap.DI_ZHI_HUA_JIN_SHEN.get(benGuaYaoZhi))) {
                    jinTui = "化进";
                }
                // 化退：变化为死墓方向，主衰退不利
                else if (bianGuaYaoZhi.equals(LiuYaoMap.DI_ZHI_HUA_TUI_SHEN.get(benGuaYaoZhi))) {
                    jinTui = "化退";
                }
            }
            yaoInfo.add(jinTui);

            // 8. 字段[5]：世应位置
            // 从卦象映射表获取，"世"代表求测者，"应"代表所测事物
            yaoInfo.add(getBenGuaLiuYaoShiYing().get(i));

            // 9. 字段[6]：六亲关系
            // 从卦象映射表获取，表示该爻在六亲体系中的角色
            yaoInfo.add(getBenGuaLiuYaoLiuQin().get(i));

            // 10. 字段[7]：地支
            // 十二地支之一，是该爻的核心标识
            yaoInfo.add(benGuaYaoZhi);

            // 11. 字段[8]：五行属性
            // 从卦象映射表获取，用于生克制化分析
            yaoInfo.add(getBenGuaLiuYaoWuXing().get(i));

            // 12. 字段[9]：旺衰百分比
            // 将旺衰得分(-40到+30)转换为百分比(0%到100%)
            // 公式：((得分 + 30) / 70) * 100
            double percent = ((double) (wangShuaiScore + 30) / 70) * 100;
            String percentStr = String.format("%.0f%%", percent);
            yaoInfo.add(percentStr);
            // 同时记录到汇总列表供其他方法使用
            this.liuYaoWangShuaiPercent.add(percentStr);

            // 将当前爻的完整信息添加到本卦内部信息列表
            this.benguainformation.add(yaoInfo);
        }
    }

    /**
     * 计算变卦内部信息 - 生成bianguainformation数据
     *
     * TODO: 此方法尚未实现，需要为变卦计算与benguainformation相同结构的详细信息
     *
     * 实现要点：
     * 1. 应该在bianGua()方法调用后执行
     * 2. 数据结构与benguainformation完全相同
     * 3. 主要分析变卦六爻的状态，用于预测事物发展趋势
     * 4. 动静状态：变卦中一般不再有动爻，都是静爻
     * 5. 化状态：变卦作为结果状态，不再有化的概念
     * 6. 旬空、世应、六亲、地支、五行：从变卦映射表获取
     * 7. 旺衰计算：基于相同的月日对变卦地支的影响
     *
     * 调用位置建议：在duanGua()方法中，calculateBenGuaInformation()之后调用
     */
    private void calculateBianGuaInformation() {
        // TODO: 实现变卦内部信息计算
        // this.bianguainformation = new ArrayList<>();
        // ... 具体实现逻辑
    }

    /**
     * "考学"模式的断卦逻辑
     */
    private String determineKaoXueOutcome() {
        // 重置批语，采用卫语句提前返回，避免深嵌套
        this.analyzeKaoXue = "";
        if (this.benguainformation == null || this.benguainformation.size() != 6) {
            this.analyzeKaoXue = "资料不完整，无法断卦";
            return buildResult("资料不完整");
        }

        // 0. 是否存在动爻
        boolean hasMoving = hasMovingLines();

        // 1. 找父母爻位置
        List<Integer> parentIdxList = findPositionsByKin(this.benGuaLiuYaoLiuQin, "父母");
        int yongIdx = selectYongShen(parentIdxList);
        if (yongIdx == -1) {
            // 在藏爻中寻找父母爻
            int hiddenIdx = -1;
            for(int i=0;i<benGuaCangYaoLiuQin.size();i++){
                if("父母".equals(benGuaCangYaoLiuQin.get(i))){hiddenIdx=i;break;}
            }
            if(hiddenIdx!=-1){
                // 计算藏爻旺衰
                String hiddenGanZhi = benGuaCangYaoGanZhi.get(hiddenIdx);
                String hiddenZhi = hiddenGanZhi.substring(1);
                int hiddenScore = calcWangShuaiScore(hiddenZhi);

                int shiIdxTmp = this.benGuaLiuYaoShiYing.indexOf("世");
                String shiZhiTmp = (shiIdxTmp >= 0) ? this.benGuaLiuYaoGanZhi.get(shiIdxTmp).substring(1) : "";
                int shiScore = (shiIdxTmp >= 0) ? calcWangShuaiScore(shiZhiTmp) : 0;

                String mainWu = this.benGuaLiuYaoWuXing.get(hiddenIdx);
                String hiddenWu = DI_ZHI_TO_WU_XING.getOrDefault(hiddenZhi,"");

                boolean mainKeHidden = mainWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(hiddenWu));

                if(hiddenScore>0 && shiScore>0 && !mainKeHidden){
                    this.analyzeKaoXue="用神藏于藏爻且力量良好，断吉。";
                    return buildResult("吉");
                }else{
                    this.analyzeKaoXue="用神藏爻力量不足，断凶。";
                    return buildResult("凶");
                }
            }

            this.analyzeKaoXue = "用神缺失，恐难取得好成绩。";
            return buildResult("凶");
        }

        // 2. 静卦 or 动卦
        if (!hasMoving) {
            analyzeStaticKaoXue(yongIdx);
            return buildResult(this.duangua);
        }

        // 3. 动卦快速通道
        if (analyzeDynamicFastPath()) return buildResult(this.duangua);

        // 4. 动卦一般分析
        analyzeDynamicGeneral(yongIdx);
        return buildResult(this.duangua);
    }

    /**
     * "姻缘"模式的断卦逻辑
     * @param maleAsk true=男测感情; false=女测感情
     */
    private String determineLoveOutcome(boolean maleAsk) {
        // 重置批语
        this.analyzeLove = "";
        if (this.benguainformation == null || this.benguainformation.size() != 6) {
            this.analyzeLove = "资料不完整，无法断卦";
            return buildLoveResult("资料不完整");
        }

        // 1. 用神类型
        String yongKin = maleAsk ? "妻财" : "官鬼";

        // 2. 寻找用神位置
        List<Integer> yongPos = findPositionsByKin(this.benGuaLiuYaoLiuQin, yongKin);
        int yongIdx = selectYongShen(yongPos);

        if (yongIdx == -1) {
            // 不在明爻，检查藏爻
            for (int i = 0; i < this.benGuaCangYaoLiuQin.size(); i++) {
                if (yongKin.equals(this.benGuaCangYaoLiuQin.get(i))) {
                    this.analyzeLove = "用神藏于藏爻，双方关系或有疏远。";
                    return buildLoveResult("平");
                }
            }
            this.analyzeLove = "用神缺失，难成。";
            return buildLoveResult("凶");
        }

        boolean hasMoving = hasMovingLines();

        StringBuilder sb = new StringBuilder();
        String grade = "平";

        // 3. 基本旺衰与五行关系
        int yongScore = calcWangShuaiScore(this.benGuaLiuYaoGanZhi.get(yongIdx).substring(1));
        int shiIdx = this.benGuaLiuYaoShiYing.indexOf("世");
        int shiScore = (shiIdx >= 0) ? calcWangShuaiScore(this.benGuaLiuYaoGanZhi.get(shiIdx).substring(1)) : 0;

        String yongWu = this.benGuaLiuYaoWuXing.get(yongIdx);
        String shiWu = (shiIdx >= 0) ? this.benGuaLiuYaoWuXing.get(shiIdx) : "";

        // ★ 若世爻既不生也不克用神，且用神也不生克世爻，则改用应爻的六亲为新用神
        boolean shiShengYongTmp = yongWu.equals(LiuYaoMap.WU_XING_XIANG_SHENG.get(shiWu));
        boolean shiKeYongTmp   = yongWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(shiWu));
        boolean yongShengShiTmp = shiWu.equals(LiuYaoMap.WU_XING_XIANG_SHENG.get(yongWu));
        boolean yongKeShiTmp    = shiWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(yongWu));

        if(!shiShengYongTmp && !shiKeYongTmp && !yongShengShiTmp && !yongKeShiTmp){
            int yingIdx = this.benGuaLiuYaoShiYing.indexOf("应");
            if(yingIdx>=0){
                yongIdx = yingIdx;
                yongKin = this.benGuaLiuYaoLiuQin.get(yingIdx);
                yongScore = calcWangShuaiScore(this.benGuaLiuYaoGanZhi.get(yingIdx).substring(1));
                yongWu = this.benGuaLiuYaoWuXing.get(yingIdx);
                sb.append("世与原用神五行既不生也不克，改以应爻的").append(yongKin).append("为用神。");
            }
        }

        // 用神旺且不克冲世 → 吉
        boolean clashOrKe = yongWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(shiWu)) ||
                            yongWu.equals(LiuYaoMap.DI_ZHI_XIANG_CHONG.get(shiWu));

        if (yongScore >= 0 && !clashOrKe) {
            grade = "吉";
            sb.append("用神旺且不克世，两人易成。");
        }

        // 用神弱且克/冲世 → 凶
        if (yongScore < 0 && clashOrKe) {
            grade = "凶";
            sb.append("用神弱且克/冲世，不易成。");
        }

        // 用神被克(世五行克用五行)
        if (shiWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(yongWu))) {
            grade = "凶";
            sb.append("世克用神，相性不佳。");
        }

        // 用神合世
        if (yongWu.equals(shiWu)) {
            if (!"吉".equals(grade)) grade = "平";
            sb.append("用神与世同五行，相合可期。");
        }

        // 4. 动爻相关规则
        int movingCount = 0;
        for (int i = 0; i < 6; i++) {
            if ("动爻".equals(this.benguainformation.get(i).get(1))) {
                movingCount++;
                // 动爻生用神
                String moveWu = this.benGuaLiuYaoWuXing.get(i);
                if (yongWu.equals(LiuYaoMap.WU_XING_XIANG_SHENG.get(moveWu))) {
                    sb.append("动爻生用神，可能存在竞争者。");
                    if (!"凶".equals(grade)) grade = "平";
                }
            }
        }
        if (movingCount >= 4) {
            sb.append("动爻过多，关系不稳。");
            if (!"凶".equals(grade)) grade = "平";
        }

        this.analyzeLove = sb.toString();
        return buildLoveResult(grade);
    }

    /**
     * "出行"模式的断卦逻辑
     * 规则来源：docs/断卦_出行规则.md
     */
    private String determineTravelOutcome() {
        this.analyzeTravel = "";
        // 基本校验
        if (this.benguainformation == null || this.benguainformation.size() != 6) {
            this.analyzeTravel = "资料不完整，无法断卦";
            return buildTravelResult("资料不完整");
        }

        StringBuilder sb = new StringBuilder();
        String grade = "平";

        // 计算常用索引及五行/评分
        int shiIdx = this.benGuaLiuYaoShiYing.indexOf("世");
        int yingIdx = this.benGuaLiuYaoShiYing.indexOf("应");

        // 若世或应不存在，直接返回资料不足
        if (shiIdx < 0 || yingIdx < 0) {
            this.analyzeTravel = "缺失世/应信息，无法断卦";
            return buildTravelResult("资料不完整");
        }

        String shiZhi = this.benGuaLiuYaoGanZhi.get(shiIdx).substring(1);
        String yingZhi = this.benGuaLiuYaoGanZhi.get(yingIdx).substring(1);

        int shiScore = calcWangShuaiScore(shiZhi);
        int yingScore = calcWangShuaiScore(yingZhi);

        String shiWu = this.benGuaLiuYaoWuXing.get(shiIdx);
        String yingWu = this.benGuaLiuYaoWuXing.get(yingIdx);

        /* --------------- 规则1 应爻衰 --------------- */
        if (yingScore < 0) {
            sb.append("应爻衰卦主去目的地可能不顺。");
            if (!"凶".equals(grade)) grade = "平"; // 不至于直接判凶
        }

        /* --------------- 规则2 父母旺克世 --------------- */
        List<Integer> fuMuIdxs = findPositionsByKin(this.benGuaLiuYaoLiuQin, "父母");
        for (int idx : fuMuIdxs) {
            String fuZhi = this.benGuaLiuYaoGanZhi.get(idx).substring(1);
            int fuScore = calcWangShuaiScore(fuZhi);
            String fuWu = this.benGuaLiuYaoWuXing.get(idx);
            boolean fuKeShi = fuWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(shiWu));
            if (fuScore > 0 && shiScore < 0 && fuKeShi) {
                sb.append("父母旺相又克世爻，世爻衰需防有意外凶灾。");
                grade = "凶";
                break;
            }
        }

        /* --------------- 规则3 妻财克世 --------------- */
        List<Integer> qiCaiIdxs = findPositionsByKin(this.benGuaLiuYaoLiuQin, "妻财");
        for (int idx : qiCaiIdxs) {
            String qiWu = this.benGuaLiuYaoWuXing.get(idx);
            boolean qiKeShi = qiWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(shiWu));
            if (qiKeShi) {
                sb.append("须防因食物问题、财务问题或女人影响。");
                if (!"凶".equals(grade)) grade = "平";
                break;
            }
        }

        /* --------------- 规则4 官鬼旺克世 --------------- */
        List<Integer> guanGuiIdxs = findPositionsByKin(this.benGuaLiuYaoLiuQin, "官鬼");
        for (int idx : guanGuiIdxs) {
            String ggZhi = this.benGuaLiuYaoGanZhi.get(idx).substring(1);
            int ggScore = calcWangShuaiScore(ggZhi);
            String ggWu = this.benGuaLiuYaoWuXing.get(idx);
            boolean ggKeShi = ggWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(shiWu));
            if (ggKeShi && ggScore > 0 && shiScore < 0) {
                sb.append("可能有大凶之象，需防人身安全，重大疾病等。");
                grade = "凶";
                break;
            }
        }

        /* --------------- 规则5 子孙克世 --------------- */
        List<Integer> ziSunIdxs = findPositionsByKin(this.benGuaLiuYaoLiuQin, "子孙");
        for (int idx : ziSunIdxs) {
            String zsWu = this.benGuaLiuYaoWuXing.get(idx);
            boolean zsKeShi = zsWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(shiWu));
            if (zsKeShi) {
                sb.append("可能为受小儿影响，或娱乐游玩时有灾。");
                if (!"凶".equals(grade)) grade = "平";
                break;
            }
        }

        /* --------------- 规则6 世爻旬空 --------------- */
        String shiVoid = this.benguainformation.get(shiIdx).get(2); // 旬空/非空
        if ("旬空".equals(shiVoid)) {
            sb.append("世爻旬空可能意指卦主内心不安。");
            if (!"凶".equals(grade)) grade = "平";
        }

        /* --------------- 规则7 间爻动且克世 --------------- */
        int minIdx = Math.min(shiIdx, yingIdx);
        int maxIdx = Math.max(shiIdx, yingIdx);
        for (int idx = minIdx + 1; idx < maxIdx; idx++) {
            String dong = this.benguainformation.get(idx).get(0);
            if (!"动爻".equals(dong)) continue;
            String jianWu = this.benGuaLiuYaoWuXing.get(idx);
            boolean jianKeShi = jianWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(shiWu));
            if (jianKeShi) {
                sb.append("不是受同行拖累，就是中途有阻。");
                if (!"凶".equals(grade)) grade = "平";
                break;
            }
        }

        /* --------------- 规则8 合象繁多 --------------- */
        boolean hasHe = false;
        // 与月/日合
        String monthZhi = getMonthGanZhi().substring(1);
        String dayZhi = getDayGanZhi().substring(1);
        if (shiZhi.equals(LiuYaoMap.DI_ZHI_XIANG_HE.get(monthZhi)) || shiZhi.equals(LiuYaoMap.DI_ZHI_XIANG_HE.get(dayZhi))) {
            hasHe = true;
        }
        // 与间爻或其他动爻合
        for (int i = 0; i < 6 && !hasHe; i++) {
            if (i == shiIdx) continue;
            String otherZhi = this.benGuaLiuYaoGanZhi.get(i).substring(1);
            if (shiZhi.equals(LiuYaoMap.DI_ZHI_XIANG_HE.get(otherZhi))) {
                hasHe = true; break;
            }
            // 与变卦同位合
            if (this.bianGuaLiuYaoGanZhi != null) {
                String bianZhi = this.bianGuaLiuYaoGanZhi.get(i).substring(1);
                if (shiZhi.equals(LiuYaoMap.DI_ZHI_XIANG_HE.get(bianZhi))) { hasHe = true; break; }
            }
        }
        if (hasHe) {
            sb.append("卦主可能被琐事缠身。");
            if (!"凶".equals(grade)) grade = "平";
        }

        /* --------------- 规则9 世爻伏吟 --------------- */
        if ("伏吟".equals(this.benguainformation.get(shiIdx).get(3))) {
            sb.append("世爻伏吟心有忧虑。");
            if (!"凶".equals(grade)) grade = "平";
        }

        /* --------------- 新规则 应爻旺且不被克 --------------- */
        if (yingScore >= 0) {
            boolean yingBeKe = false;

            // 月、日克应?
            String monthWu = DI_ZHI_TO_WU_XING.getOrDefault(monthZhi, "");
            String dayWu   = DI_ZHI_TO_WU_XING.getOrDefault(dayZhi  , "");
            if (yingWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(monthWu))) yingBeKe = true;
            if (yingWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(dayWu)))   yingBeKe = true;

            // 动爻克应?
            for (int i = 0; i < 6 && !yingBeKe; i++) {
                if ("动爻".equals(this.benguainformation.get(i).get(0)) && i != yingIdx) {
                    String moveWu = this.benGuaLiuYaoWuXing.get(i);
                    if (yingWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(moveWu))) {
                        yingBeKe = true; break;
                    }
                }
            }

            // 变卦同位爻克应?
            if (!yingBeKe && this.bianGuaLiuYaoGanZhi != null) {
                String bianZhi = this.bianGuaLiuYaoGanZhi.get(yingIdx).substring(1);
                String bianWu  = DI_ZHI_TO_WU_XING.getOrDefault(bianZhi, "");
                if (yingWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(bianWu))) yingBeKe = true;
            }

            if (!yingBeKe) {
                sb.append("应爻较旺，此去目的地较为顺利。");
                if (!"凶".equals(grade)) grade = "吉";
            }
        }

        this.analyzeTravel = sb.toString();
        return buildTravelResult(grade);
    }

    /* -------------------- 出行 buildResult -------------------- */
    private String buildTravelResult(String grade) {
        if (this.analyzeTravel == null || this.analyzeTravel.isEmpty()) return grade;
        return grade + "\n" + this.analyzeTravel;
    }

    /**
     * 将吉凶等级与详细批语组合
     */
    private String buildResult(String grade){
        if(this.analyzeKaoXue==null||this.analyzeKaoXue.isEmpty()) return grade;
        return grade+"\n"+this.analyzeKaoXue;
    }

    /* ===================== 姻缘结果辅助 ===================== */

    private String buildLoveResult(String grade){
        if(this.analyzeLove==null||this.analyzeLove.isEmpty()) return grade;
        return grade+"\n"+this.analyzeLove;
    }

    /* ===================== 财运结果辅助 ===================== */

    private String buildCaiYunResult(String grade){
        if(this.analyzeCaiYun==null||this.analyzeCaiYun.isEmpty()) return grade;
        return grade+"\n"+this.analyzeCaiYun;
    }

    /**
     * "财运"模式的断卦逻辑
     */
    private String determineCaiYunOutcome(){

        this.analyzeCaiYun="";
        if(this.benguainformation==null||this.benguainformation.size()!=6){
            this.analyzeCaiYun="资料不完整，无法断卦";
            return buildCaiYunResult("资料不完整");
        }

        boolean hasMoving = hasMovingLines();
        StringBuilder sb = new StringBuilder();
        String grade = "平";

        /* --------------- 子孙爻发动全局规则 --------------- */
        boolean zisunActiveFound=false;
        boolean zisunActiveGood=false;
        for(int i=0;i<6;i++){
            if(!"子孙".equals(this.benGuaLiuYaoLiuQin.get(i))) continue;
            String dong=this.benguainformation.get(i).get(0);
            if(!"动爻".equals(dong)) continue;
            zisunActiveFound=true;
            String hua=this.benguainformation.get(i).get(1);
            String xk=this.benguainformation.get(i).get(2);
            if(!"化废".equals(hua) && !"旬空".equals(xk)) zisunActiveGood=true;
        }
        if(zisunActiveFound){
            if(zisunActiveGood){
                sb.append("子孙爻发动生财，有客前来生财。\n");
                if(!"凶".equals(grade)) grade="吉";
            }else{
                sb.append("原神受伤，不利求财。\n");
                if(!"吉".equals(grade)) grade="凶";
            }
        }

        /* 常用索引 */
        int shiIdx=this.benGuaLiuYaoShiYing.indexOf("世");
        int yingIdx=this.benGuaLiuYaoShiYing.indexOf("应");

        java.util.function.IntFunction<Integer> scoreAt = idx -> {
            String zhi=this.benGuaLiuYaoGanZhi.get(idx).substring(1);
            return calcWangShuaiScore(zhi);
        };

        java.util.List<Integer> caiIdxs = findPositionsByKin(this.benGuaLiuYaoLiuQin,"妻财");

        if(hasMoving){
            /* ---------------- 有动爻情况 ---------------- */
            if(!caiIdxs.isEmpty()){
                for(int ci: caiIdxs){
                    int cScore=scoreAt.apply(ci);
                    String dong=this.benguainformation.get(ci).get(0);
                    String hua=this.benguainformation.get(ci).get(1);
                    String xk=this.benguainformation.get(ci).get(2);
                    String caiZhi=this.benguainformation.get(ci).get(7);

                    /* 1) 财爻=世 */
                    if(ci==shiIdx){
                        if(cScore>0){
                            sb.append("财爻旺相持世，对求财有利。\n");
                            grade="吉";
                        }else{
                            sb.append("财爻衰，求财不利。\n");
                            if(!"吉".equals(grade)) grade="凶";
                        }
                        continue;
                    }

                    /* 2) 世=官鬼 & 财爻生世 */
                    if(shiIdx>=0 && "官鬼".equals(this.benGuaLiuYaoLiuQin.get(shiIdx))){
                        if("动爻".equals(dong) && cScore>0 && !"化废".equals(hua)){
                            sb.append("财爻旺相生世，对求财有利。\n");
                            grade="吉";
                        }else{
                            sb.append("财爻衰，不利求财。\n");
                            if(!"吉".equals(grade)) grade="凶";
                        }
                    }

                    /* 3) 财爻空/废/冲 */
                    String dayZhi=getDayGanZhi().substring(1);
                    String monthZhi=getMonthGanZhi().substring(1);
                    boolean conflictDay=caiZhi.equals(LiuYaoMap.DI_ZHI_XIANG_CHONG.get(dayZhi));
                    boolean conflictMonth=caiZhi.equals(LiuYaoMap.DI_ZHI_XIANG_CHONG.get(monthZhi));

                    if("旬空".equals(xk) || "化废".equals(hua) || (cScore<=0 && (conflictDay||conflictMonth))){
                        sb.append("财爻无力，不易求财。\n");
                        if(!"吉".equals(grade)) grade="凶";
                        if("动爻".equals(dong) && yingIdx>=0 && "官鬼".equals(this.benGuaLiuYaoLiuQin.get(yingIdx))){
                            sb.append("财来生应，不利求财。\n");
                        }
                    }
                }
            }else{
                sb.append("财爻不显，难以求财。\n");
                if(!"吉".equals(grade)) grade="凶";
            }

            /* 5) 兄弟发动 */
            java.util.List<Integer> broIdxs=findPositionsByKin(this.benGuaLiuYaoLiuQin,"兄弟");
            for(int bi: broIdxs){
                String dong=this.benguainformation.get(bi).get(0);
                String hua=this.benguainformation.get(bi).get(1);
                if("动爻".equals(dong) && bi!=shiIdx && !"化废".equals(hua)){
                    sb.append("兄弟发动，不利求财。\n");
                    if(!"吉".equals(grade)) grade="平";
                    break;
                }
            }

            // --------------- 其他情况：综合妻财旺衰 ----------------
            if ("平".equals(grade) && sb.length()==0 && !caiIdxs.isEmpty()) {
                int ci = caiIdxs.get(0); // 取第一个妻财爻参考
                int cScore = scoreAt.apply(ci);
                String hua = this.benguainformation.get(ci).get(1);
                String xk  = this.benguainformation.get(ci).get(2);

                if (cScore >= 0 && !"化废".equals(hua) && !"旬空".equals(xk)) {
                    sb.append("财运不错。\n");
                    grade = "吉";
                } else if (cScore >= 0 && !"化废".equals(hua) && "旬空".equals(xk)) {
                    sb.append("财爻旬空，出空之日就是财运转好之时。\n");
                } else {
                    sb.append("财爻无力，财运不行。\n");
                    grade = "凶";
                }
            }

        }else{
            /* ---------------- 无动爻情况 ---------------- */
            if(shiIdx>=0 && "妻财".equals(this.benGuaLiuYaoLiuQin.get(shiIdx))){
                int cScore=scoreAt.apply(shiIdx);
                String xk=this.benguainformation.get(shiIdx).get(2);
                String hua=this.benguainformation.get(shiIdx).get(1);
                String caiZhi=this.benguainformation.get(shiIdx).get(7);
                String dayZhi=getDayGanZhi().substring(1);
                String monthZhi=getMonthGanZhi().substring(1);
                boolean conflictDay=caiZhi.equals(LiuYaoMap.DI_ZHI_XIANG_CHONG.get(dayZhi));
                boolean conflictMonth=caiZhi.equals(LiuYaoMap.DI_ZHI_XIANG_CHONG.get(monthZhi));

                if(cScore>0 && !"旬空".equals(xk) && !"化废".equals(hua) && !conflictDay && !conflictMonth){
                    sb.append("财爻持世原神有利，财运顺遂。\n");
                    grade="吉";
                }else{
                    sb.append("原神受伤，对财运不利。\n");
                    if(!"吉".equals(grade)) grade="凶";
                }
            }else if(shiIdx>=0 && "兄弟".equals(this.benGuaLiuYaoLiuQin.get(shiIdx))){
                String xk=this.benguainformation.get(shiIdx).get(2);
                if("旬空".equals(xk)){
                    sb.append("兄弟持世，财运不济，但短期看不出来。\n");
                    if(!"凶".equals(grade)) grade="平";
                }else{
                    sb.append("兄弟持世，财运不济。\n");
                    if(!"吉".equals(grade)) grade="凶";
                }
            }

            if(caiIdxs.isEmpty()){
                sb.append("财爻不显，不利求财。\n");
                if(!"吉".equals(grade)) grade="凶";
            }

            if(!caiIdxs.isEmpty() && shiIdx>=0 && "官鬼".equals(this.benGuaLiuYaoLiuQin.get(shiIdx))){
                int cScore=scoreAt.apply(caiIdxs.get(0));
                String hua=this.benguainformation.get(caiIdxs.get(0)).get(1);
                String xk=this.benguainformation.get(shiIdx).get(2);
                if(cScore>0 && !"旬空".equals(xk) && !"化废".equals(hua)){
                    sb.append("官鬼持世，妻财旺相，财运不错。\n");
                    grade="吉";
                }else{
                    sb.append("财运不佳。\n");
                    if(!"吉".equals(grade)) grade="凶";
                }
            }

            if(!caiIdxs.isEmpty() && shiIdx>=0 && "父母".equals(this.benGuaLiuYaoLiuQin.get(shiIdx))){
                sb.append("财克我，意味财来找我，求财有利。\n");
                if(!"凶".equals(grade)) grade="吉";
            }

            if(!caiIdxs.isEmpty() && shiIdx>=0 && "子孙".equals(this.benGuaLiuYaoLiuQin.get(shiIdx))){
                int cScore=scoreAt.apply(caiIdxs.get(0));
                int sScore=scoreAt.apply(shiIdx);
                if(cScore>0 && sScore>0){
                    sb.append("子孙持世，财爻旺相，求财有利。\n");
                    grade="吉";
                }else{
                    sb.append("求财阻力大，劳心劳神也未必讨好。\n");
                    if(!"吉".equals(grade)) grade="凶";
                }
            }
        }

        // 去重相同批语行，避免多次重复同义句
        java.util.LinkedHashSet<String> uniqLines = new java.util.LinkedHashSet<>();
        for (String line : sb.toString().split("\n")) {
            uniqLines.add(line);
        }
        StringBuilder finalSb = new StringBuilder();
        for (String l : uniqLines) {
            if(!l.isEmpty()) finalSb.append(l).append("\n");
        }
        this.analyzeCaiYun = finalSb.toString().trim();
        return buildCaiYunResult(grade);
    }

    /*===================== 共用小工具 =====================*/

    private boolean hasMovingLines() {
        for (String mark : this.liuYaoYaoXiangMark) {
            if ("○".equals(mark) || "×".equals(mark)) return true;
        }
        return false;
    }

    private List<Integer> findPositionsByKin(List<String> kinList, String kinName) {
        List<Integer> list = new ArrayList<>();
        for (int i = 0; i < kinList.size(); i++) {
            if (kinName.equals(kinList.get(i))) list.add(i);
        }
        return list;
    }

    /**
     * 多父母爻时的优选算法：动爻 > 世/应 > 六条附加信息计分
     */
    private int selectYongShen(List<Integer> parentPos) {
        if (parentPos.isEmpty()) return -1;
        if (parentPos.size() == 1) return parentPos.get(0);

        // 1) 动爻优先
        for (int idx : parentPos) {
            if ("动爻".equals(this.benguainformation.get(idx).get(1))) return idx;
        }
        // 2) 世/应 其次
        for (int idx : parentPos) {
            String se = this.benguainformation.get(idx).get(6);
            if ("世".equals(se) || "应".equals(se)) return idx;
        }
        // 3) 计分法
        int best = parentPos.get(0), bestScore = -1;
        for (int idx : parentPos) {
            int sc = calcParentExtraScore(idx);
            if (sc > bestScore) { bestScore = sc; best = idx; }
        }
        return best;
    }

    private int calcParentExtraScore(int idx) {
        int score = 0;
        String monthZhi = getMonthGanZhi().substring(1);
        String dayZhi = getDayGanZhi().substring(1);
        String yaoZhi = this.benguainformation.get(idx).get(8);

        if ("旬空".equals(this.benguainformation.get(idx).get(3))) score++;
        if (monthZhi.equals(LiuYaoMap.DI_ZHI_XIANG_CHONG.get(yaoZhi))) score++;
        if (LiuYaoMap.YUE_YAO_WANG_SHUAI.getOrDefault(monthZhi + yaoZhi, 0) >= 10) score++;
        if (LiuYaoMap.RI_YAO_WANG_SHUAI.getOrDefault(dayZhi + yaoZhi, 0) >= 10) score++;
        if (monthZhi.equals(yaoZhi)) score++;
        if (dayZhi.equals(yaoZhi)) score++;
        return score;
    }

    /*===================== 辅助：地支 → 五行 / 旺衰评分 =====================*/

    private static final Map<String,String> DI_ZHI_TO_WU_XING = new HashMap<String,String>(){{
        put("寅","木");put("卯","木");
        put("巳","火");put("午","火");
        put("申","金");put("酉","金");
        put("子","水");put("亥","水");
        put("辰","土");put("戌","土");put("丑","土");put("未","土");
    }};

    private int calcWangShuaiScore(String yaoZhi){
        String monthZhi=getMonthGanZhi().substring(1);
        String dayZhi=getDayGanZhi().substring(1);
        int score=0;
        if(LiuYaoMap.YUE_YAO_WANG_SHUAI.containsKey(Arrays.asList(monthZhi,yaoZhi)))
            score+=LiuYaoMap.YUE_YAO_WANG_SHUAI.get(Arrays.asList(monthZhi,yaoZhi));
        if(LiuYaoMap.RI_YAO_WANG_SHUAI.containsKey(Arrays.asList(dayZhi,yaoZhi)))
            score+=LiuYaoMap.RI_YAO_WANG_SHUAI.get(Arrays.asList(dayZhi,yaoZhi));
        return score;
    }

    /*===================== 静卦分析 =====================*/

    private void analyzeStaticKaoXue(int yongIdx) {
        StringBuilder sb = new StringBuilder();
        int yongScore = calcWangShuaiScore(this.getBenGuaLiuYaoGanZhi().get(yongIdx).substring(1));
        int shiIdx = this.benGuaLiuYaoShiYing.indexOf("世");
        int shiScore = (shiIdx >= 0) ? calcWangShuaiScore(this.getBenGuaLiuYaoGanZhi().get(shiIdx).substring(1)) : 0;
        String yongWu = this.benGuaLiuYaoWuXing.get(yongIdx);
        String shiWu = (shiIdx >= 0) ? this.benGuaLiuYaoWuXing.get(shiIdx) : "";
        String shiKin = (shiIdx >= 0) ? this.benGuaLiuYaoLiuQin.get(shiIdx) : "";

        // 1) 用神=世爻
        if (yongIdx == shiIdx) {
            this.duangua = "吉";
            sb.append("用神为世爻，考试顺利。\n");
            this.analyzeKaoXue = sb.toString();
            return;
        }

        // 2) 世爻为忌神(妻财)
        if ("妻财".equals(shiKin)) {
            if ("旬空".equals(this.benguainformation.get(shiIdx).get(3))) {
                this.duangua = "平";
                sb.append("世爻为忌神且旬空，短期尚可，长期不利。\n");
            } else {
                this.duangua = "凶";
                sb.append("世爻为忌神，考试多阻。\n");
            }
        }

        // 4) 用神克世（考学非妻财）
        if (yongWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(shiWu)) && shiIdx >= 0) {
            this.duangua = "凶";
            sb.append("用神克世，不利。\n");
        }

        // 3/5) 五行相生关系
        boolean yongShengShi = yongWu.equals(LiuYaoMap.WU_XING_XIANG_SHENG.get(shiWu));
        boolean shiShengYong = shiWu.equals(LiuYaoMap.WU_XING_XIANG_SHENG.get(yongWu));

        if (yongShengShi && shiScore >= 0) {
            if (!"吉".equals(this.duangua)) this.duangua = "平";
            sb.append("用神生世，加分。\n");
        } else if (shiShengYong && yongScore >= 0 && shiScore >= 0) {
            this.duangua = "吉";
            sb.append("世生用神，双方得力。\n");
        }

        this.analyzeKaoXue = sb.toString();
    }

    /*===================== 动卦分析 =====================*/

    private boolean analyzeDynamicFastPath() {
        if (this.bianGuaLiuYaoLiuQin == null) return false;
        for (int i = 0; i < 6; i++) {
            if (!"动爻".equals(this.benguainformation.get(i).get(0))) continue;
            if ("父母".equals(this.bianGuaLiuYaoLiuQin.get(i))) {
                int score = calcWangShuaiScore(getBenGuaLiuYaoGanZhi().get(i).substring(1));
                if (score >= -10) {
                    this.duangua = "吉";
                    this.analyzeKaoXue = "动爻化父母且旺衰≥-10，直接判吉。";
                    return true;
                }
            }
        }
        return false;
    }

    private void analyzeDynamicGeneral(int yongIdx) {
        StringBuilder sb = new StringBuilder();

        // 1) 收集动爻索引
        List<Integer> moving = new ArrayList<>();
        for (int i = 0; i < 6; i++) if ("动爻".equals(this.benguainformation.get(i).get(1))) moving.add(i);

        // 2) 统计路线终点
        int shengYong = 0, keYong = 0, shengGuan = 0, keGuan = 0, neutral = 0;
        for (int idx : moving) {
            RouteResult res = traceRoute(idx, yongIdx, moving, new HashSet<>());
            if (res == null) continue;
            switch (res.terminalType + res.action) {
                case "用神生": shengYong++; break;
                case "用神克": keYong++; break;
                case "官鬼生": shengGuan++; break;
                case "官鬼克": keGuan++; break;
                default: neutral++; break;
            }
        }

        // 3) 规则评估（核心摘要）
        if (shengYong > 0 && keYong == 0) { this.duangua = "吉"; sb.append("动动相生用神占优。\n"); }
        else if (keYong > 0 && shengYong == 0) { this.duangua = "凶"; sb.append("动爻克用神，成绩不够令人满意。\n"); }
        else if (shengYong > 0 && keGuan > 0) { this.duangua = "吉"; sb.append("生用神且克点不在关键爻，仍吉。\n"); }
        else { this.duangua = "平"; sb.append("生克交杂，成绩中等。\n"); }

        this.analyzeKaoXue = sb.toString();
    }

    /** 追踪动爻生/克路线（深度≤6） 规则：生>克>生用神>克用神 */
    private RouteResult traceRoute(int curIdx,int yongIdx,List<Integer> moving,Set<Integer> visited){
        if(visited.contains(curIdx)||visited.size()>6) return null;
        visited.add(curIdx);

        // 当前爻若旬空且不冲日支 => 不可用
        if(isVoidAndNotChongDay(curIdx)) return null;

        String curWu=this.benGuaLiuYaoWuXing.get(curIdx);

        // 1) 生其它动爻
        for(int nxt: moving){
            if(nxt==curIdx||visited.contains(nxt)||isVoidAndNotChongDay(nxt)) continue;
            String nxtWu=this.benGuaLiuYaoWuXing.get(nxt);
            if(nxtWu.equals(LiuYaoMap.WU_XING_XIANG_SHENG.get(curWu))){
                RouteResult child=traceRoute(nxt,yongIdx,moving,visited);
                if(child!=null) return new RouteResult("生",child.terminalType);
            }
        }

        // 2) 克其它动爻
        for(int nxt: moving){
            if(nxt==curIdx||visited.contains(nxt)||isVoidAndNotChongDay(nxt)) continue;
            String nxtWu=this.benGuaLiuYaoWuXing.get(nxt);
            if(nxtWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(curWu))){
                RouteResult child=traceRoute(nxt,yongIdx,moving,visited);
                if(child!=null) return new RouteResult("克",child.terminalType);
            }
        }

        // 3) 对用神
        String yongWu=this.benGuaLiuYaoWuXing.get(yongIdx);
        if(yongWu.equals(LiuYaoMap.WU_XING_XIANG_SHENG.get(curWu))) return new RouteResult("生","用神");
        if(yongWu.equals(LiuYaoMap.WU_XING_XIANG_KE.get(curWu))) return new RouteResult("克","用神");

        // 4) 对官鬼
        for(int i=0;i<6;i++) if("官鬼".equals(this.benGuaLiuYaoLiuQin.get(i))){
            String gw=this.benGuaLiuYaoWuXing.get(i);
            if(gw.equals(LiuYaoMap.WU_XING_XIANG_SHENG.get(curWu))) return new RouteResult("生","官鬼");
            if(gw.equals(LiuYaoMap.WU_XING_XIANG_KE.get(curWu))) return new RouteResult("克","官鬼");
        }

        // 无可用动作
        return null;
    }

    private boolean isVoidAndNotChongDay(int idx){
        if(!"旬空".equals(this.benguainformation.get(idx).get(3))) return false;
        String yaoZhi=this.benguainformation.get(idx).get(8);
        String dayZhi=getDayGanZhi().substring(1);
        String chong=LiuYaoMap.DI_ZHI_XIANG_CHONG.get(dayZhi);
        return !yaoZhi.equals(chong);
    }

//------------------------------------------------------------------------------------------------------------------------------------

    /**
     * 获取公历日期
     *
     * @return 公历日期
     */
    public String getSolarStr() {
        return CommonUtil.solarStr(getSolar());
    }

    /**
     * 获取农历日期
     *
     * @return 农历日期
     */
    public String getLunarStr() {
        return CommonUtil.lunarStr(getLunar());
    }

    /**
     * 返回神煞
     *
     * @param yearZhi 年干地支
     * @param dayZhi  日干地支
     */
    private String returnShenSha(List<String> yearZhi, List<String> dayZhi) {

        // 1、判断年支，若匹配则返回神煞
        for (String key : yearZhi) {
            if (getYearZhi().equals(key)) return key;
        }
        for (String key : dayZhi) {
            if (getYearZhi().equals(key)) return key;
        }
        // 2、判断月支，若匹配则返回神煞
        for (String key : yearZhi) {
            if (getMonthZhi().equals(key)) return key;
        }
        for (String key : dayZhi) {
            if (getMonthZhi().equals(key)) return key;
        }
        // 3、判断日支，若匹配返回神煞
        for (String key : yearZhi) {
            if (getDayZhi().equals(key)) return key;
        }
        for (String key : dayZhi) {
            if (getDayZhi().equals(key)) return key;
        }
        // 4、判断时支，若匹配则返回神煞
        for (String key : yearZhi) {
            if (getHourZhi().equals(key)) return key;
        }
        for (String key : dayZhi) {
            if (getHourZhi().equals(key)) return key;
        }

        return null;

    }

    @Override
    public String toString() {

        StringBuilder s = new StringBuilder();

        s.append("公历:").append(getSolarStr());
        s.append("   ");
        s.append("农历:").append(getLunarStr());
        s.append("   ");
        s.append("星期:").append(getWeek());
        s.append("   ");
        s.append("八字:").append(getBaZi());
        s.append("   ");
        s.append("八字五行:").append(getBaZiWuXing());
        s.append("   ");
        s.append("八字纳音:").append(getBaZiNaYin());
        s.append("   ");
        s.append("上卦:").append(getShangGua()).append("(").append(getShangGuaAs()).append(")");
        s.append("   ");
        s.append("下卦:").append(getXiaGua()).append("(").append(getXiaGuaAs()).append(")");
        s.append("   ");
        s.append("本卦:").append(getBenGua()).append("(").append(getBenGuaAs()).append(")");
        s.append("   ");
        s.append("变卦:").append(getBianGua()).append("(").append(getBianGuaAs()).append(")");
        s.append("   ");
        s.append("互卦:").append(getHuGua()).append("(").append(getHuGuaAs()).append(")");
        s.append("   ");
        s.append("错卦:").append(getCuoGua()).append("(").append(getCuoGuaAs()).append(")");
        s.append("   ");
        s.append("综卦:").append(getZongGua()).append("(").append(getZongGuaAs()).append(")");
        s.append("   ");
        s.append("驿马:").append(getYiMa());
        s.append("   ");
        s.append("天马:").append(getTianMa());
        s.append("   ");
        s.append("天元禄:").append(getTianYuanLu());
        s.append("   ");
        s.append("天乙贵人:").append(getTianYiGuiRen());
        s.append("   ");
        s.append("太极贵人:").append(getTaiJiGuiRen());
        s.append("   ");
        s.append("天德贵人:").append(getTianDeGuiRen());
        s.append("   ");
        s.append("月德贵人:").append(getYueDeGuiRen());
        s.append("   ");
        s.append("符印:").append(getTangFuGuoYin());
        s.append("   ");
        s.append("咸池:").append(getXianChi());
        s.append("   ");
        s.append("天喜:").append(getTianXi());
        s.append("   ");
        s.append("皇恩:").append(getHuangEn());
        s.append("   ");
        s.append("文昌:").append(getWenChang());
        s.append("   ");
        s.append("华盖:").append(getHuaGai());
        s.append("   ");
        s.append("将星:").append(getJiangXing());
        s.append("   ");
        s.append("灾煞:").append(getZaiSha());
        s.append("   ");
        s.append("劫煞:").append(getJieSha());
        s.append("   ");
        s.append("谋星:").append(getMouXing());
        s.append("   ");
        s.append("天医:").append(getTianYi());

        return s.toString();

    }

    private static class RouteResult {
        String action;
        String terminalType;
        RouteResult(String a, String t){ this.action=a; this.terminalType=t; }
    }

}
