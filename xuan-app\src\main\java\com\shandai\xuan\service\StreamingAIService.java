package com.shandai.xuan.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 流式AI服务 - 支持Server-Sent Events实时响应
 */
@Service
public class StreamingAIService {

    private static final Logger logger = LoggerFactory.getLogger(StreamingAIService.class);

    private final String apiKey;
    private final String apiUrl;
    private final String defaultModel;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public StreamingAIService(@Value("${gemini.api.key}") String apiKey,
                             @Value("${gemini.api.url}") String apiUrl,
                             @Value("${ai.analysis.default-model}") String defaultModel,
                             RestTemplate restTemplate) {
        this.apiKey = apiKey;
        this.apiUrl = apiUrl;
        this.defaultModel = defaultModel;
        this.restTemplate = restTemplate;
        this.objectMapper = new ObjectMapper();

        // 添加构造函数日志
        logger.info("StreamingAIService初始化 - API密钥: {}...", apiKey.substring(0, Math.min(10, apiKey.length())));
        logger.info("StreamingAIService初始化 - API URL: {}", apiUrl);
        logger.info("StreamingAIService初始化 - 默认模型: {}", defaultModel);
    }
    
    /**
     * 快速分析 - 使用更激进的参数设置
     */
    public String getFastAnalysis(String prompt) {
        try {
            logger.info("开始快速AI分析");
            long startTime = System.currentTimeMillis();
            
            // 构建请求体 - 优化参数以提高速度
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", defaultModel); // 使用配置的模型
            requestBody.put("temperature", 0.7); // 使用正常的随机性
            // 不设置max_tokens限制，让模型完整生成内容
            requestBody.put("top_p", 0.9);       // 优化采样
            requestBody.put("stream", false);    // 非流式以获得最快响应
            
            // 构建消息列表
            List<Map<String, String>> messages = new ArrayList<>();
            Map<String, String> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", prompt);
            messages.add(message);
            requestBody.put("messages", messages);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + apiKey);
            headers.set("Content-Type", "application/json");
            
            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 添加调试日志
            logger.info("发送AI请求 - URL: {}", apiUrl);
            logger.info("发送AI请求 - 模型: {}", defaultModel);
            logger.info("发送AI请求 - API密钥前缀: {}", apiKey.substring(0, Math.min(10, apiKey.length())) + "...");
            logger.info("发送AI请求体: {}", objectMapper.writeValueAsString(requestBody));

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                apiUrl,
                HttpMethod.POST,
                requestEntity,
                String.class
            );
            
            long endTime = System.currentTimeMillis();
            logger.info("快速AI分析完成，耗时：{}ms", endTime - startTime);
            
            // 解析响应
            JsonNode responseJson = objectMapper.readTree(response.getBody());
            JsonNode choices = responseJson.get("choices");
            if (choices != null && choices.size() > 0) {
                JsonNode firstChoice = choices.get(0);
                JsonNode responseMessage = firstChoice.get("message");
                if (responseMessage != null) {
                    JsonNode content = responseMessage.get("content");
                    if (content != null) {
                        return content.asText();
                    }
                }
            }
            
            return "AI分析时出现错误：响应格式不正确";
            
        } catch (Exception e) {
            logger.error("快速AI分析失败", e);
            return "AI分析时出现错误：" + e.getMessage();
        }
    }
}
