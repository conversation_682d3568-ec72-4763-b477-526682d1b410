package com.xuan.tieban;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 铁板神数数据访问类。
 * <p>
 * 这是一个统一的、简洁的数据接口。所有数据从资源文件 tieban_data.json 中加载。
 * 项目中其他地方只需要调用这里的 public 方法即可，无需关心数据源。
 */
public class TieBanMap {

    // 铁板神数取条文
    private static final Map<String, List<String>> TIE_BAN_MAP = loadMapFromJson();

    /**
     *  天干取数
     *  甲壬为6, 乙癸为2, 庚为3, 辛为4, 丙为8, 己为9, 戊为1, 丁为7
     */
    public static final Map<Character, Integer> TIAN_GAN_MAP = Map.ofEntries(
            Map.entry('甲', 6), Map.entry('壬', 6),
            Map.entry('乙', 2), Map.entry('癸', 2),
            Map.entry('庚', 3),
            Map.entry('辛', 4),
            Map.entry('丙', 8),
            Map.entry('己', 9),
            Map.entry('戊', 1),
            Map.entry('丁', 7)
    );

    /**
     *  天干配卦
     *  甲壬乾, 乙癸坤, 丙艮, 丁兑, 戊坎, 己离, 庚震, 辛巽
     */
    public static final Map<Character, String> TIAN_GAN_GUA_MAP = Map.ofEntries(
            Map.entry('甲', "乾"), Map.entry('壬', "乾"),
            Map.entry('乙', "坤"), Map.entry('癸', "坤"),
            Map.entry('丙', "艮"),
            Map.entry('丁', "兑"),
            Map.entry('戊', "坎"),
            Map.entry('己', "离"),
            Map.entry('庚', "震"),
            Map.entry('辛', "巽")
    );


    /**
     *  天干配数
     *  甲一，乙六，丙二，丁七，戊三，己八，庚四，辛九，壬五，癸零
     */
    public static final Map<Character, Integer> TIAN_GAN_PEI_SHU_MAP = Map.of(
            '甲', 1,
            '乙', 6,
            '丙', 2,
            '丁', 7,
            '戊', 3,
            '己', 8,
            '庚', 4,
            '辛', 9,
            '壬', 5,
            '癸', 0
    );
    

    /**
     *  地支取数
     *  亥1, 子6, 寅3, 卯8, 巳2, 午7, 申4, 酉9, 辰戌5, 丑未10
     */
    public static final Map<Character, Integer> DI_ZHI_MAP = Map.ofEntries(
            Map.entry('亥', 1), Map.entry('子', 6),
            Map.entry('寅', 3), Map.entry('卯', 8),
            Map.entry('巳', 2), Map.entry('午', 7),
            Map.entry('申', 4), Map.entry('酉', 9),
            Map.entry('辰', 5), Map.entry('戌', 5),
            Map.entry('丑', 10), Map.entry('未', 10)
    );

    /**
     *  六爻取数
     *  子丑30, 寅卯60, 辰巳90, 午未120, 申酉150, 戌亥180
     */
    public static final Map<Character, Integer> LIU_YAO_MAP = Map.ofEntries(
            Map.entry('子', 30), Map.entry('丑', 30),
            Map.entry('寅', 60), Map.entry('卯', 60),
            Map.entry('辰', 90), Map.entry('巳', 90),
            Map.entry('午', 120), Map.entry('未', 120),
            Map.entry('申', 150), Map.entry('酉', 150),
            Map.entry('戌', 180), Map.entry('亥', 180)
    );

    /**
     *  地支配卦
     *  子亥坎, 丑坤, 寅卯震, 辰兑, 巳午离, 未艮, 戌巽
     */
    public static final Map<Character, String> DI_ZHI_GUA_MAP = Map.ofEntries(
            Map.entry('子', "坎"), Map.entry('亥', "坎"),
            Map.entry('丑', "坤"),
            Map.entry('寅', "震"), Map.entry('卯', "震"),
            Map.entry('辰', "兑"),
            Map.entry('巳', "离"), Map.entry('午', "离"),
            Map.entry('未', "艮"),
            Map.entry('申', "乾"), Map.entry('酉', "乾"),
            Map.entry('戌', "巽")
    );

    /**
     *  天干地支太玄数表
     *  天干: 甲己 9, 乙庚 8, 丙辛 7, 丁壬 6, 戊癸 5
     *  地支: 子午 9, 丑未 8, 寅申 7, 卯酉 8, 辰戌 5, 巳亥 4
     */
    public static final Map<Character, Integer> TAI_XUAN_SHU_MAP = Map.ofEntries(
            Map.entry('甲', 9), Map.entry('己', 9),
            Map.entry('乙', 8), Map.entry('庚', 8),
            Map.entry('丙', 7), Map.entry('辛', 7),
            Map.entry('丁', 6), Map.entry('壬', 6),
            Map.entry('戊', 5), Map.entry('癸', 5),
            Map.entry('子', 9), Map.entry('午', 9),
            Map.entry('丑', 8), Map.entry('未', 8),
            Map.entry('寅', 7), Map.entry('申', 7),
            Map.entry('卯', 8), Map.entry('酉', 8),
            Map.entry('辰', 5), Map.entry('戌', 5),
            Map.entry('巳', 4), Map.entry('亥', 4)
    );

    /**
     *  八卦配数
     *  乾8, 坤2, 艮8, 兑7, 坎1, 离9, 震3, 巽4
     */
    public static final Map<Character, Integer> BA_GUA_SHU_MAP = Map.of(
            '乾', 6,
            '坤', 2,
            '艮', 8,
            '兑', 7,
            '坎', 1,
            '离', 9,
            '震', 3,
            '巽', 4
    );

    /**
     * [简洁的公开接口]
     * 根据 key (整型) 获取对应的词条列表。
     *
     * @param key 整数 ID
     * @return 包含两个字符串的列表，如果未找到则返回 null。
     */
    public static List<String> getTieBans(int key) {
        // 在查找时，将整型 key 转换为字符串。
        return TIE_BAN_MAP.get(String.valueOf(key));
    }

    /**
     * [旧的公开接口 - 已更新]
     * 判断是否为铁板用户。
     */
    public static boolean isTieBan(int serverId, String roleId) {
        if (roleId == null) {
            return false;
        }
        List<String> roleIdList = getTieBans(serverId);
        if (roleIdList == null || roleIdList.isEmpty()) {
            return false;
        }
        return roleIdList.contains(roleId);
    }

    /**
     * [内部实现细节]
     * 从 tieban_data.json 文件加载所有数据到 Map 中。
     * 这个方法是 private 的，外部代码无需也无法调用它。
     */
    private static Map<String, List<String>> loadMapFromJson() {
        String resourcePath = "tieban_data.json";
        try (InputStream is = TieBanMap.class.getResourceAsStream(resourcePath)) {
            if (is == null) {
                System.err.println("关键资源文件未找到: " + resourcePath);
                return Collections.emptyMap();
            }

            Gson gson = new Gson();
            // 定义正确的 Map 类型，键为 String。
            Type type = new TypeToken<Map<String, List<String>>>() {}.getType();
            return gson.fromJson(new InputStreamReader(is, StandardCharsets.UTF_8), type);

        } catch (Exception e) {
            System.err.println("加载铁板map数据时出错");
            e.printStackTrace();
            return Collections.emptyMap();
        }
    }
}
