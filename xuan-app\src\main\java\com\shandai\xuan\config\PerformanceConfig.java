package com.shandai.xuan.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 性能监控配置
 */
@Configuration
@ConfigurationProperties(prefix = "performance.monitor")
@Data
public class PerformanceConfig {
    
    /**
     * 是否启用性能监控
     */
    private boolean enabled = true;
    
    /**
     * 是否在控制台输出性能报告
     */
    private boolean consoleOutput = true;
    
    /**
     * 是否输出详细的步骤信息
     */
    private boolean detailedSteps = true;
    
    /**
     * 性能阈值配置
     */
    private Thresholds thresholds = new Thresholds();
    
    @Data
    public static class Thresholds {
        /**
         * 六爻计算阈值（毫秒）
         */
        private long liuyaoCalculation = 100;
        
        /**
         * 数据提取阈值（毫秒）
         */
        private long dataExtraction = 50;
        
        /**
         * 提示词构建阈值（毫秒）
         */
        private long promptBuilding = 20;
        
        /**
         * AI调用阈值（毫秒）
         */
        private long aiCall = 10000;
        
        /**
         * 缓存处理阈值（毫秒）
         */
        private long cacheProcessing = 10;
        
        /**
         * 网络请求阈值（毫秒）
         */
        private long networkRequest = 8000;
        
        /**
         * 响应解析阈值（毫秒）
         */
        private long responseParsing = 100;
    }
}
