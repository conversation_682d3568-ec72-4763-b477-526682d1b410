# AI分析性能监控使用指南

## 功能概述

为了解决AI分析子模块速度慢的问题，我们新增了详细的性能监控功能，可以在终端实时查看每个步骤的耗时情况。

## 监控的步骤

### 主要步骤
1. **六爻计算** - 基础六爻数据计算
2. **数据提取** - 从LiuYao对象提取分析数据
3. **提示词构建** - 构建AI分析的提示词
4. **AI调用** - 调用硅基流动API进行分析
5. **缓存处理** - 缓存相关操作
6. **重试处理** - 失败重试机制

### AI调用细分步骤
- **请求构建** - 构建HTTP请求体
- **网络请求** - 实际的API网络调用
- **响应解析** - 解析AI返回的JSON响应

## 终端输出示例

```
🚀 [session-1703123456789-1] 开始执行: 总体分析 (时间: 14:30:45.123)
🚀 [session-1703123456789-1] 开始执行: 六爻计算 (时间: 14:30:45.125)
✅ [session-1703123456789-1] 完成执行: 六爻计算 | 耗时: 85ms 🟢
🚀 [session-1703123456789-1] 开始执行: 数据提取 (时间: 14:30:45.210)
✅ [session-1703123456789-1] 完成执行: 数据提取 | 耗时: 32ms 🟢
🚀 [session-1703123456789-1] 开始执行: 缓存处理 (时间: 14:30:45.242)
🚀 [session-1703123456789-1] 开始执行: 提示词构建 (时间: 14:30:45.243)
✅ [session-1703123456789-1] 完成执行: 提示词构建 | 耗时: 15ms 🟢
🚀 [session-1703123456789-1] 开始执行: AI调用 (时间: 14:30:45.258)
🚀 [session-1703123456789-1] 开始执行: 请求构建 (时间: 14:30:45.259)
✅ [session-1703123456789-1] 完成执行: 请求构建 | 耗时: 8ms 🟢
🚀 [session-1703123456789-1] 开始执行: 网络请求 (时间: 14:30:45.267)
✅ [session-1703123456789-1] 完成执行: 网络请求 | 耗时: 3.45s 🟢
🚀 [session-1703123456789-1] 开始执行: 响应解析 (时间: 14:30:48.717)
✅ [session-1703123456789-1] 完成执行: 响应解析 | 耗时: 12ms 🟢
✅ [session-1703123456789-1] 完成执行: AI调用 | 耗时: 3.48s 🟢
✅ [session-1703123456789-1] 完成执行: 缓存处理 | 耗时: 3.50s 🟢
✅ [session-1703123456789-1] 完成执行: 总体分析 | 耗时: 3.62s 🟢

================================================================================
📊 [session-1703123456789-1] AI分析性能报告
================================================================================
  六爻计算      :     85ms ( 2.3%) [██░░░░░░░░░░░░░░░░░░] 🟢
  数据提取      :     32ms ( 0.9%) [█░░░░░░░░░░░░░░░░░░░] 🟢
  提示词构建    :     15ms ( 0.4%) [█░░░░░░░░░░░░░░░░░░░] 🟢
  AI调用        :   3.48s (96.1%) [████████████████████] 🟢
  缓存处理      :   3.50s (96.7%) [████████████████████] 🟢
--------------------------------------------------------------------------------
  总耗时        :   3.62s (100.0%)
================================================================================
```

## 性能状态指示器

- 🟢 **绿色** - 性能良好（在阈值内）
- 🟡 **黄色** - 性能一般（超过阈值但在2倍阈值内）
- 🔴 **红色** - 性能较差（超过2倍阈值）

## 性能阈值配置

在 `application.properties` 中可以调整各步骤的性能阈值：

```properties
# 性能监控配置
performance.monitor.enabled=true
performance.monitor.console-output=true
performance.monitor.detailed-steps=true

# 性能阈值配置（毫秒）
performance.monitor.thresholds.liuyao-calculation=100
performance.monitor.thresholds.data-extraction=50
performance.monitor.thresholds.prompt-building=20
performance.monitor.thresholds.ai-call=10000
performance.monitor.thresholds.cache-processing=10
performance.monitor.thresholds.network-request=8000
performance.monitor.thresholds.response-parsing=100
```

## 使用方法

### 1. 正常使用AI分析
直接调用现有的AI分析接口，性能监控会自动启用：

```bash
# 调用AI分析接口
curl -X POST http://localhost:8080/api/divination/liuyao/simple-ai \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试用户",
    "sex": "男",
    "occupy": "工作发展如何？",
    "year": 2024,
    "month": 1,
    "day": 15,
    "hour": 14,
    "minute": 30,
    "liuYao": 1,
    "wuYao": 2,
    "siYao": 1,
    "sanYao": 2,
    "erYao": 1,
    "yiYao": 2
  }'
```

### 2. 使用性能测试接口
我们提供了专门的测试接口：

```bash
# 测试AI分析性能
curl -X POST http://localhost:8080/api/performance/test-ai-analysis

# 测试性能监控器本身
curl -X GET http://localhost:8080/api/performance/test-monitor
```

### 3. 查看日志文件
性能监控数据会同时输出到：
- **控制台** - 实时查看
- **logs/performance.log** - 性能专用日志
- **logs/xuan-app.log** - 应用总日志

## 性能优化建议

根据监控结果，可以针对性地进行优化：

### 1. 如果AI调用耗时过长
- 检查网络连接
- 考虑更换更快的AI模型
- 调整max_tokens参数
- 优化提示词长度

### 2. 如果六爻计算耗时过长
- 检查算法实现
- 考虑添加计算缓存

### 3. 如果数据提取耗时过长
- 优化数据提取逻辑
- 减少不必要的数据处理

### 4. 如果网络请求耗时过长
- 检查网络环境
- 调整超时配置
- 考虑使用CDN或更近的API节点

## 故障排查

### 1. 性能监控不显示
检查配置：
```properties
performance.monitor.enabled=true
performance.monitor.console-output=true
```

### 2. 日志级别设置
确保日志级别正确：
```properties
logging.level.com.shandai.xuan.util.PerformanceMonitor=INFO
```

### 3. 查看详细错误信息
如果某个步骤失败，会显示：
```
❌ [session-xxx] 执行失败: AI调用 | 耗时: 2.5s | 错误: 网络超时
```

## 扩展功能

未来可以考虑添加：
- 性能数据持久化存储
- 性能趋势分析
- 性能告警机制
- Web界面的性能监控面板
- 性能数据导出功能
