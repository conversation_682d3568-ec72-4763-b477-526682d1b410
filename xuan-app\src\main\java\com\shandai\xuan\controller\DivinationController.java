package com.shandai.xuan.controller;

import com.shandai.xuan.dto.BaZiRequest;
import com.shandai.xuan.dto.LiuYaoRequest;
import com.shandai.xuan.dto.LiuYaoAiAnalysisRequest;
import com.shandai.xuan.dto.TieBanRequest;
import com.shandai.xuan.service.DivinationService;
import com.shandai.xuan.service.LiuYaoAiAnalysisService;
import com.shandai.xuan.service.StreamingAIService;
import com.shandai.xuan.service.PerformanceOptimizedAIService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xuan.core.bazi.BaZi;
import com.xuan.core.liuyao.LiuYao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/divination")
public class DivinationController {

    private static final Logger logger = LoggerFactory.getLogger(DivinationController.class);

    @Autowired
    private DivinationService divinationService;

    @Autowired
    private LiuYaoAiAnalysisService liuYaoAiAnalysisService;

    @Autowired
    private StreamingAIService streamingAIService;

    @Autowired
    private PerformanceOptimizedAIService optimizedAIService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @PostMapping("/bazi")
    public Map<String, Object> getBaZi(@RequestBody BaZiRequest request) {
        return divinationService.calculateBaZi(request);
    }

    @PostMapping("/liuyao")
    public Map<String, Object> getLiuYao(@RequestBody LiuYaoRequest request) {
        return divinationService.calculateLiuYao(request);
    }

    /**
     * 纯六爻计算接口（不包含AI分析）
     */
    @PostMapping("/liuyao/calculate-only")
    public Map<String, Object> getLiuYaoCalculateOnly(@RequestBody LiuYaoRequest request) {
        return divinationService.calculateLiuYaoWithoutAI(request);
    }

    @PostMapping("/tieban")
    public Map<String, Object> getTieBan(@RequestBody TieBanRequest request) {
        return divinationService.calculateTieBan(request);
    }

    /**
     * 六爻AI深度分析接口
     */
    @PostMapping("/liuyao/ai-analysis")
    public Map<String, Object> getLiuYaoAiAnalysis(@RequestBody LiuYaoAiAnalysisRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 执行AI分析
            String aiAnalysis = liuYaoAiAnalysisService.executeAnalysis(request);

            result.put("success", true);
            result.put("aiAnalysis", aiAnalysis);
            result.put("analysisType", request.getAnalysisType());
            result.put("question", request.getQuestion());
            result.put("timestamp", System.currentTimeMillis());

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", "AI分析失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 简化的六爻AI分析接口（兼容现有前端）
     */
    @PostMapping("/liuyao/simple-ai")
    public Map<String, Object> getSimpleLiuYaoAi(@RequestBody LiuYaoRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 构建AI分析请求
            LiuYaoAiAnalysisRequest aiRequest = new LiuYaoAiAnalysisRequest();
            aiRequest.setLiuYaoRequest(request);
            aiRequest.setQuestion(request.getOccupy()); // 使用占事作为问题
            aiRequest.setAnalysisType("comprehensive");

            // 执行AI分析
            String aiAnalysis = liuYaoAiAnalysisService.executeAnalysis(aiRequest);

            result.put("success", true);
            result.put("aiAnalysis", aiAnalysis);

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", "AI分析失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 流式六爻AI分析接口 - 修复超时问题
     */
    @PostMapping(value = "/liuyao/streaming-ai", produces = "text/event-stream")
    public ResponseEntity<StreamingResponseBody> getStreamingLiuYaoAi(@RequestBody LiuYaoRequest request) {
        StreamingResponseBody stream = outputStream -> {
            try {
                // 发送开始信号
                outputStream.write("data: {\"type\":\"start\",\"message\":\"🤖 开始AI分析...\"}\n\n".getBytes("UTF-8"));
                outputStream.flush();

                // 构建AI分析请求
                LiuYaoAiAnalysisRequest aiRequest = new LiuYaoAiAnalysisRequest();
                aiRequest.setLiuYaoRequest(request);
                aiRequest.setQuestion(request.getOccupy());
                aiRequest.setAnalysisType("comprehensive");

                // 发送进度信号
                outputStream.write("data: {\"type\":\"progress\",\"message\":\"📊 正在分析卦象...\"}\n\n".getBytes("UTF-8"));
                outputStream.flush();

                // 执行AI分析
                String aiAnalysis = liuYaoAiAnalysisService.executeAnalysis(aiRequest);

                // 模拟流式输出（将完整结果分块发送）
                String[] sentences = aiAnalysis.split("(?<=[。！？\\n])");
                for (int i = 0; i < sentences.length; i++) {
                    if (!sentences[i].trim().isEmpty()) {
                        String chunk = sentences[i];
                        String jsonData = String.format("data: {\"type\":\"chunk\",\"content\":\"%s\"}\n\n",
                            chunk.replace("\"", "\\\"").replace("\n", "\\n").replace("\r", ""));
                        outputStream.write(jsonData.getBytes("UTF-8"));
                        outputStream.flush();

                        // 添加延迟模拟打字效果
                        Thread.sleep(50);
                    }
                }

                // 发送完成信号
                outputStream.write("data: {\"type\":\"complete\",\"message\":\"✅ 分析完成\"}\n\n".getBytes("UTF-8"));
                outputStream.flush();

            } catch (Exception e) {
                try {
                    String errorData = String.format("data: {\"type\":\"error\",\"message\":\"❌ %s\"}\n\n",
                        e.getMessage().replace("\"", "\\\""));
                    outputStream.write(errorData.getBytes("UTF-8"));
                    outputStream.flush();
                } catch (Exception ignored) {}
            }
        };

        return ResponseEntity.ok()
                .header("Content-Type", "text/event-stream; charset=utf-8")
                .header("Cache-Control", "no-cache")
                .header("Connection", "keep-alive")
                .header("Access-Control-Allow-Origin", "*")
                .body(stream);
    }

    /**
     * 快速六爻AI分析接口
     */
    @PostMapping("/liuyao/fast-ai")
    public Map<String, Object> getFastLiuYaoAi(@RequestBody LiuYaoRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取六爻计算结果（不包含AI分析）
            Map<String, Object> liuYaoResult = divinationService.calculateLiuYaoWithoutAI(request);

            // 检查是否有错误（DivinationService在出错时会返回包含"error"字段的Map）
            if (liuYaoResult.containsKey("error")) {
                result.put("success", false);
                result.put("error", "六爻计算失败：" + liuYaoResult.get("error"));
                return result;
            }

            // 构建简化的提示词
            String prompt = buildFastPrompt(request);

            // 调用快速AI分析
            String aiAnalysis = streamingAIService.getFastAnalysis(prompt);

            result.put("success", true);
            result.put("aiAnalysis", aiAnalysis);
            result.put("data", liuYaoResult); // 直接返回六爻计算结果

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", "快速AI分析失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 构建快速分析提示词
     */
    private String buildFastPrompt(LiuYaoRequest request) {
        return String.format("六爻占卜：%s。请简要分析吉凶并给出建议，控制在100字内。",
                            request.getOccupy());
    }

    /**
     * 优化版流式六爻AI分析接口 - 解决超时和性能问题
     */
    @PostMapping(value = "/liuyao/optimized-streaming-ai", produces = "text/event-stream")
    public ResponseEntity<StreamingResponseBody> getOptimizedStreamingLiuYaoAi(@RequestBody LiuYaoRequest request) {
        StreamingResponseBody stream = outputStream -> {
            try {
                logger.info("开始优化版流式AI分析");

                // 发送开始信号
                outputStream.write("data: {\"type\":\"start\",\"message\":\"🚀 开始优化AI分析...\"}\n\n".getBytes("UTF-8"));
                outputStream.flush();

                // 1. 立即计算六爻基础信息（快速，优先执行）
                outputStream.write("data: {\"type\":\"progress\",\"message\":\"📊 正在计算六爻基础信息...\"}\n\n".getBytes("UTF-8"));
                outputStream.flush();

                // 获取六爻计算结果（不包含AI，真正的几毫秒）
                logger.info("开始计算六爻基础信息（不包含AI）");
                Map<String, Object> liuYaoResult = divinationService.calculateLiuYaoWithoutAI(request);
                logger.info("六爻基础信息计算完成，结果包含data: {}", liuYaoResult.containsKey("data"));

                // 2. 立即发送基础信息给用户
                logger.info("六爻计算结果keys: {}", liuYaoResult.keySet());
                if (liuYaoResult.containsKey("liuyao")) {
                    // calculateLiuYaoWithoutAI 返回的是 {"liuyao": {六爻数据}}
                    Object liuyaoData = liuYaoResult.get("liuyao");
                    logger.info("六爻数据类型: {}", liuyaoData.getClass().getSimpleName());

                    String basicInfoJson = objectMapper.writeValueAsString(liuyaoData);
                    logger.info("基础信息JSON长度: {}, 前100字符: {}", basicInfoJson.length(),
                               basicInfoJson.length() > 100 ? basicInfoJson.substring(0, 100) + "..." : basicInfoJson);

                    outputStream.write(String.format("data: {\"type\":\"basic_info\",\"data\":%s}\n\n", basicInfoJson).getBytes("UTF-8"));
                    outputStream.flush();

                    // 发送基础信息完成信号
                    outputStream.write("data: {\"type\":\"progress\",\"message\":\"✅ 基础信息已显示，正在启动AI分析...\"}\n\n".getBytes("UTF-8"));
                    outputStream.flush();
                    logger.info("基础信息已发送给前端");
                } else if (liuYaoResult.containsKey("data")) {
                    String basicInfoJson = objectMapper.writeValueAsString(liuYaoResult.get("data"));
                    logger.info("基础信息JSON长度: {}", basicInfoJson.length());
                    outputStream.write(String.format("data: {\"type\":\"basic_info\",\"data\":%s}\n\n", basicInfoJson).getBytes("UTF-8"));
                    outputStream.flush();

                    // 发送基础信息完成信号
                    outputStream.write("data: {\"type\":\"progress\",\"message\":\"✅ 基础信息已显示，正在启动AI分析...\"}\n\n".getBytes("UTF-8"));
                    outputStream.flush();
                    logger.info("基础信息已发送给前端");
                } else {
                    logger.warn("六爻计算结果中没有data或liuyao字段，结果keys: {}", liuYaoResult.keySet());
                    outputStream.write("data: {\"type\":\"progress\",\"message\":\"⚠️ 基础信息计算异常，继续AI分析...\"}\n\n".getBytes("UTF-8"));
                    outputStream.flush();
                }

                // 3. 构建AI分析请求
                LiuYaoAiAnalysisRequest aiRequest = new LiuYaoAiAnalysisRequest();
                aiRequest.setLiuYaoRequest(request);
                aiRequest.setQuestion(request.getOccupy());
                aiRequest.setAnalysisType("comprehensive");

                // 4. 现在才发起AI异步分析
                outputStream.write("data: {\"type\":\"ai_start\",\"message\":\"🤖 AI分析已开始，请查看上方基础信息...\"}\n\n".getBytes("UTF-8"));
                outputStream.flush();

                // 使用优化服务执行异步分析
                logger.info("开始调用异步AI分析服务");
                PerformanceOptimizedAIService.AsyncAnalysisResult asyncResult = optimizedAIService.executeAnalysisAsync(aiRequest);
                logger.info("异步AI分析调用完成，RequestID: {}, 排队位置: {}, 状态: {}",
                           asyncResult.getRequestId(), asyncResult.getQueuePosition(), asyncResult.getStatus());

                // 发送排队状态
                if (asyncResult.getQueuePosition() > 0) {
                    String queueMessage = String.format("⏳ 您前面还有%d个用户，预计等待%d分钟",
                        asyncResult.getQueuePosition(), asyncResult.getEstimatedWaitMinutes());
                    outputStream.write(String.format("data: {\"type\":\"queue\",\"message\":\"%s\"}\n\n", queueMessage).getBytes("UTF-8"));
                } else {
                    outputStream.write("data: {\"type\":\"processing\",\"message\":\"🤖 AI正在分析中...\"}\n\n".getBytes("UTF-8"));
                }
                outputStream.flush();

                // 轮询获取结果 - 优化版，防止sleep中断
                String requestId = asyncResult.getRequestId();
                boolean completed = false;
                int pollCount = 0;
                final int maxPolls = 600; // 最多轮询2分钟（200ms * 600 = 120秒）

                while (!completed && pollCount < maxPolls) {
                    try {
                        Thread.sleep(200); // 改为每200毫秒轮询一次，更快响应
                    } catch (InterruptedException e) {
                        // 如果sleep被中断，记录日志但继续执行
                        Thread.currentThread().interrupt();
                        outputStream.write("data: {\"type\":\"progress\",\"message\":\"⚠️ 轮询被中断，正在重试...\"}\n\n".getBytes("UTF-8"));
                        outputStream.flush();
                    }
                    pollCount++;

                    PerformanceOptimizedAIService.TaskStatus status = optimizedAIService.getTaskStatus(requestId);

                    if (status == PerformanceOptimizedAIService.TaskStatus.USER_NOTIFIED ||
                        status == PerformanceOptimizedAIService.TaskStatus.COMPLETED) {

                        // 获取结果
                        try {
                            String aiAnalysis = optimizedAIService.getAsyncResult(requestId).get();

                            // 立即发送完整结果，不要模拟打字效果（节省时间）
                            String jsonData = String.format("data: {\"type\":\"chunk\",\"content\":\"%s\"}\n\n",
                                aiAnalysis.replace("\"", "\\\"").replace("\n", "\\n").replace("\r", ""));
                            outputStream.write(jsonData.getBytes("UTF-8"));
                            outputStream.flush();

                            completed = true;
                        } catch (Exception e) {
                            throw new RuntimeException("获取分析结果失败: " + e.getMessage(), e);
                        }

                    } else if (status == PerformanceOptimizedAIService.TaskStatus.FAILED) {
                        throw new RuntimeException("AI分析失败");
                    } else if (pollCount % 10 == 0) {
                        // 每10秒发送一次进度更新
                        outputStream.write("data: {\"type\":\"progress\",\"message\":\"🔄 AI仍在分析中，请稍候...\"}\n\n".getBytes("UTF-8"));
                        outputStream.flush();
                    }
                }

                if (!completed) {
                    throw new RuntimeException("AI分析超时，请重试");
                }

                // 发送完成信号
                outputStream.write("data: {\"type\":\"complete\",\"message\":\"✅ 优化分析完成\"}\n\n".getBytes("UTF-8"));
                outputStream.flush();

            } catch (Exception e) {
                try {
                    String errorData = String.format("data: {\"type\":\"error\",\"message\":\"❌ 分析失败: %s\"}\n\n",
                        e.getMessage().replace("\"", "\\\""));
                    outputStream.write(errorData.getBytes("UTF-8"));
                    outputStream.flush();
                } catch (Exception ignored) {}
            }
        };

        return ResponseEntity.ok()
                .header("Content-Type", "text/event-stream; charset=utf-8")
                .header("Cache-Control", "no-cache")
                .header("Connection", "keep-alive")
                .header("Access-Control-Allow-Origin", "*")
                .body(stream);
    }
}