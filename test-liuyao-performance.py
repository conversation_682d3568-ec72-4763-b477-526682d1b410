#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的六爻占断性能测试
测试从请求到AI分析完成的全过程用时
"""

import requests
import json
import time
from datetime import datetime

# 应用配置
APP_BASE_URL = "http://localhost:8080"

def test_liuyao_ai_analysis():
    """测试完整的六爻AI分析流程"""
    print("🔮 开始六爻占断性能测试")
    print("="*60)
    
    # 构建六爻请求数据
    liuyao_request = {
        "year": 2025,
        "month": 1,
        "day": 27,
        "hour": 14,
        "minute": 30,
        "sex": 1,  # 男
        "name": "测试用户",
        "occupy": "测试工作运势发展前景",
        "address": "北京",
        "dateType": 0,  # 公历
        "leapMonth": 0,
        "qiGuaMode": 1,  # 自动起卦
        "liuYao": 0,    # 上爻：阳爻
        "wuYao": 1,     # 五爻：阴爻
        "siYao": 0,     # 四爻：阳爻
        "sanYao": 2,    # 三爻：阳爻动
        "erYao": 1,     # 二爻：阴爻
        "yiYao": 0,     # 初爻：阳爻
        "yearGanZhiSet": 1
    }
    
    # 构建AI分析请求
    ai_analysis_request = {
        "liuYaoRequest": liuyao_request,
        "question": "我想了解近期工作运势的发展前景，包括是否有升职机会、工作环境变化、以及需要注意的事项。",
        "analysisType": "comprehensive",
        "needDetailedExplanation": True
    }
    
    print(f"📋 测试数据:")
    print(f"   占事: {liuyao_request['occupy']}")
    print(f"   时间: {liuyao_request['year']}-{liuyao_request['month']}-{liuyao_request['day']} {liuyao_request['hour']}:{liuyao_request['minute']}")
    print(f"   卦象: 上{liuyao_request['liuYao']} 五{liuyao_request['wuYao']} 四{liuyao_request['siYao']} 三{liuyao_request['sanYao']} 二{liuyao_request['erYao']} 初{liuyao_request['yiYao']}")
    print("-" * 60)
    
    # 开始计时
    total_start_time = time.time()
    
    try:
        # 1. 测试完整的AI分析接口
        print("🚀 步骤1: 调用完整AI分析接口")
        step1_start = time.time()
        
        response = requests.post(
            f"{APP_BASE_URL}/api/divination/liuyao/ai-analysis",
            json=ai_analysis_request,
            headers={"Content-Type": "application/json"},
            timeout=300  # 5分钟超时
        )
        
        step1_end = time.time()
        step1_duration = step1_end - step1_start
        
        print(f"   ⏱️ 完整分析用时: {step1_duration:.2f}秒")
        print(f"   📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get("success"):
                ai_analysis = result.get("aiAnalysis", "")
                analysis_length = len(ai_analysis)
                
                print(f"   ✅ 分析成功!")
                print(f"   📝 分析内容长度: {analysis_length} 字符")
                print(f"   🎯 分析类型: {result.get('analysisType', 'N/A')}")
                
                # 显示分析内容的前200字符
                preview = ai_analysis[:200] + "..." if len(ai_analysis) > 200 else ai_analysis
                print(f"   📖 内容预览: {preview}")
                
            else:
                print(f"   ❌ 分析失败: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"   ❌ HTTP请求失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("   ⏰ 请求超时!")
        return False
    except requests.exceptions.ConnectionError:
        print("   🔌 连接错误! 请确保应用已启动")
        return False
    except Exception as e:
        print(f"   💥 未知错误: {e}")
        return False
    
    # 总用时
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    print("-" * 60)
    print("📊 性能测试结果:")
    print(f"   🕐 总用时: {total_duration:.2f}秒")
    print(f"   ⚡ AI分析用时: {step1_duration:.2f}秒")
    
    # 性能评估
    if total_duration < 10:
        performance_level = "🚀 极快"
    elif total_duration < 30:
        performance_level = "⚡ 快速"
    elif total_duration < 60:
        performance_level = "✅ 正常"
    elif total_duration < 120:
        performance_level = "⚠️ 较慢"
    else:
        performance_level = "🐌 很慢"
    
    print(f"   📈 性能评级: {performance_level}")
    
    return True

def test_simple_ai_analysis():
    """测试简化的AI分析接口"""
    print("\n" + "="*60)
    print("🔮 测试简化AI分析接口")
    print("-" * 60)
    
    # 简化的六爻请求
    simple_request = {
        "year": 2025,
        "month": 1,
        "day": 27,
        "hour": 14,
        "minute": 30,
        "sex": 1,
        "name": "测试用户",
        "occupy": "测试感情运势",
        "address": "上海",
        "dateType": 0,
        "leapMonth": 0,
        "qiGuaMode": 1,
        "liuYao": 1,
        "wuYao": 0,
        "siYao": 1,
        "sanYao": 0,
        "erYao": 2,  # 动爻
        "yiYao": 1,
        "yearGanZhiSet": 1
    }
    
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{APP_BASE_URL}/api/divination/liuyao/simple-ai",
            json=simple_request,
            headers={"Content-Type": "application/json"},
            timeout=180
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ 简化分析用时: {duration:.2f}秒")
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                analysis = result.get("aiAnalysis", "")
                print(f"✅ 简化分析成功! 内容长度: {len(analysis)} 字符")
                return True
            else:
                print(f"❌ 简化分析失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"💥 简化分析错误: {e}")
        return False

def test_fast_ai_analysis():
    """测试快速AI分析接口"""
    print("\n" + "="*60)
    print("⚡ 测试快速AI分析接口")
    print("-" * 60)
    
    fast_request = {
        "year": 2025,
        "month": 1,
        "day": 27,
        "hour": 14,
        "minute": 30,
        "sex": 0,  # 女
        "name": "测试用户",
        "occupy": "测试学业运势",
        "address": "广州",
        "dateType": 0,
        "leapMonth": 0,
        "qiGuaMode": 1,
        "liuYao": 0,
        "wuYao": 1,
        "siYao": 0,
        "sanYao": 1,
        "erYao": 0,
        "yiYao": 3,  # 动爻
        "yearGanZhiSet": 1
    }
    
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{APP_BASE_URL}/api/divination/liuyao/fast-ai",
            json=fast_request,
            headers={"Content-Type": "application/json"},
            timeout=120
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ 快速分析用时: {duration:.2f}秒")
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                analysis = result.get("aiAnalysis", "")
                print(f"✅ 快速分析成功! 内容长度: {len(analysis)} 字符")
                return True
            else:
                print(f"❌ 快速分析失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"💥 快速分析错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 六爻占断系统性能测试")
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 应用地址: {APP_BASE_URL}")
    print("="*60)
    
    # 检查应用是否启动
    try:
        health_response = requests.get(f"{APP_BASE_URL}/api/test/config", timeout=5)
        if health_response.status_code == 200:
            print("✅ 应用已启动，开始测试...")
        else:
            print("⚠️ 应用可能未完全启动，但继续测试...")
    except:
        print("❌ 无法连接到应用，请确保应用已启动在 http://localhost:8080")
        return
    
    # 执行测试
    results = []
    
    # 测试1: 完整AI分析
    print("\n🔍 测试1: 完整AI分析")
    result1 = test_liuyao_ai_analysis()
    results.append(("完整AI分析", result1))
    
    # 测试2: 简化AI分析
    print("\n🔍 测试2: 简化AI分析")
    result2 = test_simple_ai_analysis()
    results.append(("简化AI分析", result2))
    
    # 测试3: 快速AI分析
    print("\n🔍 测试3: 快速AI分析")
    result3 = test_fast_ai_analysis()
    results.append(("快速AI分析", result3))
    
    # 总结
    print("\n" + "="*60)
    print("📋 测试总结:")
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)
    
    for test_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试都通过！六爻占断系统运行正常。")
    elif success_count > 0:
        print("⚠️ 部分测试通过，系统基本可用。")
    else:
        print("❌ 所有测试都失败，请检查系统配置。")

if __name__ == "__main__":
    main()
