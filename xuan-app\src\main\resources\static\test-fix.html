<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>六爻功能修复测试</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            align-items: center;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 120px;
        }
        label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            color: white;
            margin-right: 10px;
        }
        .btn-calculate {
            background-color: #4CAF50;
        }
        .btn-ai-fast {
            background-color: #FF9800;
        }
        .btn-ai-deep {
            background-color: #2196F3;
        }
        .result {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #4CAF50;
            white-space: pre-wrap;
            font-family: monospace;
            margin-top: 15px;
            max-height: 500px;
            overflow-y: auto;
        }
        .error {
            color: red;
            background-color: #ffebee;
            border-left-color: #f44336;
        }
        .test-info {
            background-color: #e3f2fd;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h2>🔧 六爻功能修复测试</h2>
        <p><strong>测试目标：</strong></p>
        <ul>
            <li>✅ "计算六爻"按钮应该只进行计算，不调用AI（快速响应）</li>
            <li>✅ AI分析按钮应该在同一区域显示结果，不创建新的显示框</li>
            <li>✅ 计算六爻后再点AI分析，应该在原结果下方追加AI分析</li>
        </ul>
    </div>

    <div class="container">
        <h2>六爻计算测试</h2>
        
        <div class="form-row">
            <div class="form-group">
                <label>年份：</label>
                <input type="number" id="liuyao-year" value="2025" min="1900" max="2100">
            </div>
            <div class="form-group">
                <label>月份：</label>
                <input type="number" id="liuyao-month" value="7" min="1" max="12">
            </div>
            <div class="form-group">
                <label>日期：</label>
                <input type="number" id="liuyao-day" value="29" min="1" max="31">
            </div>
            <div class="form-group">
                <label>小时：</label>
                <input type="number" id="liuyao-hour" value="20" min="0" max="23">
            </div>
            <div class="form-group">
                <label>分钟：</label>
                <input type="number" id="liuyao-minute" value="0" min="0" max="59">
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label>性别：</label>
                <select id="liuyao-sex">
                    <option value="1">男</option>
                    <option value="0">女</option>
                </select>
            </div>
            <div class="form-group">
                <label>姓名：</label>
                <input type="text" id="liuyao-name" value="测试用户" placeholder="请输入姓名">
            </div>
            <div class="form-group">
                <label>占事：</label>
                <input type="text" id="liuyao-occupy" value="测试功能修复" placeholder="请输入占事">
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label>地区：</label>
                <input type="text" id="liuyao-address" value="北京" placeholder="请输入地区">
            </div>
            <div class="form-group">
                <label>日期类型：</label>
                <select id="liuyao-date-type">
                    <option value="0">公历</option>
                    <option value="1">农历</option>
                </select>
            </div>
            <div class="form-group">
                <label>闰月：</label>
                <select id="liuyao-leap-month">
                    <option value="0">平月</option>
                    <option value="1">闰月</option>
                </select>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label>起卦模式：</label>
                <select id="liuyao-qigua-mode">
                    <option value="0">日期起卦</option>
                    <option value="1">手工起卦</option>
                </select>
            </div>
            <div class="form-group">
                <label>年干支设置：</label>
                <select id="liuyao-year-ganzhi-set">
                    <option value="0">以正月初一作为新年的开始</option>
                    <option value="1">以立春当天作为新年的开始</option>
                    <option value="2">以立春交接的时刻作为新年的开始</option>
                </select>
            </div>
        </div>

        <!-- 手工起卦选项 -->
        <div class="form-row">
            <div class="form-group">
                <label>六爻：</label>
                <select id="liuyao-liuyao">
                    <option value="0">—（2正1背）</option>
                    <option value="1">- -（1正2背）</option>
                    <option value="2">— ○（0正3背）</option>
                    <option value="3">- - ×（3正0背）</option>
                </select>
            </div>
            <div class="form-group">
                <label>五爻：</label>
                <select id="liuyao-wuyao">
                    <option value="0">—（2正1背）</option>
                    <option value="1">- -（1正2背）</option>
                    <option value="2">— ○（0正3背）</option>
                    <option value="3">- - ×（3正0背）</option>
                </select>
            </div>
            <div class="form-group">
                <label>四爻：</label>
                <select id="liuyao-siyao">
                    <option value="0">—（2正1背）</option>
                    <option value="1">- -（1正2背）</option>
                    <option value="2">— ○（0正3背）</option>
                    <option value="3">- - ×（3正0背）</option>
                </select>
            </div>
            <div class="form-group">
                <label>三爻：</label>
                <select id="liuyao-sanyao">
                    <option value="0">—（2正1背）</option>
                    <option value="1">- -（1正2背）</option>
                    <option value="2">— ○（0正3背）</option>
                    <option value="3">- - ×（3正0背）</option>
                </select>
            </div>
            <div class="form-group">
                <label>二爻：</label>
                <select id="liuyao-eryao">
                    <option value="0">—（2正1背）</option>
                    <option value="1">- -（1正2背）</option>
                    <option value="2">— ○（0正3背）</option>
                    <option value="3">- - ×（3正0背）</option>
                </select>
            </div>
            <div class="form-group">
                <label>一爻：</label>
                <select id="liuyao-yiyao">
                    <option value="0">—（2正1背）</option>
                    <option value="1">- -（1正2背）</option>
                    <option value="2">— ○（0正3背）</option>
                    <option value="3">- - ×（3正0背）</option>
                </select>
            </div>
        </div>

        <div class="form-row">
            <button class="btn-calculate" onclick="testCalculateOnly()">🧮 计算六爻（纯计算）</button>
            <button class="btn-ai-fast" onclick="testAIFast()">⚡ AI快速分析</button>
            <button class="btn-ai-deep" onclick="testAIDeep()">🤖 AI深度分析（传统）</button>
            <button class="btn-ai-deep" onclick="testStreamingAI()" style="background-color: #9C27B0;">🌊 AI流式分析</button>
        </div>

        <div id="test-result" class="result" style="display: none;"></div>
    </div>

    <script>
        // 测试纯六爻计算（不包含AI）
        async function testCalculateOnly() {
            const startTime = Date.now();
            const resultDiv = document.getElementById('test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '🧮 正在计算六爻（纯计算，不调用AI）...';

            try {
                const data = collectFormData();
                console.log('🧮 发送纯计算请求:', data);

                const response = await fetch('/api/divination/liuyao/calculate-only', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                const endTime = Date.now();
                const duration = endTime - startTime;

                if (result.error) {
                    throw new Error(result.error);
                }

                const liuyaoData = result.liuyao;
                let displayText = `✅ 六爻计算完成！耗时: ${duration}ms\n\n`;
                displayText += `公历：${liuyaoData.solarStr}\n`;
                displayText += `农历：${liuyaoData.lunarStr}\n`;
                displayText += `星期：${liuyaoData.week}\n`;
                displayText += `本卦：${liuyaoData.benGua}\n`;
                displayText += `本卦卦象：${liuyaoData.benGuaAs}\n`;
                displayText += `断卦：${liuyaoData.duangua}\n\n`;
                displayText += `📊 性能测试结果：\n`;
                displayText += `- 响应时间：${duration}ms\n`;
                displayText += `- 是否调用AI：❌ 否（符合预期）\n`;
                displayText += `- 数据完整性：✅ 完整\n`;

                resultDiv.innerHTML = displayText;

            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 计算失败！耗时: ${duration}ms\n错误: ${error.message}`;
            }
        }

        // 测试AI快速分析
        async function testAIFast() {
            const resultDiv = document.getElementById('test-result');
            
            // 检查是否已有计算结果
            if (resultDiv.style.display === 'none' || resultDiv.innerHTML.includes('❌')) {
                alert('请先点击"计算六爻"按钮进行基础计算！');
                return;
            }

            try {
                // 清除之前的AI分析结果
                const existingAiSection = document.getElementById('ai-analysis-section');
                if (existingAiSection) {
                    existingAiSection.remove();
                }
                
                // 添加AI分析状态
                const currentContent = resultDiv.innerHTML;
                resultDiv.innerHTML = currentContent + '<div id="ai-analysis-section" style="margin-top: 20px; border-left: 4px solid #FF9800; padding-left: 15px;"><div style="color: #FF9800; font-weight: bold;">⚡ AI正在快速分析中，请稍候...</div></div>';

                const data = collectFormData();
                const response = await fetch('/api/divination/liuyao/fast-ai', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    let displayHtml = '<h3 style="color: #FF9800;">⚡ AI快速分析结果</h3>';
                    displayHtml += '<div style="background-color: #fff3e0; padding: 15px; border-radius: 5px; line-height: 1.6;">';
                    displayHtml += result.aiAnalysis.replace(/\n/g, '<br>');
                    displayHtml += '</div>';
                    displayHtml += '<p style="color: #666; font-size: 12px; margin-top: 10px;">快速分析时间: ' + new Date().toLocaleString() + '</p>';

                    const aiSection = document.getElementById('ai-analysis-section');
                    if (aiSection) {
                        aiSection.innerHTML = displayHtml;
                    }
                } else {
                    const aiSection = document.getElementById('ai-analysis-section');
                    if (aiSection) {
                        aiSection.innerHTML = '<div style="color: red;">快速AI分析失败: ' + (result.error || '未知错误') + '</div>';
                    }
                }

            } catch (error) {
                const aiSection = document.getElementById('ai-analysis-section');
                if (aiSection) {
                    aiSection.innerHTML = '<div style="color: red;">快速AI分析出错: ' + error.message + '</div>';
                }
            }
        }

        // 测试AI深度分析
        async function testAIDeep() {
            const resultDiv = document.getElementById('test-result');
            
            // 检查是否已有计算结果
            if (resultDiv.style.display === 'none' || resultDiv.innerHTML.includes('❌')) {
                alert('请先点击"计算六爻"按钮进行基础计算！');
                return;
            }

            try {
                // 清除之前的AI分析结果
                const existingAiSection = document.getElementById('ai-analysis-section');
                if (existingAiSection) {
                    existingAiSection.remove();
                }
                
                // 添加AI分析状态
                const currentContent = resultDiv.innerHTML;
                resultDiv.innerHTML = currentContent + '<div id="ai-analysis-section" style="margin-top: 20px; border-left: 4px solid #4CAF50; padding-left: 15px;"><div style="color: #4CAF50; font-weight: bold;">🤖 AI正在深度分析中，请稍候...</div></div>';

                const data = collectFormData();
                const response = await fetch('/api/divination/liuyao/simple-ai', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    let displayHtml = '<h3 style="color: #4CAF50;">🤖 AI深度分析结果</h3>';
                    displayHtml += '<div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; line-height: 1.6;">';
                    displayHtml += result.aiAnalysis.replace(/\n/g, '<br>');
                    displayHtml += '</div>';
                    displayHtml += '<div style="margin-top: 10px; font-size: 12px; color: #666;">分析时间: ' + new Date().toLocaleString() + '</div>';

                    const aiSection = document.getElementById('ai-analysis-section');
                    if (aiSection) {
                        aiSection.innerHTML = displayHtml;
                    }
                } else {
                    const aiSection = document.getElementById('ai-analysis-section');
                    if (aiSection) {
                        aiSection.innerHTML = '<div style="color: red;">深度AI分析失败: ' + (result.error || '未知错误') + '</div>';
                    }
                }

            } catch (error) {
                const aiSection = document.getElementById('ai-analysis-section');
                if (aiSection) {
                    aiSection.innerHTML = '<div style="color: red;">深度AI分析出错: ' + error.message + '</div>';
                }
            }
        }

        // 测试流式AI分析
        async function testStreamingAI() {
            const resultDiv = document.getElementById('test-result');

            // 检查是否已有计算结果
            if (resultDiv.style.display === 'none' || resultDiv.innerHTML.includes('❌')) {
                alert('请先点击"计算六爻"按钮进行基础计算！');
                return;
            }

            try {
                // 清除之前的AI分析结果
                const existingAiSection = document.getElementById('ai-analysis-section');
                if (existingAiSection) {
                    existingAiSection.remove();
                }

                // 添加流式AI分析区域
                const currentContent = resultDiv.innerHTML;
                resultDiv.innerHTML = currentContent + `
                    <div id="ai-analysis-section" style="margin-top: 20px; border-left: 4px solid #9C27B0; padding-left: 15px;">
                        <h3 style="color: #9C27B0;">🌊 AI流式分析</h3>
                        <div id="ai-status" style="color: #9C27B0; font-weight: bold; margin-bottom: 10px;">正在初始化...</div>
                        <div id="ai-content" style="background-color: #f3e5f5; padding: 15px; border-radius: 5px; line-height: 1.6; min-height: 50px; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
                        <div id="ai-timestamp" style="margin-top: 10px; font-size: 12px; color: #666;"></div>
                    </div>
                `;

                const data = collectFormData();
                await startStreamingAIAnalysis(data);

            } catch (error) {
                const aiSection = document.getElementById('ai-analysis-section');
                if (aiSection) {
                    aiSection.innerHTML = '<div style="color: red;">流式AI分析出错: ' + error.message + '</div>';
                }
            }
        }

        // 流式AI分析函数
        async function startStreamingAIAnalysis(data) {
            const statusElement = document.getElementById('ai-status');
            const contentElement = document.getElementById('ai-content');
            const timestampElement = document.getElementById('ai-timestamp');

            try {
                const response = await fetch('/api/divination/liuyao/optimized-streaming-ai', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop(); // 保留不完整的行

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                handleStreamingData(data, statusElement, contentElement, timestampElement);
                            } catch (e) {
                                console.warn('解析SSE数据失败:', line, e);
                            }
                        }
                    }
                }

            } catch (error) {
                statusElement.textContent = '❌ 连接失败';
                contentElement.textContent = '无法连接到AI服务：' + error.message;
                timestampElement.textContent = '错误时间: ' + new Date().toLocaleString();
            }
        }

        // 处理流式数据
        function handleStreamingData(data, statusElement, contentElement, timestampElement) {
            switch (data.type) {
                case 'start':
                    statusElement.textContent = data.message;
                    contentElement.textContent = '';
                    timestampElement.textContent = '开始时间: ' + new Date().toLocaleString();
                    break;
                case 'progress':
                    statusElement.textContent = data.message;
                    break;
                case 'chunk':
                    contentElement.textContent += data.content;
                    // 自动滚动到底部
                    contentElement.scrollTop = contentElement.scrollHeight;
                    break;
                case 'complete':
                    statusElement.textContent = data.message;
                    timestampElement.textContent = '完成时间: ' + new Date().toLocaleString();
                    break;
                case 'error':
                    statusElement.textContent = data.message;
                    contentElement.textContent = '分析过程中出现错误，请重试。';
                    timestampElement.textContent = '错误时间: ' + new Date().toLocaleString();
                    break;
            }
        }

        // 收集表单数据
        function collectFormData() {
            return {
                year: parseInt(document.getElementById('liuyao-year').value),
                month: parseInt(document.getElementById('liuyao-month').value),
                day: parseInt(document.getElementById('liuyao-day').value),
                hour: parseInt(document.getElementById('liuyao-hour').value),
                minute: parseInt(document.getElementById('liuyao-minute').value),
                sex: parseInt(document.getElementById('liuyao-sex').value),
                name: document.getElementById('liuyao-name').value,
                occupy: document.getElementById('liuyao-occupy').value,
                address: document.getElementById('liuyao-address').value,
                dateType: parseInt(document.getElementById('liuyao-date-type').value),
                leapMonth: parseInt(document.getElementById('liuyao-leap-month').value),
                qiGuaMode: parseInt(document.getElementById('liuyao-qigua-mode').value),
                liuYao: parseInt(document.getElementById('liuyao-liuyao').value),
                wuYao: parseInt(document.getElementById('liuyao-wuyao').value),
                siYao: parseInt(document.getElementById('liuyao-siyao').value),
                sanYao: parseInt(document.getElementById('liuyao-sanyao').value),
                erYao: parseInt(document.getElementById('liuyao-eryao').value),
                yiYao: parseInt(document.getElementById('liuyao-yiyao').value),
                yearGanZhiSet: parseInt(document.getElementById('liuyao-year-ganzhi-set').value)
            };
        }
    </script>
</body>
</html>
