package com.shandai.xuan.service;

import com.shandai.xuan.config.AiAnalysisConfig;
import com.shandai.xuan.dto.LiuYaoAiAnalysisRequest;
import com.shandai.xuan.dto.LiuYaoData;
import com.shandai.xuan.util.PerformanceMonitor;
import com.xuan.core.liuyao.LiuYao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * 六爻AI分析服务
 */
@Service
public class LiuYaoAiAnalysisService {
    
    private static final Logger logger = LoggerFactory.getLogger(LiuYaoAiAnalysisService.class);
    
    @Autowired
    private AIService aiService;
    
    @Autowired
    private LiuYaoDataExtractor dataExtractor;
    
    @Autowired
    private LiuYaoPromptTemplateService promptTemplateService;
    
    @Autowired
    private AiAnalysisConfig config;

    @Autowired
    private PerformanceMonitor performanceMonitor;
    
    /**
     * 执行六爻AI分析
     */
    public String executeAnalysis(LiuYaoAiAnalysisRequest request) {
        // 清理之前的监控数据并开始新的监控会话
        performanceMonitor.clear();
        performanceMonitor.startStep("总体分析");

        try {
            if (!config.isEnabled()) {
                performanceMonitor.failStep("总体分析", "AI分析功能已禁用");
                return "AI分析功能已禁用";
            }

            // 1. 首先计算基础六爻数据
            performanceMonitor.startStep("六爻计算");
            LiuYao liuYao = calculateLiuYao(request.getLiuYaoRequest());
            performanceMonitor.endStep("六爻计算");

            // 2. 提取分析数据
            performanceMonitor.startStep("数据提取");
            LiuYaoData liuYaoData = dataExtractor.extract(liuYao, request.getQuestion());
            performanceMonitor.endStep("数据提取");

            // 3. 执行AI分析（带缓存）
            performanceMonitor.startStep("缓存处理");
            String result = executeAnalysisWithCache(liuYaoData, request.getAnalysisType());
            performanceMonitor.endStep("缓存处理");

            // 完成总体分析
            performanceMonitor.endStep("总体分析");

            // 输出性能报告
            performanceMonitor.printSummary();

            return result;

        } catch (Exception e) {
            performanceMonitor.failStep("总体分析", e.getMessage());
            performanceMonitor.printSummary();
            logger.error("执行六爻AI分析失败", e);
            return "AI分析过程中出现错误：" + e.getMessage();
        } finally {
            // 清理监控数据
            performanceMonitor.clear();
        }
    }
    
    /**
     * 带缓存的AI分析执行
     */
    @Cacheable(value = "liuyao-ai-analysis", 
               key = "#liuYaoData.benGua + '_' + #liuYaoData.occupy + '_' + #analysisType",
               condition = "@aiAnalysisConfig.cacheEnabled")
    public String executeAnalysisWithCache(LiuYaoData liuYaoData, String analysisType) {
        return executeAnalysisInternal(liuYaoData, analysisType);
    }
    
    /**
     * 内部AI分析执行逻辑
     */
    private String executeAnalysisInternal(LiuYaoData liuYaoData, String analysisType) {
        int retryCount = 0;
        Exception lastException = null;

        while (retryCount < config.getMaxRetries()) {
            try {
                // 构建提示词
                performanceMonitor.startStep("提示词构建");
                String prompt = promptTemplateService.buildAnalysisPrompt(liuYaoData, analysisType);
                performanceMonitor.endStep("提示词构建");

                logger.debug("执行AI分析，重试次数: {}", retryCount);

                // 调用AI服务（恢复原来的调用方式）
                String stepName = retryCount == 0 ? "AI调用" : "AI调用(重试" + retryCount + ")";
                performanceMonitor.startStep(stepName);
                String result = aiService.getAnalysis(prompt);
                performanceMonitor.endStep(stepName);

                if (result != null && !result.trim().isEmpty()) {
                    logger.info("AI分析成功完成");
                    return result;
                }

                throw new RuntimeException("AI返回空结果");

            } catch (Exception e) {
                String stepName = retryCount == 0 ? "AI调用" : "AI调用(重试" + retryCount + ")";
                performanceMonitor.failStep(stepName, e.getMessage());

                lastException = e;
                retryCount++;

                if (retryCount < config.getMaxRetries()) {
                    logger.warn("AI分析失败，准备重试 {}/{}: {}",
                               retryCount, config.getMaxRetries(), e.getMessage());

                    performanceMonitor.startStep("重试等待");
                    try {
                        Thread.sleep(config.getRetryIntervalMs() * retryCount);
                        performanceMonitor.endStep("重试等待");
                    } catch (InterruptedException ie) {
                        performanceMonitor.failStep("重试等待", "线程被中断");
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    logger.error("AI分析重试次数已用完", e);
                }
            }
        }

        return "AI分析失败，已重试" + config.getMaxRetries() + "次。最后错误：" +
               (lastException != null ? lastException.getMessage() : "未知错误");
    }
    
    /**
     * 计算六爻数据（复用现有逻辑）
     */
    private LiuYao calculateLiuYao(com.shandai.xuan.dto.LiuYaoRequest request) {
        // 这里复用DivinationService中的逻辑
        // 为了简化，我们直接创建LiuYao对象
        // 在实际应用中，可以考虑将这部分逻辑提取到公共服务中
        
        try {
            java.util.Calendar c = java.util.Calendar.getInstance();
            c.set(request.getYear(), request.getMonth() - 1, request.getDay(), 
                  request.getHour(), request.getMinute(), 0);
            
            com.xuan.core.liuyao.LiuYaoSetting setting = new com.xuan.core.liuyao.LiuYaoSetting();
            setting.setSex(request.getSex());
            setting.setName(request.getName());
            setting.setOccupy(request.getOccupy());
            setting.setAddress(request.getAddress());
            setting.setDate(c.getTime());
            setting.setDateType(request.getDateType());
            setting.setLeapMonth(request.getLeapMonth());
            setting.setQiGuaMode(request.getQiGuaMode());
            setting.setLiuYao(request.getLiuYao());
            setting.setWuYao(request.getWuYao());
            setting.setSiYao(request.getSiYao());
            setting.setSanYao(request.getSanYao());
            setting.setErYao(request.getErYao());
            setting.setYiYao(request.getYiYao());
            setting.setYearGanZhiSet(request.getYearGanZhiSet());
            
            return new LiuYao(setting);
            
        } catch (Exception e) {
            logger.error("计算六爻数据失败", e);
            throw new RuntimeException("计算六爻数据失败：" + e.getMessage(), e);
        }
    }
}
