文本系列
创建对话请求（OpenAI）
Creates a model response for the given chat conversation.

POST /chat/completions

Authorizations
​
Authorization
stringheaderrequired
Use the following format for authentication: Bearer <your api key>

Body
application/json
LLM
VLM
​
model
enum<string>default:Qwen/QwQ-32Brequired
Corresponding Model Name. To better enhance service quality, we will make periodic changes to the models provided by this service, including but not limited to model on/offlining and adjustments to model service capabilities. We will notify you of such changes through appropriate means such as announcements or message pushes where feasible.

Available options: Qwen/Qwen3-235B-A22B-Instruct-2507, baidu/ERNIE-4.5-300B-A47B, moonshotai/Kimi-K2-Instruct, ascend-tribe/pangu-pro-moe, tencent/Hunyuan-A13B-Instruct, MiniMaxAI/MiniMax-M1-80k, Tongyi-Zhiwen/QwenLong-L1-32B, Qwen/Qwen3-30B-A3B, Qwen/Qwen3-32B, Qwen/Qwen3-14B, Qwen/Qwen3-8B, Qwen/Qwen3-235B-A22B, THUDM/GLM-Z1-32B-0414, THUDM/GLM-4-32B-0414, THUDM/GLM-Z1-Rumination-32B-0414, THUDM/GLM-4-9B-0414, THUDM/GLM-4-9B-0414, Qwen/QwQ-32B, Pro/deepseek-ai/DeepSeek-R1, Pro/deepseek-ai/DeepSeek-V3, deepseek-ai/DeepSeek-R1, deepseek-ai/DeepSeek-V3, deepseek-ai/DeepSeek-R1-0528-Qwen3-8B, deepseek-ai/DeepSeek-R1-Distill-Qwen-32B, deepseek-ai/DeepSeek-R1-Distill-Qwen-14B, deepseek-ai/DeepSeek-R1-Distill-Qwen-7B, Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B, deepseek-ai/DeepSeek-V2.5, Qwen/Qwen2.5-72B-Instruct-128K, Qwen/Qwen2.5-72B-Instruct, Qwen/Qwen2.5-32B-Instruct, Qwen/Qwen2.5-14B-Instruct, Qwen/Qwen2.5-7B-Instruct, Qwen/Qwen2.5-Coder-32B-Instruct, Qwen/Qwen2.5-Coder-7B-Instruct, Qwen/Qwen2-7B-Instruct, TeleAI/TeleChat2, THUDM/glm-4-9b-chat, Vendor-A/Qwen/Qwen2.5-72B-Instruct, internlm/internlm2_5-7b-chat, Pro/Qwen/Qwen2.5-7B-Instruct, Pro/Qwen/Qwen2-7B-Instruct, Pro/THUDM/glm-4-9b-chat 
Example:
"Qwen/QwQ-32B"

​
messages
object[]required
A list of messages comprising the conversation so far.

Required array length: 1 - 10 elements

Show child attributes

​
stream
boolean
If set, tokens are returned as Server-Sent Events as they are made available. Stream terminates with data: [DONE]

Example:
false

​
max_tokens
integerdefault:512
The maximum number of tokens to generate.

Required range: 1 <= x <= 16384
Example:
512

​
enable_thinking
booleandefault:true
Switches between thinking and non-thinking modes. Default is True. This field only applies to Qwen3 and tencent/Hunyuan-A13B-Instruct models.

Example:
false

​
thinking_budget
integerdefault:4096
Maximum number of tokens for chain-of-thought output. This field applies to all Reasoning models.

Required range: 128 <= x <= 32768
Example:
4096

​
min_p
numberdefault:0.05
Dynamic filtering threshold that adapts based on token probabilities.This field only applies to Qwen3.

Required range: 0 <= x <= 1
Example:
0.05

​
stop

string | null
Up to 4 sequences where the API will stop generating further tokens. The returned text will not contain the stop sequence.

Example:
null

​
temperature
numberdefault:0.7
Determines the degree of randomness in the response.

Example:
0.7

​
top_p
numberdefault:0.7
The top_p (nucleus) parameter is used to dynamically adjust the number of choices for each predicted token based on the cumulative probabilities.

Example:
0.7

​
top_k
numberdefault:50
Example:
50

​
frequency_penalty
numberdefault:0.5
Example:
0.5

​
n
integerdefault:1
Number of generations to return

Example:
1

​
response_format
object
An object specifying the format that the model must output.

Show child attributes

​
tools
object[]
A list of tools the model may call. Currently, only functions are supported as a tool. Use this to provide a list of functions the model may generate JSON inputs for. A max of 128 functions are supported.

Show child attributes

Response
200

200
application/json

application/json
200

​
id
string
​
choices object[]

Show child attributes

​
usage
object
Hide child attributes

​
usage.prompt_tokens
integer
​
usage.completion_tokens
integer
​
usage.total_tokens
integer
​
created
integer
​
model
string
​
object
enum<string>
Available options: chat.completion 