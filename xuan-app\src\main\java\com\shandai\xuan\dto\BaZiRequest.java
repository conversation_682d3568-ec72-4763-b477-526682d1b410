package com.shandai.xuan.dto;

import lombok.Data;

@Data
public class BaZiRequest {
    private int year;
    private int month;
    private int day;
    private int hour;
    private int minute;
    private int sex; // 性别（0:女。1:男）
    private String name; // 姓名
    private String occupy; // 占事
    private String address; // 地区
    private int dateType; // 日期类型（0:公历。1:农历）
    private int leapMonth; // 闰月（0:不使用闰月。1:使用闰月）
    private int qiYunLiuPai; // 起运流派（0:按天数和时辰数计算。1:按分钟数计算）
    private int yearGanZhiSet; // 年干支设置（0:以正月初一作为新年的开始。1:以立春当天作为新年的开始。2:以立春交接的时刻作为新年的开始）
    private int monthGanZhiSet; // 月干支设置（0:以节交接当天起算。1:以节交接时刻起算）
    private int dayGanZhiSet; // 日干支设置（0:晚子时日干支算当天。1:晚子时日干支算明天）
    private int hourGanZhiSet; // 时干支设置（0:支持早子时和晚子时）
    private int renYuan; // 人元司令分野类型（0:子平真诠法决。1:渊海子平法决。2:星平会海法决。3:三命通会法决。4:神峰通考法决。5:万育吾之法决）
} 