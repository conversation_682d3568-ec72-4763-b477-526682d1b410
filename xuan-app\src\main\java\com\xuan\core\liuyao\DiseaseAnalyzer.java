package com.xuan.core.liuyao;

import java.util.*;

/**
 * 六爻疾病诊断辅助类
 */
class DiseaseAnalyzer {

    /** 地支 -> 疾病类别 */
    private static final Map<String, String> ZHI_TO_DISEASE = new HashMap<String, String>() {{
        put("亥", "肾、泌尿或生殖系统、血液类疾病");
        put("子", "肾、泌尿或生殖系统、血液类疾病");
        put("寅", "肝胆、神经系统、腿脚疾病");
        put("卯", "肝胆、神经系统、腿脚疾病");
        put("巳", "心脏、肠道、发烧/炎症、眼疾");
        put("午", "心脏、肠道、发烧/炎症、眼疾");
        put("申", "肺部、骨骼、背部、牙齿疾病");
        put("酉", "肺部、骨骼、背部、牙齿疾病");
        put("辰", "脾胃、肠道等消化系统疾病");
        put("戌", "脾胃、肠道等消化系统疾病");
        put("丑", "脾胃、肠道等消化系统疾病");
        put("未", "脾胃、肠道等消化系统疾病");
    }};

    /** 六神 -> 典型症象 */
    private static final Map<String, String> LIU_SHEN_TO_SYMPTOM = new HashMap<String, String>() {{
        put("朱雀", "发烧、发炎、精神错乱");
        put("勾陈", "肿胀、损伤、脾胃病、肿瘤");
        put("腾蛇", "疑难杂症、精神类、失眠");
        put("白虎", "妇科、血液病、产后并发症");
        put("玄武", "阴虚之病");
    }};

    /** 爻位 -> 身体部位 */
    private static final String[] POS_TO_PART = {
            "脚部",           // 初爻 (index 0)
            "膝盖或生殖器",   // 二爻 (1)
            "腰腹/脾胃/肾脏", // 三爻 (2)
            "胸背/心肺",       // 四爻 (3)
            "五官/颈部/心胸",  // 五爻 (4)
            "头部/大脑"        // 上爻 (5)
    };

    /**
     * 主诊断入口
     * @param ly 已完成 calculateBenGuaInformation 的 LiuYao
     * @return 诊断文本
     */
    static String analyze(LiuYao ly) {
        List<List<String>> info = ly.getBenguainformation();
        if (info == null || info.size() != 6) return "资料不完整，无法断卦";

        // 1. 查找官鬼爻位置列表
        List<Integer> ghostIdx = findPositionsByKin(ly.getBenGuaLiuYaoLiuQin(), "官鬼");
        if (ghostIdx.isEmpty()) return "卦中无官鬼爻，无法判病";

        // 2. 选用神爻
        int yongIdx = selectYongShen(ly, ghostIdx);

        // 3. 获取基础信息
        String bodyPart = POS_TO_PART[yongIdx];
        String yaoZhi = ly.getBenGuaLiuYaoGanZhi().get(yongIdx).substring(1);
        int score = Integer.parseInt(info.get(yongIdx).get(0));
        boolean voided = "旬空".equals(info.get(yongIdx).get(2));
        String sixShen = ly.getBenGuaLiuYaoLiuShen().get(yongIdx);

        // 4. 组装诊断
        StringBuilder sb = new StringBuilder();
        // 病情轻重
        if (voided) sb.append("官鬼旬空，病不严重。");
        else if (score > 0) sb.append("官鬼旺相，病情偏重。");
        else sb.append("官鬼衰弱，病情相对较轻。");

        // 部位 & 疾病类别
        sb.append(" 主要部位：").append(bodyPart).append("；");
        sb.append(" 可能疾病：").append(ZHI_TO_DISEASE.getOrDefault(yaoZhi, "待查")).append("。");

        // 六神附加症象
        if (LIU_SHEN_TO_SYMPTOM.containsKey(sixShen)) {
            sb.append(" 六神提示：").append(LIU_SHEN_TO_SYMPTOM.get(sixShen)).append("。");
        }

        return sb.toString();
    }

    /* ------------------ 工具方法 ------------------ */
    private static List<Integer> findPositionsByKin(List<String> kinList, String kin) {
        List<Integer> res = new ArrayList<>();
        for (int i = 0; i < kinList.size(); i++) if (kin.equals(kinList.get(i))) res.add(i);
        return res;
    }

    // 简化版用神选择：动 > 世/应 > 取首个
    private static int selectYongShen(LiuYao ly, List<Integer> cand) {
        // 动爻优先
        for (int idx : cand) if ("动爻".equals(ly.getBenguainformation().get(idx).get(1))) return idx;
        // 世/应其次
        for (int idx : cand) {
            String se = ly.getBenGuaLiuYaoShiYing().get(idx);
            if ("世".equals(se) || "应".equals(se)) return idx;
        }
        return cand.get(0);
    }
} 