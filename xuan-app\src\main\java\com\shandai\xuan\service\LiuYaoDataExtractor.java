package com.shandai.xuan.service;

import com.shandai.xuan.dto.LiuYaoData;
import com.xuan.core.liuyao.LiuYao;
import org.springframework.stereotype.Component;

/**
 * 六爻数据提取器 - 从LiuYao对象提取AI分析所需的结构化数据
 */
@Component
public class LiuYaoDataExtractor {
    
    /**
     * 从LiuYao对象提取分析数据
     */
    public LiuYaoData extract(LiuYao liuYao, String question) {
        return LiuYaoData.builder()
                // 基本卦象信息
                .benGua(liuYao.getBenGua())
                .bianGua(liuYao.getBianGua())
                .huGua(liuYao.getHuGua())
                .cuoGua(liuYao.getCuoGua())
                .zongGua(liuYao.getZongGua())
                
                // 六爻详细信息
                .benGuaLiuYaoLiuQin(liuYao.getBenGuaLiuYaoLiuQin())
                .benGuaLiuYaoShiYing(liuYao.getBenGuaLiuYaoShiYing())
                .benGuaLiuYaoWuXing(liuYao.getBenGuaLiuYaoWuXing())
                .benGuaLiuYaoLiuShen(liuYao.getBenGuaLiuYaoLiuShen())
                .benGuaLiuYaoGanZhi(liuYao.getBenGuaLiuYaoGanZhi())
                .liuYaoYaoXiangMarkName(liuYao.getLiuYaoYaoXiangMarkName())
                
                // 变卦信息
                .bianGuaLiuYaoLiuQin(liuYao.getBianGuaLiuYaoLiuQin())
                .bianGuaLiuYaoWuXing(liuYao.getBianGuaLiuYaoWuXing())
                
                // 神煞信息
                .yiMa(liuYao.getYiMa())
                .tianMa(liuYao.getTianMa())
                .tianYiGuiRen(liuYao.getTianYiGuiRen())
                .xianChi(liuYao.getXianChi())
                
                // 八字信息
                .baZi(liuYao.getBaZi())
                .baZiWuXing(liuYao.getBaZiWuXing())
                
                // 时间信息
                .yearGanZhi(liuYao.getYearGanZhi())
                .monthGanZhi(liuYao.getMonthGanZhi())
                .dayGanZhi(liuYao.getDayGanZhi())
                .hourGanZhi(liuYao.getHourGanZhi())
                
                // 卦辞信息
                .benGuaGuaCi(liuYao.getBenGuaGuaCi())
                .bianGuaGuaCi(liuYao.getBianGuaGuaCi())
                
                // 系统断卦结果
                .systemAnalysis(liuYao.getDuangua())
                
                // 占事信息
                .occupy(liuYao.getOccupy())
                .question(question)

                // 本卦内部详细信息
                .benguainformation(liuYao.getBenguainformation())

                .build();
    }
}
