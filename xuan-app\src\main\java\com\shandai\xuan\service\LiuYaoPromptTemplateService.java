package com.shandai.xuan.service;

import com.shandai.xuan.dto.LiuYaoData;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * 六爻提示词模板服务
 */
@Component
public class LiuYaoPromptTemplateService {
    
    /**
     * 构建六爻分析提示词
     */
    public String buildAnalysisPrompt(LiuYaoData data, String analysisType) {
        StringBuilder prompt = new StringBuilder();
        
        // 系统角色设定
        prompt.append(getSystemPrompt()).append("\n\n");
        
        // 卦象信息
        prompt.append(buildGuaXiangContext(data)).append("\n\n");
        
        // 六爻详情
        prompt.append(buildLiuYaoContext(data)).append("\n\n");
        
        // 神煞和八字信息
        prompt.append(buildShenShaContext(data)).append("\n\n");
        
        // 跳过系统断卦参考以减少token使用
        
        // 分析指令
        prompt.append(buildAnalysisInstructions(data, analysisType));
        
        return prompt.toString();
    }
    
    /**
     * 获取系统提示词
     */
    private String getSystemPrompt() {
        return "你是专业的六爻占卜师。请根据卦象信息进行简洁分析，包括：\n" +
                "1. 卦象解读\n" +
                "2. 用神分析\n" +
                "3. 动静变化\n" +
                "4. 吉凶判断\n" +
                "5. 具体建议\n\n" +
                "**重要约束：回答必须控制在500字以内，要求简洁明了，重点突出关键信息。**";
    }
    
    /**
     * 构建卦象信息上下文
     */
    private String buildGuaXiangContext(LiuYaoData data) {
        return String.format("## 卦象\n" +
                "本卦：%s → 变卦：%s",
            data.getBenGua(), data.getBianGua()
        );
    }
    
    /**
     * 构建六爻详情上下文
     */
    private String buildLiuYaoContext(LiuYaoData data) {
        StringBuilder context = new StringBuilder("## 六爻详解\n");

        // 使用benguainformation构建详细的六爻信息
        if (data.getBenguainformation() != null && !data.getBenguainformation().isEmpty()) {
            String[] yaoNames = {"初", "二", "三", "四", "五", "上"};

            for (int i = 0; i < 6; i++) {
                List<String> yaoInfo = data.getBenguainformation().get(i);
                if (yaoInfo != null && yaoInfo.size() >= 10) {
                    // benguainformation格式：[动静, 化状态, 旬空, 伏吟, 化进退, 世应, 六亲, 地支, 五行, 旺衰百分比]
                    String dongJing = yaoInfo.get(0);      // 动爻/静爻
                    String huaState = yaoInfo.get(1);      // 化生/克/废/空
                    String xunKong = yaoInfo.get(2);       // 旬空状态
                    String jinTui = yaoInfo.get(4);        // 化进/化退
                    String shiYing = yaoInfo.get(5);       // 世应
                    String liuQin = yaoInfo.get(6);        // 六亲
                    String diZhi = yaoInfo.get(7);         // 地支
                    String wuXing = yaoInfo.get(8);        // 五行
                    String wangShuai = yaoInfo.get(9);     // 旺衰百分比

                    // 显示所有爻的信息，每一爻都对分析有价值
                    context.append(String.format("%s爻 %s %s %s：",
                        yaoNames[i], liuQin, diZhi, wuXing));

                    // 添加状态信息
                    StringBuilder states = new StringBuilder();
                    if (!"".equals(shiYing)) states.append(shiYing).append(" ");
                    if ("动爻".equals(dongJing)) {
                        states.append("动 ");
                        // 动爻的化状态信息
                        if (!"".equals(huaState)) states.append(huaState).append(" ");
                        if (!"".equals(jinTui)) states.append(jinTui).append(" ");
                    } else {
                        states.append("静 ");
                    }
                    if ("旬空".equals(xunKong)) states.append("旬空 ");
                    states.append("旺衰").append(wangShuai);

                    context.append(states.toString()).append("\n");
                }
            }
        } else {
            // 降级到原有逻辑
            String[] yaoNames = {"上", "五", "四", "三", "二", "初"};
            for (int i = 5; i >= 0; i--) {
                String shiying = safeGet(data.getBenGuaLiuYaoShiYing(), i);
                String dongJing = safeGet(data.getLiuYaoYaoXiangMarkName(), i);
                if (!"".equals(shiying) || "动".equals(dongJing)) {
                    context.append(String.format("%s爻：%s %s\n",
                        yaoNames[5-i], shiying, dongJing));
                }
            }
        }

        return context.toString();
    }

    /**
     * 构建神煞和八字信息上下文
     */
    private String buildShenShaContext(LiuYaoData data) {
        return String.format("## 时间\n日辰：%s", data.getDayGanZhi());
    }

    /**
     * 构建分析指令
     */
    private String buildAnalysisInstructions(LiuYaoData data, String analysisType) {
        return String.format("## 问题\n占事：%s\n\n## 分析要求\n请简要分析吉凶并给出建议。\n\n**重要：回答必须控制在500字以内，要求简洁明了，重点突出。**", data.getOccupy());
    }

    /**
     * 安全获取列表元素
     */
    private String safeGet(java.util.List<String> list, int index) {
        if (list == null || index < 0 || index >= list.size()) {
            return "";
        }
        String value = list.get(index);
        return value != null ? value : "";
    }
}
