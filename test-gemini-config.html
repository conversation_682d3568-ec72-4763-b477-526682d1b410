<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini API配置测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .test-button:hover {
            background-color: #45a049;
        }
        .test-button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .config-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            color: #004085;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Gemini API配置测试</h1>
        
        <div class="config-info">
            <h3>当前配置信息：</h3>
            <p><strong>API域名：</strong> https://gemini.scself1700.dpdns.org</p>
            <p><strong>模型：</strong> gemini-2.5-flash</p>
            <p><strong>API格式：</strong> OpenAI兼容格式</p>
            <p><strong>API密钥：</strong> AIzaSyAT2Xg1_6NZdWlpJ6j6dQ_qwpQcihARf7o</p>
        </div>

        <div class="test-section">
            <h3>🔧 基础连接测试</h3>
            <p>测试与AI服务的基本连接</p>
            <button class="test-button" onclick="testBasicConnection()">测试连接</button>
            <div id="basic-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🎯 六爻分析测试</h3>
            <p>测试六爻AI分析功能</p>
            <button class="test-button" onclick="testLiuYaoAnalysis()">测试六爻分析</button>
            <div id="liuyao-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>⚡ 快速分析测试</h3>
            <p>测试流式AI服务的快速分析</p>
            <button class="test-button" onclick="testFastAnalysis()">测试快速分析</button>
            <div id="fast-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.textContent = message;
        }

        function showLoading(elementId) {
            showResult(elementId, '正在测试中，请稍候...', 'loading');
        }

        async function testBasicConnection() {
            const button = event.target;
            button.disabled = true;
            showLoading('basic-result');

            try {
                const response = await fetch(`${API_BASE}/api/test/ai-basic`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: "你好，请简单回复一下，确认连接正常。"
                    })
                });

                if (response.ok) {
                    const result = await response.text();
                    showResult('basic-result', `✅ 连接成功！\n\nAI回复：\n${result}`, 'success');
                } else {
                    const error = await response.text();
                    showResult('basic-result', `❌ 连接失败！\n\n错误信息：\n${error}`, 'error');
                }
            } catch (error) {
                showResult('basic-result', `❌ 网络错误！\n\n错误信息：\n${error.message}`, 'error');
            } finally {
                button.disabled = false;
            }
        }

        async function testLiuYaoAnalysis() {
            const button = event.target;
            button.disabled = true;
            showLoading('liuyao-result');

            try {
                const testData = {
                    "question": "测试工作运势",
                    "gua": "乾为天",
                    "bianGua": "天风姤",
                    "dongYao": [1],
                    "shiYao": 6,
                    "yingYao": 3,
                    "yongShen": "官鬼",
                    "yuanShen": "妻财",
                    "chouShen": "兄弟"
                };

                const response = await fetch(`${API_BASE}/api/liuyao/ai-analysis`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                if (response.ok) {
                    const result = await response.text();
                    showResult('liuyao-result', `✅ 六爻分析成功！\n\n分析结果：\n${result}`, 'success');
                } else {
                    const error = await response.text();
                    showResult('liuyao-result', `❌ 六爻分析失败！\n\n错误信息：\n${error}`, 'error');
                }
            } catch (error) {
                showResult('liuyao-result', `❌ 网络错误！\n\n错误信息：\n${error.message}`, 'error');
            } finally {
                button.disabled = false;
            }
        }

        async function testFastAnalysis() {
            const button = event.target;
            button.disabled = true;
            showLoading('fast-result');

            try {
                const response = await fetch(`${API_BASE}/api/test/ai-fast`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: "请快速分析一下今日运势，简短回复即可。"
                    })
                });

                if (response.ok) {
                    const result = await response.text();
                    showResult('fast-result', `✅ 快速分析成功！\n\n分析结果：\n${result}`, 'success');
                } else {
                    const error = await response.text();
                    showResult('fast-result', `❌ 快速分析失败！\n\n错误信息：\n${error}`, 'error');
                }
            } catch (error) {
                showResult('fast-result', `❌ 网络错误！\n\n错误信息：\n${error.message}`, 'error');
            } finally {
                button.disabled = false;
            }
        }
    </script>
</body>
</html>
