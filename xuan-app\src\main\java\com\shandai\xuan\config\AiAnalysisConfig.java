package com.shandai.xuan.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * AI分析配置
 */
@Configuration
@ConfigurationProperties(prefix = "ai.analysis")
@Data
public class AiAnalysisConfig {

    /**
     * 是否启用AI分析
     */
    private boolean enabled = true;

    /**
     * 分析超时时间（秒）
     */
    private int timeoutSeconds = 60;

    /**
     * 最大重试次数
     */
    private int maxRetries = 3;

    /**
     * 重试间隔（毫秒）
     */
    private long retryIntervalMs = 1000;

    /**
     * 是否启用缓存
     */
    private boolean cacheEnabled = true;

    /**
     * 缓存过期时间（分钟）
     */
    private int cacheExpirationMinutes = 30;

    /**
     * 默认模型名称
     */
    private String defaultModel = "gemini-2.5-flash";

    /**
     * 温度参数
     */
    private double temperature = 0.7;

    /**
     * 最大token数
     */
    private int maxTokens = 2048;

    /**
     * 是否启用模型使用监控
     */
    private boolean usageMonitoringEnabled = true;

    /**
     * 是否启用自动模型切换
     */
    private boolean autoModelSwitchEnabled = true;

    /**
     * 使用率阈值（百分比），超过此阈值时切换模型
     */
    private double usageThreshold = 80.0;

    /**
     * 监控检查间隔（秒）
     */
    private int monitoringIntervalSeconds = 30;
}
