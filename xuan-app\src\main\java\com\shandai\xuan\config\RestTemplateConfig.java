package com.shandai.xuan.config;

import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

/**
 * RestTemplate配置 - 优化版本
 * 使用Apache HttpClient连接池提升性能
 */
@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate() {
        // 创建连接池管理器
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(50);  // 最大连接数
        connectionManager.setDefaultMaxPerRoute(20);  // 每个路由的最大连接数
        connectionManager.setValidateAfterInactivity(2000);  // 2秒后验证连接
        connectionManager.closeIdleConnections(30, TimeUnit.SECONDS);  // 关闭30秒空闲连接

        // 创建请求配置
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(10000)  // 连接超时10秒（减少等待时间）
                .setSocketTimeout(60000)   // 读取超时60秒（AI响应时间）
                .setConnectionRequestTimeout(5000)  // 从连接池获取连接超时5秒
                .build();

        // 创建HttpClient
        HttpClient httpClient = HttpClientBuilder.create()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .setKeepAliveStrategy((response, context) -> 30 * 1000)  // Keep-Alive 30秒
                .setRetryHandler((exception, executionCount, context) -> {
                    // 简单重试策略：网络异常时重试1次
                    return executionCount < 2;
                })
                .build();

        // 创建请求工厂
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);

        return new RestTemplate(factory);
    }
}
