功能简介：给六爻模块LiuYao.java设计一个新功能，根据卦和固定的占事内容来生成吉凶判断结果。

rule：采用组合模式(Composite Pattern)和封装方法的设计思路将具体的计算逻辑封装在各个私有方法内最后在组合起来统一使用，每个方法只负责一个特定计算任务；跟六个爻信息有关的list，规律是第0号元素对应第一爻，第1号元素对应第二爻，第2号元素对应第三爻，第4号元素对应第五爻，第5号元素对应第六爻
需求：
1、数据准备说明：occupy是已经定义的占事变量负责接收LiuYaoSetting setting.occupy的值，List<List<String>> guainformation是已经定义的变量主要负责记录六个爻分别的状态String信息；判断地支相冲在LiuYaoMap.java的DI_ZHI_XIANG_CHONG；判断五行相克在LiuYaoMap.java的 DI_ZHI_XIANG_KE；判断五行相克在LiuYaoMap.java的 DI_ZHI_XIANG_SHENG；月地支对爻地支的影响的打分情况在LiuYaoMap.java的YUE_YAO_WANG_SHUAI；日地支对爻地支的影响的打分情况在LiuYaoMap.java的RI_YAO_WANG_SHUAI；本卦六爻从一爻到六爻分别对应的六亲benGuaLiuYaoLiuQin；本卦五行分别对应的benGuaLiuYaoWuXing ；本卦世爻应爻，如果是空字符就不是世爻也不是应爻，benGuaLiuYaoShiYing；本卦藏爻六亲benGuaCangYaoLiuQin；本卦藏爻干支benGuaCangYaoGanZhi，使用时只需要提取地支，因此需要将字符串拆开成两个字符，第0个字符是天干，第1个字符是地支，取第1个字符地支；变卦六亲bianGuaLiuYaoLiuQin；变卦五行bianGuaLiuYaoWuXing；List<string>旬空baZiXunKong一共八个元素，依次分别是[年旬空，月旬空，日旬空，时旬空]，使用时使用日地支，只对比两个日旬空；List<string>baZi记录了年月日时的天干地支依次是[年天干地支，月天干地支，日天干地支，时天干地支]，使用时通常只使用月地支和日地支
2、这个功能需求需要根据occupy的值来切换模式，目前先设定occupy为“考学”和“财运”两个模式
3、财运模式暂时先搁置只留框架
4、新增一个小方法，要能计算并装填返回List<List<String>> benguainformation，数据格式是[[第一爻信息],[第二爻信息],[第三爻信息],[第四爻信息],[第五爻信息],[第六爻信息]]，六个爻的信息又分别需要记录如第i爻的[旺衰得分，是否是动爻，化生/克/废/空/合绊，是否旬空，是否伏吟，化进还是化退,是世还是应，第i爻六亲，第i爻地支，第i爻五行]，注意文档表述第i爻代表的是list里第i-1个元素这是人类表述和机器表达的区别注意区别，接下来介绍怎么计算这些信息：第一、旺衰得分。第i爻对应list的第i-1个元素。因为benGuaLiuYaoGanZhi是干支的信息，所以先将benGuaLiuYaoGanZhi内第i爻的字符串信息拆成天干和地支从而将得到的地支记录到中间变量yaozhi，将getMonthGanZhi()和getDayGanZhi()得到的干支同样的办法拆开成天干和地支，分别得到中间变量monthzhi和dayzhi，分别比对第i爻地支和月地支的得分用YUE_YAO_WANG_SHUAI与第i爻地支和日地支的得分用RI_YAO_WANG_SHUAI，两个得分相加得到的结果就是旺衰得分；第二、判断是否是动爻。for循环判别liuYaoYaoXiangMark内第i爻是否是○或者×，如果是○或者×则得到是动爻，否则是静爻；第三、判断是化生/克/废/空。用LiuYaoMap.java的WU_XING_XIANG_SHENG和LiuYaoMap.java的WU_XING_XIANG_KE比较变卦bianGuaLiuYaoWuXing 第i爻的五行是否生本卦benGuaLiuYaoWuXing 第i爻的五行，比如输入bianGuaLiuYaoWuXing第i爻的五行到WU_XING_XIANG_SHENG中如果得到的结果equal了benGuaLiuYaoWuXing 第i爻的五行，则记录化回头生，如果输入bianGuaLiuYaoWuXing第i爻的五行到WU_XING_XIANG_KE的结果符合benGuaLiuYaoWuXing 第i爻的五行则记录化回头克。判断是否化废，使用时需要将bianGuaLiuYaoGanZhi的得到的第i爻的string分别拆成天干和地支，取地支使用从而得到第i爻bianGuaLiuYaoGanZhi的地支，将getMonthGanZhi()拆分干支得到的地支通过LiuYaoMap.java的DI_ZHI_XIANG_CHONG比较，如果结果符合第i爻bianGuaLiuYaoGanZhi拆分得到的地支，则记录化废，如果getDayGanZhi()拆分干支得到的地支使用DI_ZHI_XIANG_CHONG得到的结果符合第i爻bianGuaLiuYaoGanZhi拆分得到的地支并且getDayGanZhi()拆分干支得到的地支比对LiuYaoMap.java的YUE_YAO_WANG_SHUAI如果得分小于0分也记录化废。接下来再确定是否化空，将getDayXunKong()得到的字符串拆开得到两个地支分别比对第i爻bianGuaLiuYaoGanZhi拆分得到的地支如果相同则记录化空。如果化空和化废冲突则选择记录化废不记录化空，记录化废的优先级比化空更高。如果都不是则记录空字符“”；第四、判断是否旬空。将getDayXunKong()得到的字符串拆开得到两个地支分别比对第i爻benGuaLiuYaoGanZhi拆分得到的地支如果相同则记录旬空，否则记录非空；第五、比较第i爻benGuaLiuYaoGanZhi拆分得到的地支和第i爻bianGuaLiuYaoGanZhi拆分得到的地支是否相同，如果相同则记录伏吟，否则记录空字符“”；第六、将第i爻benGuaLiuYaoGanZhi拆分得到的地支用LiuYaoMap.java的DI_ZHI_HUA_JIN_SHEN得到的结果与第i爻bianGuaLiuYaoGanZhi拆分得到的地支是否相同，如果相同则化记录化进；将第i爻benGuaLiuYaoGanZhi拆分得到的地支用LiuYaoMap.java的DI_ZHI_HUA_TUI_SHEN得到的结果与第i爻bianGuaLiuYaoGanZhi拆分得到的地支是否相同，如果相同则记录化退。如果都不同则记录空字符“”第七、复制第i爻benGuaLiuYaoShiYing的结果，记录到第i爻的是世还是应的信息。第八、复制第i爻六亲，benGuaLiuYaoLiuQinbenGuaLiuYaoGanZhi中天干地支的地支，benGuaLiuYaoWuXing五行
5、简单设计。根据第1爻的旺衰打分来得到结果，生成LiuYao.java的private String变量duangua,如果大于等于0分则记录吉否则凶