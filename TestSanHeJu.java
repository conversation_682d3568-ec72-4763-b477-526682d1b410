import com.xuan.core.liuyao.LiuYao;
import com.xuan.core.liuyao.LiuYaoSetting;

import java.util.Calendar;

/**
 * Test SanHeJu functionality
 */
public class TestSanHeJu {

    public static void main(String[] args) {
        System.out.println("=== Test SanHeJu Function ===");

        // Create a LiuYao example with SanHeJu
        <PERSON>aoSetting setting = new LiuYaoSetting();
        setting.setSex(1);
        setting.setName("Test");
        setting.setOccupy("Test SanHeJu");
        setting.setAddress("Beijing");
        
        Calendar c = Calendar.getInstance();
        c.set(2024, 1 - 1, 1, 0, 0, 0);
        setting.setDate(c.getTime());
        
        setting.setDateType(0);
        setting.setLeapMonth(0);
        setting.setQiGuaMode(2); // Manual mode

        // Test case 1: Normal case
        System.out.println("\n--- Test Case 1: Normal case ---");
        testLiuYao(setting, 0, 1, 2, 0, 3, 2);

        // Test case 2: Try to create SanHeJu with different settings
        System.out.println("\n--- Test Case 2: Different settings ---");
        testLiuYao(setting, 1, 0, 3, 2, 1, 0);
        
        setting.setYearGanZhiSet(1);
        setting.setMonthGanZhiSet(1);
        setting.setDayGanZhiSet(1);
        setting.setHourGanZhiSet(0);
    }

    private static void testLiuYao(LiuYaoSetting setting, int liu, int wu, int si, int san, int er, int yi) {
        // Set hexagram lines
        setting.setLiuYao(liu);  // 6th line
        setting.setWuYao(wu);    // 5th line
        setting.setSiYao(si);    // 4th line
        setting.setSanYao(san);  // 3rd line
        setting.setErYao(er);    // 2nd line
        setting.setYiYao(yi);    // 1st line

        try {
            LiuYao liuYao = new LiuYao(setting);
            
            System.out.println("BenGua: " + liuYao.getBenGua());
            System.out.println("BenGua GanZhi: " + liuYao.getBenGuaLiuYaoGanZhi());
            System.out.println("BenGua DiZhi:");
            for (int i = 0; i < 6; i++) {
                String ganZhi = liuYao.getBenGuaLiuYaoGanZhi().get(i);
                String zhi = ganZhi.substring(1);
                System.out.println("Line " + (i+1) + ": " + zhi);
            }

            System.out.println("Moving lines: " + liuYao.getLiuYaoYaoXiangMarkName());
            System.out.println("Month DiZhi: " + liuYao.getMonthGanZhi().substring(1));
            System.out.println("Day DiZhi: " + liuYao.getDayGanZhi().substring(1));

            // Show BianGua info if available
            if (liuYao.getBianGuaLiuYaoGanZhi() != null) {
                System.out.println("BianGua GanZhi: " + liuYao.getBianGuaLiuYaoGanZhi());
                System.out.println("BianGua DiZhi:");
                for (int i = 0; i < 6; i++) {
                    String ganZhi = liuYao.getBianGuaLiuYaoGanZhi().get(i);
                    String zhi = ganZhi.substring(1);
                    System.out.println("Line " + (i+1) + ": " + zhi);
                }
            }
            
            // Test benguainformation
            if (liuYao.getBenguainformation() != null) {
                System.out.println("\nBenGua Information:");
                for (int i = 0; i < liuYao.getBenguainformation().size(); i++) {
                    if (i < 6) {
                        System.out.println("Line " + (i+1) + ": " + liuYao.getBenguainformation().get(i));
                    } else {
                        System.out.println("SanHeJu Info: " + liuYao.getBenguainformation().get(i));

                        // Debug: Manual check for SanHeJu
                        System.out.println("\nDebug - Manual SanHeJu check:");
                        System.out.println("Looking for Shen-Zi-Chen combination:");
                        System.out.println("- Shen: Line 4 = " + liuYao.getBenguainformation().get(3).get(7));
                        System.out.println("- Zi: Line 6 = " + liuYao.getBenguainformation().get(5).get(7) + ", Month = " + liuYao.getMonthGanZhi().substring(1) + ", Day = " + liuYao.getDayGanZhi().substring(1));
                        if (liuYao.getBianGuaLiuYaoGanZhi() != null) {
                            System.out.println("- Chen: BianGua Line 3 = " + liuYao.getBianGuaLiuYaoGanZhi().get(2).substring(1));
                        }
                    }
                }
            } else {
                System.out.println("benguainformation is null");
            }

        } catch (Exception e) {
            System.err.println("Error during test:");
            e.printStackTrace();
        }
    }
}
