package com.shandai.xuan.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.context.request.async.TimeoutCallableProcessingInterceptor;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.concurrent.Executor;

/**
 * 异步和定时任务配置
 */
@Configuration
@EnableAsync
@EnableScheduling
public class AsyncConfig implements WebMvcConfigurer {
    
    /**
     * 异步任务执行器
     */
    @Bean(name = "taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("ModelMonitor-");
        executor.initialize();
        return executor;
    }

    /**
     * 配置异步请求超时时间
     */
    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        // 设置异步请求超时时间为60秒（解决30秒超时问题）
        configurer.setDefaultTimeout(60000);

        // 添加超时处理拦截器
        configurer.registerCallableInterceptors(new TimeoutCallableProcessingInterceptor());
    }
}
