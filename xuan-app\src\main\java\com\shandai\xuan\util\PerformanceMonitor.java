package com.shandai.xuan.util;

import com.shandai.xuan.config.PerformanceConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 性能监控工具类
 * 用于监控AI分析各个步骤的耗时
 */
@Component
public class PerformanceMonitor {

    private static final Logger logger = LoggerFactory.getLogger(PerformanceMonitor.class);

    @Autowired
    private PerformanceConfig config;
    
    // 存储每个线程的性能数据
    private static final ThreadLocal<Map<String, Long>> startTimes = ThreadLocal.withInitial(HashMap::new);
    private static final ThreadLocal<Map<String, Long>> durations = ThreadLocal.withInitial(HashMap::new);
    private static final ThreadLocal<String> sessionId = ThreadLocal.withInitial(() -> 
        "session-" + System.currentTimeMillis() + "-" + Thread.currentThread().getId());
    
    /**
     * 开始监控某个步骤
     */
    public void startStep(String stepName) {
        long startTime = System.currentTimeMillis();
        startTimes.get().put(stepName, startTime);
        
        // 在终端输出开始信息
        String session = sessionId.get();
        System.out.println(String.format("🚀 [%s] 开始执行: %s (时间: %s)", 
            session, stepName, formatTime(startTime)));
        logger.info("🚀 [{}] 开始执行: {} (时间: {})", session, stepName, formatTime(startTime));
    }
    
    /**
     * 结束监控某个步骤
     */
    public long endStep(String stepName) {
        long endTime = System.currentTimeMillis();
        Long startTime = startTimes.get().get(stepName);
        
        if (startTime == null) {
            logger.warn("⚠️ 步骤 {} 没有对应的开始时间", stepName);
            return 0;
        }
        
        long duration = endTime - startTime;
        durations.get().put(stepName, duration);
        
        // 在终端输出结束信息
        String session = sessionId.get();
        String durationStr = formatDuration(duration);
        String status = getPerformanceStatus(stepName, duration);
        
        System.out.println(String.format("✅ [%s] 完成执行: %s | 耗时: %s %s", 
            session, stepName, durationStr, status));
        logger.info("✅ [{}] 完成执行: {} | 耗时: {} {}", session, stepName, durationStr, status);
        
        return duration;
    }
    
    /**
     * 记录步骤失败
     */
    public void failStep(String stepName, String errorMessage) {
        long endTime = System.currentTimeMillis();
        Long startTime = startTimes.get().get(stepName);
        
        long duration = startTime != null ? endTime - startTime : 0;
        String session = sessionId.get();
        String durationStr = formatDuration(duration);
        
        System.out.println(String.format("❌ [%s] 执行失败: %s | 耗时: %s | 错误: %s", 
            session, stepName, durationStr, errorMessage));
        logger.error("❌ [{}] 执行失败: {} | 耗时: {} | 错误: {}", 
            session, stepName, durationStr, errorMessage);
    }
    
    /**
     * 输出完整的性能报告
     */
    public void printSummary() {
        Map<String, Long> currentDurations = durations.get();
        String session = sessionId.get();
        
        if (currentDurations.isEmpty()) {
            System.out.println(String.format("📊 [%s] 性能报告: 无数据", session));
            return;
        }
        
        long totalTime = currentDurations.values().stream().mapToLong(Long::longValue).sum();
        
        System.out.println("\n" + "=".repeat(80));
        System.out.println(String.format("📊 [%s] AI分析性能报告", session));
        System.out.println("=".repeat(80));
        
        // 按执行顺序显示各步骤耗时
        String[] stepOrder = {"六爻计算", "数据提取", "提示词构建", "AI调用", "缓存处理", "重试处理"};
        
        for (String step : stepOrder) {
            Long duration = currentDurations.get(step);
            if (duration != null) {
                double percentage = (duration * 100.0) / totalTime;
                String durationStr = formatDuration(duration);
                String status = getPerformanceStatus(step, duration);
                String bar = generateProgressBar(percentage);
                
                System.out.println(String.format("  %-12s: %8s (%5.1f%%) %s %s", 
                    step, durationStr, percentage, bar, status));
            }
        }
        
        System.out.println("-".repeat(80));
        System.out.println(String.format("  %-12s: %8s (100.0%%)", "总耗时", formatDuration(totalTime)));
        System.out.println("=".repeat(80) + "\n");
        
        // 记录到日志
        logger.info("📊 [{}] AI分析总耗时: {}", session, formatDuration(totalTime));
    }
    
    /**
     * 清理当前线程的监控数据
     */
    public void clear() {
        startTimes.get().clear();
        durations.get().clear();
        sessionId.remove();
    }
    
    /**
     * 格式化时间戳
     */
    private String formatTime(long timestamp) {
        return new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(new java.util.Date(timestamp));
    }
    
    /**
     * 格式化持续时间
     */
    private String formatDuration(long duration) {
        if (duration < 1000) {
            return duration + "ms";
        } else if (duration < 60000) {
            return String.format("%.2fs", duration / 1000.0);
        } else {
            long minutes = duration / 60000;
            long seconds = (duration % 60000) / 1000;
            return String.format("%dm%02ds", minutes, seconds);
        }
    }
    
    /**
     * 获取性能状态标识
     */
    private String getPerformanceStatus(String stepName, long duration) {
        // 根据不同步骤设置不同的性能阈值
        long threshold = getThresholdForStep(stepName);

        if (duration <= threshold) {
            return "🟢"; // 绿色 - 性能良好
        } else if (duration <= threshold * 2) {
            return "🟡"; // 黄色 - 性能一般
        } else {
            return "🔴"; // 红色 - 性能较差
        }
    }

    /**
     * 获取步骤对应的性能阈值
     */
    private long getThresholdForStep(String stepName) {
        if (config == null) {
            return 1000; // 默认阈值
        }

        PerformanceConfig.Thresholds thresholds = config.getThresholds();
        switch (stepName) {
            case "六爻计算":
                return thresholds.getLiuyaoCalculation();
            case "数据提取":
                return thresholds.getDataExtraction();
            case "提示词构建":
                return thresholds.getPromptBuilding();
            case "AI调用":
            case "AI调用(重试1)":
            case "AI调用(重试2)":
                return thresholds.getAiCall();
            case "缓存处理":
                return thresholds.getCacheProcessing();
            case "网络请求":
                return thresholds.getNetworkRequest();
            case "响应解析":
                return thresholds.getResponseParsing();
            default:
                return 1000; // 默认阈值
        }
    }
    
    /**
     * 生成进度条
     */
    private String generateProgressBar(double percentage) {
        int barLength = 20;
        int filled = (int) (percentage * barLength / 100);
        StringBuilder bar = new StringBuilder("[");
        
        for (int i = 0; i < barLength; i++) {
            if (i < filled) {
                bar.append("█");
            } else {
                bar.append("░");
            }
        }
        bar.append("]");
        return bar.toString();
    }
    
    /**
     * 获取当前步骤的耗时
     */
    public long getStepDuration(String stepName) {
        return durations.get().getOrDefault(stepName, 0L);
    }
    
    /**
     * 检查是否有正在执行的步骤
     */
    public boolean hasRunningStep(String stepName) {
        return startTimes.get().containsKey(stepName) && !durations.get().containsKey(stepName);
    }
}
