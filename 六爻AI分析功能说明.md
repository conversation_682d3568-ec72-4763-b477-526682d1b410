# 六爻AI分析功能说明

## 功能概述

基于您现有的六爻计算系统，新增了AI深度分析功能，能够调用LiuYao.java的成员变量，结合硅基流动API进行专业的六爻分析，并通过前端界面展示结果。

## 新增功能

### 1. 后端API接口

#### 完整AI分析接口
- **路径**: `POST /api/divination/liuyao/ai-analysis`
- **功能**: 提供完整的AI分析功能，支持自定义问题和分析类型
- **请求体**: `LiuYaoAiAnalysisRequest`

#### 简化AI分析接口  
- **路径**: `POST /api/divination/liuyao/simple-ai`
- **功能**: 兼容现有前端的简化AI分析接口
- **请求体**: `LiuYaoRequest`（复用现有结构）

### 2. 前端界面

- 在六爻计算页面新增"AI深度分析"按钮
- 点击后调用AI分析接口，展示专业的六爻分析结果
- 支持实时显示分析进度和结果

### 3. 核心特性

- **数据提取**: 自动从LiuYao对象提取所有相关数据
- **智能提示词**: 结构化的专业六爻分析提示词模板
- **错误重试**: 支持最多3次重试机制
- **结果缓存**: 相同卦象的分析结果会被缓存30分钟
- **配置灵活**: 支持通过配置文件调整AI分析参数

## 技术架构

```
前端 (index.html)
    ↓
控制器 (DivinationController)
    ↓
AI分析服务 (LiuYaoAiAnalysisService)
    ↓
数据提取器 (LiuYaoDataExtractor) + 提示词服务 (LiuYaoPromptTemplateService)
    ↓
AI服务 (AIService) → 硅基流动API
```

## 使用方法

### 1. 启动应用
```bash
mvn clean install
mvn spring-boot:run -pl xuan-app
```

### 2. 访问前端
打开浏览器访问: `http://localhost:8080`

### 3. 使用AI分析
1. 填写六爻计算表单（年月日时分、占事等）
2. 点击"AI深度分析"按钮
3. 等待AI分析完成，查看详细的专业分析结果

## 配置说明

在 `application.properties` 中可以调整以下配置：

```properties
# AI分析开关
ai.analysis.enabled=true

# 分析超时时间（秒）
ai.analysis.timeout-seconds=60

# 最大重试次数
ai.analysis.max-retries=3

# 重试间隔（毫秒）
ai.analysis.retry-interval-ms=1000

# 缓存配置
ai.analysis.cache-enabled=true
ai.analysis.cache-expiration-minutes=30

# AI模型配置
ai.analysis.default-model=Qwen/Qwen2.5-72B-Instruct
ai.analysis.temperature=0.7
ai.analysis.max-tokens=2048
```

## 环境要求

- Java 8+
- Maven 3.6+
- Spring Boot 2.7.14
- 有效的硅基流动API密钥

## 注意事项

1. **API密钥**: 确保在 `application.properties` 中配置了有效的硅基流动API密钥
2. **网络连接**: 需要稳定的网络连接访问硅基流动API
3. **缓存**: 首次分析可能较慢，后续相同卦象的分析会使用缓存结果
4. **错误处理**: 如果AI分析失败，会显示详细的错误信息

## 扩展功能

未来可以考虑添加：
- 流式响应（实时显示分析过程）
- 分析结果的数据库存储
- 用户反馈和模型优化
- 多种分析模式（简要、详细、专业等）
- 分析历史记录查看
