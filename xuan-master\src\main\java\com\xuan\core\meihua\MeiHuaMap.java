package com.xuan.core.meihua;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 梅花易数-常量
 *
 * <AUTHOR>
 */
public class MeiHuaMap {

    /**
     * 地支对应的数字
     */
    public static final Map<String, Integer> DI_ZHI_SHU = new HashMap<String, Integer>() {
        private static final long serialVersionUID = -1;

        {
            put("子", 1);
            put("丑", 2);
            put("寅", 3);
            put("卯", 4);
            put("辰", 5);
            put("巳", 6);
            put("午", 7);
            put("未", 8);
            put("申", 9);
            put("酉", 10);
            put("戌", 11);
            put("亥", 12);
        }
    };

//----------------------------------------------------------------------------------------------------------------------------------------------------

    /**
     * 先天八卦
     */
    public static final Map<Integer, String> XIAN_YIAN_BA_GUA = new HashMap<Integer, String>() {
        private static final long serialVersionUID = -1;

        {
            put(1, "乾");
            put(2, "兑");
            put(3, "离");
            put(4, "震");
            put(5, "巽");
            put(6, "坎");
            put(7, "艮");
            put(8, "坤");
        }
    };

    /**
     * 八卦卦象（八卦卦名为键）
     */
    public static final Map<String, String> BA_GUA_AS = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("乾", "☰");
            put("兑", "☱");
            put("离", "☲");
            put("震", "☳");
            put("巽", "☴");
            put("坎", "☵");
            put("艮", "☶");
            put("坤", "☷");
        }
    };

    /**
     * 八卦五行
     */
    public static final Map<String, String> BA_GUA_WU_XING = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("乾", "金");
            put("兑", "金");
            put("离", "火");
            put("震", "木");
            put("巽", "木");
            put("坎", "水");
            put("艮", "土");
            put("坤", "土");
        }
    };

    /**
     * 八卦卦象代表的数字
     */
    public static final Map<String, Integer> BA_GUA_SHU = new HashMap<String, Integer>() {
        private static final long serialVersionUID = -1;

        {
            put("☰", 1);
            put("☱", 2);
            put("☲", 3);
            put("☳", 4);
            put("☴", 5);
            put("☵", 6);
            put("☶", 7);
            put("☷", 8);
        }
    };

    /**
     * 八卦卦象的三爻数字（八卦卦象为键）
     */
    public static final Map<String, List<Integer>> BA_GUA_AS_SHU = new HashMap<String, List<Integer>>() {
        private static final long serialVersionUID = -1;

        /* 自上而下：0为阴，1为阳 */ {
            put("☰", Arrays.asList(1, 1, 1)); // 乾卦（☰）；上爻为1，中爻为1，下爻为1
            put("☱", Arrays.asList(0, 1, 1)); // 兑卦（☱）；上爻为0，中爻为1，下爻为1
            put("☲", Arrays.asList(1, 0, 1)); // 离卦（☲）；上爻为1，中爻为0，下爻为1
            put("☳", Arrays.asList(0, 0, 1)); // 震卦（☳）；上爻为0，中爻为0，下爻为1
            put("☴", Arrays.asList(1, 1, 0)); // 巽卦（☴）；上爻为1，中爻为1，下爻为0
            put("☵", Arrays.asList(0, 1, 0)); // 坎卦（☵）；上爻为0，中爻为1，下爻为0
            put("☶", Arrays.asList(1, 0, 0)); // 艮卦（☶）；上爻为1，中爻为0，下爻为0
            put("☷", Arrays.asList(0, 0, 1)); // 坤卦（☷）；上爻为0，中爻为0，下爻为1
        }
    };

    /**
     * 八卦卦象的三爻数字对应的八卦卦象（八卦的三爻数字为键）
     */
    public static final Map<List<Integer>, String> BA_GUA_SHU_AS = new HashMap<List<Integer>, String>() {
        private static final long serialVersionUID = -1;

        /* 自上而下：0为阴，1为阳 */ {
            put(Arrays.asList(1, 1, 1), "☰"); // 上爻为1，中爻为1，下爻为1：乾卦（☰）
            put(Arrays.asList(0, 1, 1), "☱"); // 上爻为0，中爻为1，下爻为1：兑卦（☱）
            put(Arrays.asList(1, 0, 1), "☲"); // 上爻为1，中爻为0，下爻为1：离卦（☲）
            put(Arrays.asList(0, 0, 1), "☳"); // 上爻为0，中爻为0，下爻为1：震卦（☳）
            put(Arrays.asList(1, 1, 0), "☴"); // 上爻为1，中爻为1，下爻为0：巽卦（☴）
            put(Arrays.asList(0, 1, 0), "☵"); // 上爻为0，中爻为1，下爻为0：坎卦（☵）
            put(Arrays.asList(1, 0, 0), "☶"); // 上爻为1，中爻为0，下爻为0：艮卦（☶）
            put(Arrays.asList(0, 0, 1), "☷"); // 上爻为0，中爻为0，下爻为1：坤卦（☷）
        }
    };

//----------------------------------------------------------------------------------------------------------------------------------------------------

    /**
     * 六十四卦卦象（六十四卦卦名为键）
     */
    public static final Map<String, String> LIU_SHI_SI_GUA_AS = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("乾为天", "䷀");
            put("坤为地", "䷁");
            put("水雷屯", "䷂");
            put("山水蒙", "䷃");
            put("水天需", "䷄");
            put("天水讼", "䷅");
            put("地水师", "䷆");
            put("水地比", "䷇");
            put("风天小畜", "䷈");
            put("天泽履", "䷉");
            put("地天泰", "䷊");
            put("天地否", "䷋");
            put("天火同人", "䷌");
            put("火天大有", "䷍");
            put("地山谦", "䷎");
            put("雷地豫", "䷏");
            put("泽雷随", "䷐");
            put("山风蛊", "䷑");
            put("地泽临", "䷒");
            put("风地观", "䷓");
            put("火雷噬嗑", "䷔");
            put("山火贲", "䷕");
            put("山地剥", "䷖");
            put("地雷复", "䷗");
            put("天雷无妄", "䷘");
            put("山天大畜", "䷙");
            put("山雷颐", "䷚");
            put("泽风大过", "䷛");
            put("坎为水", "䷜");
            put("离为火", "䷝");
            put("泽山咸", "䷞");
            put("雷风恒", "䷟");
            put("天山遁", "䷠");
            put("雷天大壮", "䷡");
            put("火地晋", "䷢");
            put("地火明夷", "䷣");
            put("风火家人", "䷤");
            put("火泽睽", "䷥");
            put("水山蹇", "䷦");
            put("雷水解", "䷧");
            put("山泽损", "䷨");
            put("风雷益", "䷩");
            put("泽天夬", "䷪");
            put("天风姤", "䷫");
            put("泽地萃", "䷬");
            put("地风升", "䷭");
            put("泽水困", "䷮");
            put("水风井", "䷯");
            put("泽火革", "䷰");
            put("火风鼎", "䷱");
            put("震为雷", "䷲");
            put("艮为山", "䷳");
            put("风山渐", "䷴");
            put("雷泽归妹", "䷵");
            put("雷火丰", "䷶");
            put("火山旅", "䷷");
            put("巽为风", "䷸");
            put("兑为泽", "䷹");
            put("风水涣", "䷺");
            put("水泽节", "䷻");
            put("风泽中孚", "䷼");
            put("雷山小过", "䷽");
            put("水火既济", "䷾");
            put("火水未济", "䷿");
        }

    };

    /**
     * 六十四卦卦名（上卦数+下卦数为键）
     */
    public static final Map<List<Integer>, String> LIU_SHI_SI_GUA_NAME = new HashMap<List<Integer>, String>() {
        private static final long serialVersionUID = -1;

        {
            put(Arrays.asList(1, 1), "乾为天");
            put(Arrays.asList(8, 8), "坤为地");
            put(Arrays.asList(6, 4), "水雷屯");
            put(Arrays.asList(7, 6), "山水蒙");
            put(Arrays.asList(6, 1), "水天需");
            put(Arrays.asList(1, 6), "天水讼");
            put(Arrays.asList(8, 6), "地水师");
            put(Arrays.asList(6, 8), "水地比");
            put(Arrays.asList(5, 1), "风天小畜");
            put(Arrays.asList(1, 2), "天泽履");
            put(Arrays.asList(8, 1), "地天泰");
            put(Arrays.asList(1, 8), "天地否");
            put(Arrays.asList(1, 3), "天火同人");
            put(Arrays.asList(3, 1), "火天大有");
            put(Arrays.asList(8, 7), "地山谦");
            put(Arrays.asList(4, 8), "雷地豫");
            put(Arrays.asList(2, 4), "泽雷随");
            put(Arrays.asList(7, 5), "山风蛊");
            put(Arrays.asList(8, 2), "地泽临");
            put(Arrays.asList(5, 8), "风地观");
            put(Arrays.asList(3, 4), "火雷噬嗑");
            put(Arrays.asList(7, 3), "山火贲");
            put(Arrays.asList(7, 8), "山地剥");
            put(Arrays.asList(8, 4), "地雷复");
            put(Arrays.asList(1, 4), "天雷无妄");
            put(Arrays.asList(7, 1), "山天大畜");
            put(Arrays.asList(7, 4), "山雷颐");
            put(Arrays.asList(2, 5), "泽风大过");
            put(Arrays.asList(6, 6), "坎为水");
            put(Arrays.asList(3, 3), "离为火");
            put(Arrays.asList(2, 7), "泽山咸");
            put(Arrays.asList(4, 5), "雷风恒");
            put(Arrays.asList(1, 7), "天山遁");
            put(Arrays.asList(4, 1), "雷天大壮");
            put(Arrays.asList(3, 8), "火地晋");
            put(Arrays.asList(8, 3), "地火明夷");
            put(Arrays.asList(5, 3), "风火家人");
            put(Arrays.asList(3, 2), "火泽睽");
            put(Arrays.asList(6, 7), "水山蹇");
            put(Arrays.asList(4, 6), "雷水解");
            put(Arrays.asList(7, 2), "山泽损");
            put(Arrays.asList(5, 4), "风雷益");
            put(Arrays.asList(2, 1), "泽天夬");
            put(Arrays.asList(1, 5), "天风姤");
            put(Arrays.asList(2, 8), "泽地萃");
            put(Arrays.asList(8, 5), "地风升");
            put(Arrays.asList(2, 6), "泽水困");
            put(Arrays.asList(6, 5), "水风井");
            put(Arrays.asList(2, 3), "泽火革");
            put(Arrays.asList(3, 5), "火风鼎");
            put(Arrays.asList(4, 4), "震为雷");
            put(Arrays.asList(7, 7), "艮为山");
            put(Arrays.asList(5, 7), "风山渐");
            put(Arrays.asList(4, 2), "雷泽归妹");
            put(Arrays.asList(4, 3), "雷火丰");
            put(Arrays.asList(3, 7), "火山旅");
            put(Arrays.asList(5, 5), "巽为风");
            put(Arrays.asList(2, 2), "兑为泽");
            put(Arrays.asList(5, 6), "风水涣");
            put(Arrays.asList(6, 2), "水泽节");
            put(Arrays.asList(5, 2), "风泽中孚");
            put(Arrays.asList(4, 7), "雷山小过");
            put(Arrays.asList(6, 3), "水火既济");
            put(Arrays.asList(3, 6), "火水未济");
        }

    };

    /**
     * 六十四卦包含的两个卦名（六十四卦卦名为键）
     */
    public static final Map<String, List<String>> LIU_SHI_SI_GUA_TWO_GUA_NAME = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        {
            put("乾为天", Arrays.asList("乾", "乾"));
            put("坤为地", Arrays.asList("坤", "坤"));
            put("水雷屯", Arrays.asList("坎", "震"));
            put("山水蒙", Arrays.asList("艮", "坎"));
            put("水天需", Arrays.asList("坎", "乾"));
            put("天水讼", Arrays.asList("乾", "坎"));
            put("地水师", Arrays.asList("坤", "坎"));
            put("水地比", Arrays.asList("坎", "坤"));
            put("风天小畜", Arrays.asList("巽", "乾"));
            put("天泽履", Arrays.asList("乾", "兑"));
            put("地天泰", Arrays.asList("坤", "乾"));
            put("天地否", Arrays.asList("乾", "坤"));
            put("天火同人", Arrays.asList("乾", "离"));
            put("火天大有", Arrays.asList("离", "乾"));
            put("地山谦", Arrays.asList("坤", "艮"));
            put("雷地豫", Arrays.asList("震", "坤"));
            put("泽雷随", Arrays.asList("兑", "震"));
            put("山风蛊", Arrays.asList("艮", "巽"));
            put("地泽临", Arrays.asList("坤", "兑"));
            put("风地观", Arrays.asList("巽", "坤"));
            put("火雷噬嗑", Arrays.asList("离", "震"));
            put("山火贲", Arrays.asList("艮", "离"));
            put("山地剥", Arrays.asList("艮", "坤"));
            put("地雷复", Arrays.asList("坤", "震"));
            put("天雷无妄", Arrays.asList("乾", "震"));
            put("山天大畜", Arrays.asList("艮", "乾"));
            put("山雷颐", Arrays.asList("艮", "震"));
            put("泽风大过", Arrays.asList("兑", "巽"));
            put("坎为水", Arrays.asList("坎", "坎"));
            put("离为火", Arrays.asList("离", "离"));
            put("泽山咸", Arrays.asList("兑", "艮"));
            put("雷风恒", Arrays.asList("震", "巽"));
            put("天山遁", Arrays.asList("乾", "艮"));
            put("雷天大壮", Arrays.asList("震", "乾"));
            put("火地晋", Arrays.asList("离", "坤"));
            put("地火明夷", Arrays.asList("坤", "离"));
            put("风火家人", Arrays.asList("巽", "离"));
            put("火泽睽", Arrays.asList("离", "兑"));
            put("水山蹇", Arrays.asList("坎", "艮"));
            put("雷水解", Arrays.asList("震", "坎"));
            put("山泽损", Arrays.asList("艮", "兑"));
            put("风雷益", Arrays.asList("巽", "震"));
            put("泽天夬", Arrays.asList("兑", "乾"));
            put("天风姤", Arrays.asList("乾", "巽"));
            put("泽地萃", Arrays.asList("兑", "坤"));
            put("地风升", Arrays.asList("坤", "巽"));
            put("泽水困", Arrays.asList("兑", "坎"));
            put("水风井", Arrays.asList("坎", "巽"));
            put("泽火革", Arrays.asList("兑", "离"));
            put("火风鼎", Arrays.asList("离", "巽"));
            put("震为雷", Arrays.asList("震", "震"));
            put("艮为山", Arrays.asList("艮", "艮"));
            put("风山渐", Arrays.asList("巽", "艮"));
            put("雷泽归妹", Arrays.asList("震", "兑"));
            put("雷火丰", Arrays.asList("震", "离"));
            put("火山旅", Arrays.asList("离", "艮"));
            put("巽为风", Arrays.asList("巽", "巽"));
            put("兑为泽", Arrays.asList("兑", "兑"));
            put("风水涣", Arrays.asList("巽", "坎"));
            put("水泽节", Arrays.asList("坎", "兑"));
            put("风泽中孚", Arrays.asList("巽", "兑"));
            put("雷山小过", Arrays.asList("震", "艮"));
            put("水火既济", Arrays.asList("坎", "离"));
            put("火水未济", Arrays.asList("离", "坎"));
        }
    };

    /**
     * 六十四卦卦辞（六十四卦卦名为键）
     */
    public static final Map<String, String> LIU_SHI_SI_GUA_GUA_CI = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("乾为天", "元，亨，利，贞。");
            put("坤为地", "元，亨，利牝马之贞。君子有攸往，先迷后得主利，西南得朋，东北丧朋。安贞吉。");
            put("水雷屯", "元亨，利贞。勿用有攸往，利建侯。");
            put("山水蒙", "亨。匪我求童蒙，童蒙求我。初筮告，再三渎，渎则不告。利贞。");
            put("水天需", "有孚，光亨贞吉。利涉大川。");
            put("天水讼", "有孚，窒惕。中吉，终凶。利见大人，不利涉大川。");
            put("地水师", "贞，丈人吉，无咎。");
            put("水地比", "吉。原筮，元永贞，无咎。不宁方来，后夫凶。");
            put("风天小畜", "亨。密云不雨，自我西郊。");
            put("天泽履", "虎尾，不咥人，亨。");
            put("地天泰", "小往大来，吉，亨。");
            put("天地否", "否之匪人，不利君子贞，大往小来。");
            put("天火同人", "同人于野，亨。利涉大川，利君子贞。");
            put("火天大有", "元亨。");
            put("地山谦", "亨，君子有终。");
            put("雷地豫", "利建侯，行师。");
            put("泽雷随", "元亨，利贞，无咎。");
            put("山风蛊", "元亨，利涉大川，先甲三日，后甲三日。");
            put("地泽临", "元亨，利贞。至于八月有凶。");
            put("风地观", "盥而不荐，有孚颙若。");
            put("火雷噬嗑", "亨，利用狱。");
            put("山火贲", "亨，小利有攸往。");
            put("山地剥", "不利有攸往。");
            put("地雷复", "亨，出入无疾，朋来，无吝。反复其道，七日来复，利有攸往。");
            put("天雷无妄", "元亨利贞。其匪正有眚，不利有攸往。");
            put("山天大畜", "利贞。不家食、吉。利涉大川。");
            put("山雷颐", "贞吉。观颐，自求口实。");
            put("泽风大过", "栋桡，利于攸往，亨。");
            put("坎为水", "习坎有孚维心，亨，行有尚。");
            put("离为火", "利贞，亨。畜牛牝，吉。");
            put("泽山咸", "亨，利贞。娶女，吉。");
            put("雷风恒", "亨，无吝，利贞，利有攸往。");
            put("天山遁", "亨，小利贞。");
            put("雷天大壮", "利贞。");
            put("火地晋", "康侯用锡马蕃蔗，昼日三接。");
            put("地火明夷", "利艰贞。");
            put("风火家人", "利女贞。");
            put("火泽睽", "小事吉。");
            put("水山蹇", "利西南，不利东北，利见大人，贞吉。");
            put("雷水解", "利西南，无所往，其来复，吉。有攸往，夙吉。");
            put("山泽损", "有孚，元吉，无咎，可贞，利有攸往。曷之用？二簋可用享。");
            put("风雷益", "利有攸往，利涉大川。");
            put("泽天夬", "杨于王庭，孚号有厉，告自邑，不利即戎，利有攸往。");
            put("天风姤", "女壮，勿用取女。");
            put("泽地萃", "亨。王假有庙。利见大人，亨，利贞，用大牲吉，利有攸往。");
            put("地风升", "元亨，用见大人，勿恤，南征吉。");
            put("泽水困", "亨，贞，大人吉，无咎。有言不信。");
            put("水风井", "改邑不改井。无丧无得，往来井井。汔至，亦未繘井，赢其瓶，凶。");
            put("泽火革", "巳日乃孚，元亨利贞，悔亡。");
            put("火风鼎", "元吉，亨。");
            put("震为雷", "亨，震来虩虩，笑言哑哑，震惊百里，不丧匕鬯。");
            put("艮为山", "艮其背，不获其身，行其庭，不见其人，无咎。");
            put("风山渐", "女归，吉，利贞。");
            put("雷泽归妹", "征凶，无攸利。");
            put("雷火丰", "亨，王假之，勿忧，宜日中。");
            put("火山旅", "小亨，旅，贞吉。");
            put("巽为风", "小亨，利有攸往，利见大人。");
            put("兑为泽", "亨，利贞。");
            put("风水涣", "亨。王假有庙，利涉大川，利贞。");
            put("水泽节", "亨，苦节不可贞。");
            put("风泽中孚", "豚鱼吉。利涉大川。利贞。");
            put("雷山小过", "亨，利贞，可小事，不可大事；飞鸟遗之音，不宜上，宜下，大吉。");
            put("水火既济", "亨小，利贞。初吉，终乱。");
            put("火水未济", "亨。小狐汔济，濡其尾，无攸利。");
        }
    };

    /**
     * 六十四卦的六爻爻象（六十四卦卦象为键）
     */
    public static final Map<String, List<String>> LIU_SHI_SI_GUA_LIU_YAO_AS = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /* 依次为：初爻、二爻、三爻、四爻、五爻、上爻 */ {
            put("䷀", Arrays.asList("—", "—", "—", "—", "—", "—"));
            put("䷁", Arrays.asList("--", "--", "--", "--", "--", "--"));
            put("䷂", Arrays.asList("—", "--", "--", "--", "—", "--"));
            put("䷃", Arrays.asList("--", "—", "--", "--", "--", "—"));
            put("䷄", Arrays.asList("—", "—", "—", "--", "—", "--"));
            put("䷅", Arrays.asList("--", "—", "--", "—", "—", "—"));
            put("䷆", Arrays.asList("--", "—", "--", "--", "--", "--"));
            put("䷇", Arrays.asList("--", "--", "--", "--", "—", "--"));
            put("䷈", Arrays.asList("—", "—", "—", "--", "—", "—"));
            put("䷉", Arrays.asList("—", "—", "--", "—", "—", "—"));
            put("䷊", Arrays.asList("—", "—", "—", "--", "--", "--"));
            put("䷋", Arrays.asList("--", "--", "--", "—", "—", "—"));
            put("䷌", Arrays.asList("—", "--", "—", "—", "—", "—"));
            put("䷍", Arrays.asList("—", "—", "—", "—", "--", "—"));
            put("䷎", Arrays.asList("--", "--", "—", "--", "--", "--"));
            put("䷏", Arrays.asList("--", "--", "--", "—", "--", "--"));
            put("䷐", Arrays.asList("—", "--", "--", "—", "—", "--"));
            put("䷑", Arrays.asList("--", "—", "—", "--", "--", "—"));
            put("䷒", Arrays.asList("—", "—", "--", "--", "--", "--"));
            put("䷓", Arrays.asList("--", "--", "--", "--", "—", "—"));
            put("䷔", Arrays.asList("—", "--", "--", "—", "--", "—"));
            put("䷕", Arrays.asList("—", "--", "—", "--", "--", "—"));
            put("䷖", Arrays.asList("--", "--", "--", "--", "--", "—"));
            put("䷗", Arrays.asList("—", "--", "--", "--", "--", "--"));
            put("䷘", Arrays.asList("—", "--", "--", "—", "—", "—"));
            put("䷙", Arrays.asList("—", "—", "—", "--", "--", "—"));
            put("䷚", Arrays.asList("—", "--", "--", "--", "--", "—"));
            put("䷛", Arrays.asList("--", "—", "—", "—", "—", "--"));
            put("䷜", Arrays.asList("--", "—", "--", "--", "—", "--"));
            put("䷝", Arrays.asList("—", "--", "—", "—", "--", "—"));
            put("䷞", Arrays.asList("--", "--", "—", "—", "—", "--"));
            put("䷟", Arrays.asList("--", "—", "—", "—", "--", "--"));
            put("䷠", Arrays.asList("--", "--", "—", "—", "—", "—"));
            put("䷡", Arrays.asList("—", "—", "—", "—", "--", "--"));
            put("䷢", Arrays.asList("--", "--", "--", "—", "--", "—"));
            put("䷣", Arrays.asList("—", "--", "—", "--", "--", "--"));
            put("䷤", Arrays.asList("—", "--", "—", "--", "—", "—"));
            put("䷥", Arrays.asList("—", "—", "--", "—", "--", "—"));
            put("䷦", Arrays.asList("--", "--", "—", "--", "—", "--"));
            put("䷧", Arrays.asList("--", "—", "--", "—", "--", "--"));
            put("䷨", Arrays.asList("—", "—", "--", "--", "--", "—"));
            put("䷩", Arrays.asList("—", "--", "--", "--", "—", "—"));
            put("䷪", Arrays.asList("—", "—", "—", "—", "—", "--"));
            put("䷫", Arrays.asList("--", "—", "—", "—", "—", "—"));
            put("䷬", Arrays.asList("--", "--", "--", "—", "—", "--"));
            put("䷭", Arrays.asList("--", "—", "—", "--", "--", "--"));
            put("䷮", Arrays.asList("--", "—", "--", "—", "—", "--"));
            put("䷯", Arrays.asList("--", "—", "—", "--", "—", "--"));
            put("䷰", Arrays.asList("—", "--", "—", "—", "—", "--"));
            put("䷱", Arrays.asList("--", "—", "—", "—", "--", "—"));
            put("䷲", Arrays.asList("—", "--", "--", "—", "--", "--"));
            put("䷳", Arrays.asList("--", "--", "—", "--", "--", "—"));
            put("䷴", Arrays.asList("--", "--", "—", "--", "—", "—"));
            put("䷵", Arrays.asList("—", "—", "--", "—", "--", "--"));
            put("䷶", Arrays.asList("—", "--", "—", "—", "--", "--"));
            put("䷷", Arrays.asList("--", "--", "—", "—", "--", "—"));
            put("䷸", Arrays.asList("--", "—", "—", "--", "—", "—"));
            put("䷹", Arrays.asList("—", "—", "--", "—", "—", "--"));
            put("䷺", Arrays.asList("--", "—", "--", "--", "—", "—"));
            put("䷻", Arrays.asList("—", "—", "--", "--", "—", "--"));
            put("䷼", Arrays.asList("—", "—", "--", "--", "—", "—"));
            put("䷽", Arrays.asList("--", "--", "—", "—", "--", "--"));
            put("䷾", Arrays.asList("—", "--", "—", "--", "—", "--"));
            put("䷿", Arrays.asList("--", "—", "--", "—", "--", "—"));
        }

    };

    /**
     * 六十四卦的六爻爻名（六十四卦卦象为键）
     */
    public static final Map<String, List<String>> LIU_SHI_SI_GUA_LIU_YAO_YAO_MING = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /* 依次为：初爻、二爻、三爻、四爻、五爻、上爻 */ {
            put("䷀", Arrays.asList("初九", "九二", "九三", "九四", "九五", "上九"));
            put("䷁", Arrays.asList("初六", "六二", "六三", "六四", "六五", "上六"));
            put("䷂", Arrays.asList("初九", "六二", "六三", "六四", "九五", "上六"));
            put("䷃", Arrays.asList("初六", "九二", "六三", "六四", "六五", "上九"));
            put("䷄", Arrays.asList("初九", "九二", "九三", "六四", "九五", "上六"));
            put("䷅", Arrays.asList("初六", "九二", "六三", "九四", "九五", "上九"));
            put("䷆", Arrays.asList("初六", "九二", "六三", "六四", "六五", "上六"));
            put("䷇", Arrays.asList("初六", "六二", "六三", "六四", "九五", "上六"));
            put("䷈", Arrays.asList("初九", "九二", "九三", "六四", "九五", "上九"));
            put("䷉", Arrays.asList("初九", "九二", "六三", "九四", "九五", "上九"));
            put("䷊", Arrays.asList("初九", "九二", "九三", "六四", "六五", "上六"));
            put("䷋", Arrays.asList("初六", "六二", "六三", "九四", "九五", "上九"));
            put("䷌", Arrays.asList("初九", "六二", "九三", "九四", "九五", "上九"));
            put("䷍", Arrays.asList("初九", "九二", "九三", "九四", "六五", "上九"));
            put("䷎", Arrays.asList("初六", "六二", "九三", "六四", "六五", "上六"));
            put("䷏", Arrays.asList("初六", "六二", "六三", "九四", "六五", "上六"));
            put("䷐", Arrays.asList("初九", "六二", "六三", "九四", "九五", "上六"));
            put("䷑", Arrays.asList("初六", "九二", "九三", "六四", "六五", "上九"));
            put("䷒", Arrays.asList("初九", "九二", "六三", "六四", "六五", "上六"));
            put("䷓", Arrays.asList("初六", "六二", "六三", "六四", "九五", "上九"));
            put("䷔", Arrays.asList("初九", "六二", "六三", "九四", "六五", "上九"));
            put("䷕", Arrays.asList("初九", "六二", "九三", "六四", "六五", "上九"));
            put("䷖", Arrays.asList("初六", "六二", "六三", "六四", "六五", "上九"));
            put("䷗", Arrays.asList("初九", "六二", "六三", "六四", "六五", "上六"));
            put("䷘", Arrays.asList("初九", "六二", "六三", "九四", "九五", "上九"));
            put("䷙", Arrays.asList("初九", "九二", "九三", "六四", "六五", "上九"));
            put("䷚", Arrays.asList("初九", "六二", "六三", "六四", "六五", "上九"));
            put("䷛", Arrays.asList("初六", "九二", "九三", "九四", "九五", "上六"));
            put("䷜", Arrays.asList("初六", "九二", "六三", "六四", "九五", "上六"));
            put("䷝", Arrays.asList("初九", "六二", "九三", "九四", "六五", "上九"));
            put("䷞", Arrays.asList("初六", "六二", "九三", "九四", "九五", "上六"));
            put("䷟", Arrays.asList("初六", "九二", "九三", "九四", "六五", "上六"));
            put("䷠", Arrays.asList("初六", "六二", "九三", "九四", "九五", "上九"));
            put("䷡", Arrays.asList("初九", "九二", "九三", "九四", "六五", "上六"));
            put("䷢", Arrays.asList("初六", "六二", "六三", "九四", "六五", "上九"));
            put("䷣", Arrays.asList("初九", "六二", "九三", "六四", "六五", "上六"));
            put("䷤", Arrays.asList("初九", "六二", "九三", "六四", "九五", "上九"));
            put("䷥", Arrays.asList("初九", "九二", "六三", "九四", "六五", "上九"));
            put("䷦", Arrays.asList("初六", "六二", "九三", "六四", "九五", "上六"));
            put("䷧", Arrays.asList("初六", "九二", "六三", "九四", "六五", "上六"));
            put("䷨", Arrays.asList("初九", "九二", "六三", "六四", "六五", "上九"));
            put("䷩", Arrays.asList("初九", "六二", "六三", "六四", "九五", "上九"));
            put("䷪", Arrays.asList("初九", "九二", "九三", "九四", "九五", "上六"));
            put("䷫", Arrays.asList("初六", "九二", "九三", "九四", "九五", "上九"));
            put("䷬", Arrays.asList("初六", "六二", "六三", "九四", "九五", "上六"));
            put("䷭", Arrays.asList("初六", "九二", "九三", "六四", "六五", "上六"));
            put("䷮", Arrays.asList("初六", "九二", "六三", "九四", "九五", "上六"));
            put("䷯", Arrays.asList("初六", "九二", "九三", "六四", "九五", "上六"));
            put("䷰", Arrays.asList("初九", "六二", "九三", "九四", "九五", "上六"));
            put("䷱", Arrays.asList("初六", "九二", "九三", "九四", "六五", "上九"));
            put("䷲", Arrays.asList("初九", "六二", "六三", "九四", "六五", "上六"));
            put("䷳", Arrays.asList("初六", "六二", "九三", "六四", "六五", "上九"));
            put("䷴", Arrays.asList("初六", "六二", "九三", "六四", "九五", "上九"));
            put("䷵", Arrays.asList("初九", "九二", "六三", "九四", "六五", "上六"));
            put("䷶", Arrays.asList("初九", "六二", "九三", "九四", "六五", "上六"));
            put("䷷", Arrays.asList("初六", "六二", "九三", "九四", "六五", "上九"));
            put("䷸", Arrays.asList("初六", "九二", "九三", "六四", "九五", "上九"));
            put("䷹", Arrays.asList("初九", "九二", "六三", "九四", "九五", "上六"));
            put("䷺", Arrays.asList("初六", "九二", "六三", "六四", "九五", "上九"));
            put("䷻", Arrays.asList("初九", "九二", "六三", "六四", "九五", "上六"));
            put("䷼", Arrays.asList("初九", "九二", "六三", "六四", "九五", "上九"));
            put("䷽", Arrays.asList("初六", "六二", "九三", "九四", "六五", "上六"));
            put("䷾", Arrays.asList("初九", "六二", "九三", "六四", "九五", "上六"));
            put("䷿", Arrays.asList("初六", "九二", "六三", "九四", "六五", "上九"));
        }
    };

    /**
     * 六十四卦的六爻爻辞（六十四卦卦象为键）
     */
    public static final Map<String, List<String>> LIU_SHI_SI_GUA_LIU_YAO_YAO_CI = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        /* 依次为：初爻、二爻、三爻、四爻、五爻、上爻 */ {
            put("䷀", Arrays.asList("潜龙勿用。", "见龙在田，利见大人。", "君子终日乾乾，夕惕若厉，无咎。", "或跃在渊，无咎。", "飞龙在天，利见大人。", "亢龙有悔。"));
            put("䷁", Arrays.asList("履霜坚冰至。", "直方大，不习，无不利。", "含章可贞，或从王事，无成有终。", "括囊，无咎无誉。", "黄裳元吉。", "龙战于野，其血玄黄。"));
            put("䷂", Arrays.asList("磐桓，利居贞，利建侯。", "屯如邅如，乘马班如。匪寇，婚媾。女子贞不字，十年乃字。", "即鹿无虞，惟入于林中。君子几，不如舍，往吝。", "乘马班如，求婚媾，往吉，无不利。", "屯其膏，小贞吉，大贞凶。", "乘马班如，泣血涟如。"));
            put("䷃", Arrays.asList("发蒙，利用刑人，用说桎梏。以往吝。", "包蒙，吉。纳妇，吉。子克家。", "勿用取女，见金夫，不有躬，无攸利。", "困蒙，吝。", "童蒙，吉。", "击蒙，不利为寇，利御寇。"));
            put("䷄", Arrays.asList("需于郊，利用恒，无咎。", "需于沙，小有言，终吉。", "需于泥，致寇至。", "需于血，出自穴。", "需于酒食，贞吉。", "入于穴，有不速之客三人来，敬之，终吉。"));
            put("䷅", Arrays.asList("不永所事，小有言，终吉。", "不克讼，归而逋。其邑人三百户，无眚。", "食旧德，贞厉，终吉。或从王事，无成。", "不克讼，复既命，渝，安贞吉。", "讼元，吉。", "或锡之鞶带，终朝三褫之。"));
            put("䷆", Arrays.asList("师出以律，否臧凶。", "在师，中吉，无咎，王三锡命。", "师或舆尸，凶。", "师左次，无咎。", "田有禽，利执；言，无咎。长子帅师；弟子舆尸。贞凶。", "大君有命，开国承家，小人勿用。"));
            put("䷇", Arrays.asList("有孚比之，无咎。有孚盈缶，终来有它吉。", "比之自内，贞吉。", "比之匪人。", "外比之，贞吉。", "显比，王用三驱，失前禽，邑人不诫，吉。", "比之无首，凶。"));
            put("䷈", Arrays.asList("复自道，何其咎？吉。", "牵复，吉。", "舆说辐，夫妻反目。", "有孚，血去惕出，无咎。", "有孚挛如，富以其邻。", "既雨既处，尚德载。妇贞厉，月几望。君子征凶。"));
            put("䷉", Arrays.asList("素履，往无咎。", "履道坦坦，幽人贞吉。", "眇能视，跛能履，履虎尾，咥人，凶。武人为于大君。", "履虎尾，愬愬，终吉。", "夬履，贞厉。", "视履考祥，其旋元吉。"));
            put("䷊", Arrays.asList("拔茅茹，以其汇，征吉。", "包荒，用冯河，不遐遗。朋亡，得尚于中行。", "无平不陂，无往不复，艰贞无咎。勿恤其孚，于食有福。", "翩翩，不富以其邻，不戒以孚。", "帝乙归妹，以祉元吉。", "城复于隍，勿用师。自邑告命，贞吝。"));
            put("䷋", Arrays.asList("拔茅茹，以其汇，贞吉，亨。", "包承，小人吉，大人否，亨。", "包羞。", "有命无咎，畴离祉。", "休否，大人吉，其亡其亡，系于苞桑。", "倾否，先否后喜。"));
            put("䷌", Arrays.asList("同人于门，无咎。", "同人于宗，吝。", "伏戎于莽，升其高陵，三岁不兴。", "乘其墉。弗克攻。吉。", "同人，先号啕而后笑，大师克，相遇。", "同人于郊，无悔。"));
            put("䷍", Arrays.asList("无交害，匪咎，艰者无咎。", "大车以载，有攸往，无咎。", "公用亨于天子，小人弗克。", "匪其彭，无咎。", "厥孚交加，威加，吉。", "自天佑之，吉无不利。"));
            put("䷎", Arrays.asList("谦谦君子，用涉大川，吉。", "鸣谦，贞吉。", "劳谦，君子有终，吉。", "无不利，㧑谦。", "不富以其邻，利用侵伐，无不利。", "鸣谦，利用行师，征邑国。"));
            put("䷏", Arrays.asList("鸣豫，凶。", "介于石，不终日，贞吉。", "盱豫，悔。 迟，有悔。", "由豫，大有得。勿疑， 朋盍簪。", "贞疾，恒不死。", "冥豫，成有渝，无咎。"));
            put("䷐", Arrays.asList("官有渝，贞吉。出门交有功。", "系小子，失丈夫。", "系丈夫，失小子，随有求，得，利居贞。", "随有获，贞凶。有孚在道以明，何咎？", "孚于嘉，吉。", "拘系之，乃从维之，王用亨于西山。"));
            put("䷑", Arrays.asList("干父之蛊，有子，考无咎，厉终吉。", "干母之蛊，不可贞。", "干父之蛊，小有晦，无大咎。", "裕父之蛊，往见吝。", "干父之蛊，用誉。", "不事王侯，高尚其事。"));
            put("䷒", Arrays.asList("咸临，贞吉。", "咸临，吉无不利。", "甘临，无攸利。既忧之，无咎。", "至临，无咎。", "知临，大君之宜，吉。", "敦临，吉，无咎。"));
            put("䷓", Arrays.asList("童观，小人无咎，君子吝。", "窥观，利女贞。", "观我生，进退。", "观国之光，利用宾于王。", "观我生，君子无咎。", "观其生，君子无咎。"));
            put("䷔", Arrays.asList("屦校灭趾，无咎。", "噬肤，灭鼻，无咎。", "噬腊肉，遇毒；小吝，无咎。", "噬干胏，得金矢，利艰贞，吉。", "噬干肉，得黄金，贞厉，无咎。", "何校灭耳，凶。"));
            put("䷕", Arrays.asList("贲其趾，舍车而徒。", "贲其须。", "贲如濡如，永贞吉。", "贲如皤如，白马翰如。匪寇，婚媾。", "贲于丘园，束帛戋戋。吝，终吉。", "白贲，无咎。"));
            put("䷖", Arrays.asList("剥床以足，蔑贞凶。", "剥床以辨，蔑贞凶。", "剥之，无咎。", "剥床以肤，凶。", "贯鱼，以宫人宠，无不利。", "硕果不食，君子得舆，小人剥庐。"));
            put("䷗", Arrays.asList("不远复，无祗悔，元吉。", "休复，吉。", "频复，厉，无咎。", "中行独复。", "敦复，无悔。", "迷复，凶，有灾眚。用行师，终以大败。以其国君凶，至于十年不克征。"));
            put("䷘", Arrays.asList("无妄，往吉。", "不耕获，不菑畲，则利有攸往。", "无妄之灾，或系之牛，行人之得，邑人之灾。", "可贞，无咎。", "无妄之疾，勿药有喜。", "无妄，行有眚，无攸利。"));
            put("䷙", Arrays.asList("有厉，利已。", "舆说輹。", "良马逐，利艰贞。日闲舆卫，利有攸往。", "童牛之牿，元吉。", "豶豕之牙，吉。", "何天之衢，亨。"));
            put("䷚", Arrays.asList("舍尔灵龟，观我朵颐，凶。", "颠颐，拂经于丘颐，征凶。", "拂颐，贞凶。十年勿用，无攸利。", "颠颐，吉。虎视眈眈，其欲逐逐，无咎。", "拂经，居贞吉，不可涉大川。", "由颐，厉吉。利涉大川。"));
            put("䷛", Arrays.asList("藉用白茅，无咎。", "枯杨生稊，老夫得其女妻，无不利。", "栋桡，凶。", "栋隆，吉，有它吝。", "枯杨生华，老妇得其士夫，无咎无誉。", "过涉灭顶，凶，无咎。"));
            put("䷜", Arrays.asList("习坎，入于坎窞，凶。", "坎有险，求小得。", "来之坎坎，险且枕。入于坎窞，勿用。", "樽酒，簋贰，用缶，纳约自牖，终无咎。", "坎不盈，祗既平，无咎。", "系用徽纆，置于丛棘，三岁不得，凶。"));
            put("䷝", Arrays.asList("履错，然，敬之，无咎。", "黄离，元吉。", "日昃之离，不鼓缶而歌，则大耋之嗟，凶。", "突如其来如，焚如，死如，弃如。", "出涕沱若，戚嗟若，吉。", "王用出征，用嘉折首，获匪其丑，无咎。"));
            put("䷞", Arrays.asList("咸其拇。", "咸其腓，凶。居吉。", "咸其股，执其随，往吝。", "贞吉。悔亡。憧憧往来，朋从尔思。", "咸其脢，无悔。", "咸其辅颊舌。"));
            put("䷟", Arrays.asList("浚恒，贞凶，无攸利。", "悔亡。", "不恒其德，或承之羞；贞吝。", "田无禽。", "恒其德，贞。妇人吉，夫子凶。", "振恒，凶。"));
            put("䷠", Arrays.asList("遁尾，厉，勿用有攸往。", "执之，用黄牛之革，莫之胜说。", "系遁，有疾厉。畜臣妾，吉。", "好遁，君子吉，小人否。", "嘉遁，贞吉。", "肥遁，无不利。"));
            put("䷡", Arrays.asList("壮于趾，征凶，有孚。", "贞吉。", "小人用壮，君子用罔，贞厉。羝羊触藩，羸其角。", "贞吉，悔亡。藩决不羸，壮于大舆之輹。", "丧羊于易，无悔。", "羝羊触藩，不能退，不能遂，无攸利。艰则吉。"));
            put("䷢", Arrays.asList("晋如，摧如，贞吉。罔孚，裕无咎。", "晋如愁如，贞吉。受兹介福，于其王母。", "众允，悔亡。", "晋如鼫鼠，贞厉。", "悔亡，失得勿恤。往吉，无不利。", "晋其角，维用伐邑，厉吉，无咎。贞吝。"));
            put("䷣", Arrays.asList("明夷于飞，垂其翼。 君子于行，三日不食， 有攸往，主人有言。", "明夷，夷于左股，用拯马壮，吉。", "明夷于南狩，得其大首。不可疾贞。", "入于左腹，获明夷之心，出于门庭。", "箕子之明夷，利贞。", "不明晦，初登于天，后入于地。"));
            put("䷤", Arrays.asList("闲有家，悔亡。", "无攸遂，在中馈，贞吉。", "家人嗃嗃，悔厉，吉。妇子嘻嘻，终吝。", "富家，大吉。", "王假有家，勿恤，吉。", "有孚，威如，终吉。"));
            put("䷥", Arrays.asList("悔亡；丧马勿逐，自复。见恶人，无咎。", "遇主于巷，无咎。", "见舆曳，其牛掣，其人天且劓。无初有终。", "睽孤，遇元夫，交孚，厉无咎。", "悔亡，厥宗噬肤，往何咎？", "睽孤，见豕负涂，载鬼一车。先张之弧，后说之弧。匪寇，婚媾，往遇雨则吉。"));
            put("䷦", Arrays.asList("往蹇，来誉。", "王臣蹇蹇，匪躬之故。", "往蹇，来反。", "往蹇，来连。", "大蹇，朋来。", "往蹇，来硕。吉，利见大人。"));
            put("䷧", Arrays.asList("无咎。", "田获三狐，得黄矢，贞吉。", "负且乘，致寇至，贞吝。", "解而拇，朋至斯孚。", "君子维有解，吉。有孚于小人。", "公用射隼于高墉之上，获之，无不利。"));
            put("䷨", Arrays.asList("已事遄往，无咎，酌损之。", "利贞，征凶。弗损，益之。", "三人行，则损一人。一人行，则得其友。", "损其疾，使遄有喜，无咎。", "或益之十朋之龟，弗克违，元吉。", "弗损益之，无咎，贞吉，利有攸往。得臣无家。"));
            put("䷩", Arrays.asList("利用为大作，元吉，无咎。", "或益之十朋之龟，弗克违，永贞吉。王用享于帝，吉。", "益之用凶事，无咎。有孚中行，告公用圭。", "中行，告公从，利用为依迁国。", "有孚惠心，勿问，元吉。有孚惠我德。", "莫益之，或击之，立心勿恒，凶。"));
            put("䷪", Arrays.asList("壮于前趾，往不胜，为咎。", "惕号，莫夜有戎，勿恤。", "壮于頄，有凶。君子夬夬，独行遇雨，若濡有愠，无咎。", "臀无肤，其行次且。牵羊悔亡，闻言不信。", "苋陆夬夬，中行无咎。", "无号，终有凶。"));
            put("䷫", Arrays.asList("系于金柅，贞吉。有攸往，见凶，羸豕孚蹢躅。", "包有鱼，无咎，不利宾。", "臀无肤，其行次且，厉，无大咎。", "包无鱼，起凶。", "以杞包瓜，含章，有陨自天。", "姤其角，吝，无咎。"));
            put("䷬", Arrays.asList("有孚不终，乃乱乃萃。若号，一握为笑，勿恤，往无咎。", "引吉，无咎。孚乃利用禴。", "萃如嗟如，无攸利。往无咎，小吝。", "大吉，无咎。", "萃有位，无咎。匪孚，元永贞，悔亡。", "赍咨涕洟，无咎。"));
            put("䷭", Arrays.asList("允升，大吉。", "孚乃利用禴，无咎。", "升虚邑。", "王用亨于岐山，吉，无咎。", "贞吉，升阶。", "冥升，利于不息之贞。"));
            put("䷮", Arrays.asList("臀困于株木，入于幽谷，三岁不觌。", "困于酒食，朱绂方来，利用享祀。征凶，无咎。", "困于石，据于蒺藜，入于其宫，不见其妻，凶", "来徐徐，困于金车，吝，有终。", "劓刖，困于赤绂；乃徐有说，利用祭祀。", "困于葛藟，于臲兀危，曰动悔。有悔，征吉。"));
            put("䷯", Arrays.asList("井泥不食，旧井无禽。", "井谷射鲋，瓮敝漏。", "井渫不食，为我心恻。可用汲，王明并受其福。", "井甃，无咎。", "井冽，寒泉食。", "井收，勿幕。有孚，元吉。"));
            put("䷰", Arrays.asList("巩用黄牛之革。", "巳日乃革之，征吉，无咎。", "征凶，贞厉。革言三就，有孚。", "悔亡，有孚改命，吉。", "大人虎变，未占有孚。", "君子豹变，小人革面，征凶，居贞吉。"));
            put("䷱", Arrays.asList("鼎颠趾，利出否。得妾以其子，无咎。", "鼎有实，我仇有疾，不我能即，吉。", "鼎耳革，其行塞，雉膏不食。方雨亏悔，终吉。", "鼎折足，覆公餗，其形渥，凶。", "鼎黄耳金铉，利贞。", "鼎玉铉，大吉，无不利。"));
            put("䷲", Arrays.asList("震来虩虩，后笑言哑哑，吉。", "震来厉，亿丧贝，跻于九陵，勿逐，七日得。", "震苏苏，震行无眚。", "震遂泥。", "震往来厉，意无丧有事。", "震索索，视矍矍，征凶。震不于其躬，于其邻，无咎。婚媾有言。"));
            put("䷳", Arrays.asList("艮其趾，无咎，利永贞。", "艮其腓，不拯其随，其心不快。", "艮其限，列其夤，厉熏心。", "艮其身，无咎。", "艮其辅，言有序，悔亡。", "敦艮，吉。"));
            put("䷴", Arrays.asList("鸿渐于干，小子厉，有言，无咎。", "鸿渐于磐，饮食衎衎，吉。", "鸿渐于陆，夫征不复，妇孕不育，凶。利御寇。", "鸿渐于木，或得其桷，无咎。", "鸿渐于陵，妇三岁不孕，终莫之胜，吉。", "鸿渐于陆，其羽可用为仪，吉。"));
            put("䷵", Arrays.asList("归妹以娣，跛能履，征吉。", "眇能视，利幽人之贞。", "归妹以须，反归以娣。", "归妹愆期，迟归有时。", "帝乙归妹，其君之袂，不如其娣之袂良。月几望，吉。", "女承筐无实，士刲羊无血，无攸利。"));
            put("䷶", Arrays.asList("遇其配主，虽旬无咎，往有尚。", "丰其蔀，日中见斗，往得疑疾。有孚发若，吉。", "丰其沛，日中见沬，折其右肱，无咎。", "丰其蔀，日中见斗，遇其夷主，吉。", "来章，有庆誉，吉。", "丰其屋，蔀其家，闚其户，阒其无人，三岁不觌，凶。"));
            put("䷷", Arrays.asList("旅琐琐，斯其所取灾。", "旅即次，怀其资，得童仆贞。", "旅焚其次，丧其童仆，贞厉。", "旅于处，得其资斧，我心不快。", "射雉，一矢亡，终以誉命。", "鸟焚其巢，旅人先笑后号啕。丧牛于易，凶。"));
            put("䷸", Arrays.asList("进退，利武人之贞。", "巽在床下，用史巫纷若，吉，无咎。", "频巽，吝。", "悔亡，田获三品。", "贞吉，悔亡，无不利。无初有终。先庚三日，后庚三日，吉。", "巽在床下，丧其资斧，贞凶。"));
            put("䷹", Arrays.asList("和兑，吉。", "孚兑，吉，悔亡。", "和兑，吉。", "商兑未宁，介疾有喜。", "孚于剥，有厉。", "引兑。"));
            put("䷺", Arrays.asList("用拯马壮，吉。", "涣奔其机，悔亡。", "涣其躬，无悔。", "涣其群，元吉。涣有丘，匪夷所思。", "涣汗其大号，涣王居，无咎。", "涣其血，去逖出，无咎。"));
            put("䷻", Arrays.asList("不出户庭，无咎。", "不出门庭，凶。", "不节若，则嗟若，无咎。", "安节，亨。", "甘节，吉。往有尚。", "苦节，贞凶，悔亡。"));
            put("䷼", Arrays.asList("虞吉，有它不燕。", "鹤鸣在阴，其子和之。我有好爵，吾与尔靡之。", "得敌，或鼓或罢，或泣或歌。", "月几望，马匹亡，无咎。", "有孚挛如，无咎。", "翰音登于天，贞凶。"));
            put("䷽", Arrays.asList("飞鸟以凶。", "过其祖，遇其妣，不及其君，遇其臣，无咎。", "弗过防之，从或戕之，凶。", "无咎，弗过遇之，往厉必戒，勿用永贞。", "密云不雨，自我西郊，公弋取彼在穴。", "弗遇过之，飞鸟离之，凶，是谓灾眚。"));
            put("䷾", Arrays.asList("曳其轮，濡其尾，无咎。", "妇丧其茀，勿逐，七日得。", "高宗伐鬼方，三年克之，小人勿用。", "繻有衣袽，终日戒。", "东邻杀牛，不如西郊之禴祭，实受其福。", "濡其首，厉。"));
            put("䷿", Arrays.asList("濡其尾，吝。", "曳其轮，贞吉。", "未济，征凶，利涉大川。", "吉，悔亡，震用伐鬼方，三年有赏于大国。", "贞吉，无悔，君子之光，有孚吉。", "有孚于饮酒，无咎。濡其首，有孚失是。"));
        }
    };

//----------------------------------------------------------------------------------------------------------------------------------------------------

    /**
     * 本卦 变 变卦
     */
    public static final Map<Integer, List<Integer>> BIAN_GUA = new HashMap<Integer, List<Integer>>() {
        private static final long serialVersionUID = -1;

        {
            put(1, Arrays.asList(5, 3, 2)); // 乾卦（☰），动爻数为：1或4时变为巽[☴]，2或5时变为离[☲]，3或6时变为兑[☱]
            put(2, Arrays.asList(6, 4, 1)); // 兑卦（☱），动爻数为：1或4时变为坎[☵]，2或5时变为震[☳]，3或6时变为乾[☰]
            put(3, Arrays.asList(7, 1, 4)); // 离卦（☲），动爻数为：1或4时变为艮[☶]，2或5时变为乾[☰]，3或6时变为震[☳]
            put(4, Arrays.asList(8, 2, 3)); // 震卦（☳），动爻数为：1或4时变为坤[☷]，2或5时变为兑[☱]，3或6时变为离[☲]
            put(5, Arrays.asList(1, 7, 6)); // 巽卦（☴），动爻数为：1或4时变为乾[☰]，2或5时变为艮[☶]，3或6时变为坎[☵]
            put(6, Arrays.asList(2, 8, 5)); // 坎卦（☵），动爻数为：1或4时变为兑[☱]，2或5时变为坤[☷]，3或6时变为巽[☴]
            put(7, Arrays.asList(3, 5, 8)); // 艮卦（☶），动爻数为：1或4时变为离[☲]，2或5时变为巽[☴]，3或6时变为坤[☷]
            put(8, Arrays.asList(4, 6, 7)); // 坤卦（☷），动爻数为：1或4时变为震[☳]，2或5时变为坎[☵]，3或6时变为艮[☶]

        }
    };

    /**
     * 本卦 变 互卦、错卦、综卦（本卦卦名为键）
     *
     * <p> 互卦：本卦的二三四爻做互卦的下卦、本卦的三四五卦做互卦的上卦 </p>
     * <p> 错卦：本卦阳爻变阴爻，阴爻变阳爻</p>
     * <p> 综卦：本卦上下180°旋转</p>
     */
    public static final Map<String, List<String>> HU_CUO_ZONG = new HashMap<String, List<String>>() {
        private static final long serialVersionUID = -1;

        {
            // 例：本卦为乾为天（䷀）。变化后→ 互卦为乾为天（䷀），错卦为坤为地（䷁），综卦为乾为天（䷀）
            put("乾为天", Arrays.asList("乾为天", "坤为地", "乾为天"));
            put("坤为地", Arrays.asList("坤为地", "乾为天", "坤为地"));
            put("水雷屯", Arrays.asList("山地剥", "火风鼎", "山水蒙"));
            put("山水蒙", Arrays.asList("地雷复", "泽火革", "水雷屯"));
            put("水天需", Arrays.asList("火泽睽", "火地晋", "天水讼"));
            put("天水讼", Arrays.asList("风火家人", "地火明夷", "水天需"));
            put("地水师", Arrays.asList("地雷复", "天火同人", "水地比"));
            put("水地比", Arrays.asList("山地剥", "火天大有", "地水师"));
            put("风天小畜", Arrays.asList("火泽睽", "雷地豫", "天泽履"));
            put("天泽履", Arrays.asList("风火家人", "地山谦", "风天小畜"));
            put("地天泰", Arrays.asList("雷泽归妹", "天地否", "天地否"));
            put("天地否", Arrays.asList("风山渐", "地天泰", "地天泰"));
            put("天火同人", Arrays.asList("天风姤", "地水师", "火天大有"));
            put("火天大有", Arrays.asList("泽天夬", "水地比", "天火同人"));
            put("地山谦", Arrays.asList("雷水解", "天泽履", "雷地豫"));
            put("雷地豫", Arrays.asList("水山蹇", "风天小畜", "地山谦"));
            put("泽雷随", Arrays.asList("风山渐", "山风蛊", "山风蛊"));
            put("山风蛊", Arrays.asList("雷泽归妹", "泽雷随", "泽雷随"));
            put("地泽临", Arrays.asList("地雷复", "天山遁", "风地观"));
            put("风地观", Arrays.asList("山地剥", "雷天大壮", "地泽临"));
            put("火雷噬嗑", Arrays.asList("水山蹇", "水风井", "山火贲"));
            put("山火贲", Arrays.asList("雷水解", "泽水困", "火雷噬嗑"));
            put("山地剥", Arrays.asList("坤为地", "泽天夬", "地雷复"));
            put("地雷复", Arrays.asList("坤为地", "天风姤", "山地剥"));
            put("天雷无妄", Arrays.asList("风山渐", "地风升", "山天大畜"));
            put("山天大畜", Arrays.asList("雷泽归妹", "泽地萃", "天雷无妄"));
            put("山雷颐", Arrays.asList("坤为地", "泽风大过", "山雷颐"));
            put("泽风大过", Arrays.asList("乾为天", "山雷颐", "泽风大过"));
            put("坎为水", Arrays.asList("山雷颐", "离为火", "坎为水"));
            put("离为火", Arrays.asList("泽风大过", "坎为水", "离为火"));
            put("泽山咸", Arrays.asList("天风姤", "山泽损", "雷风恒"));
            put("雷风恒", Arrays.asList("泽天夬", "风雷益", "泽山咸"));
            put("天山遁", Arrays.asList("天风姤", "地泽临", "雷天大壮"));
            put("雷天大壮", Arrays.asList("泽天夬", "风地观", "天山遁"));
            put("火地晋", Arrays.asList("水山蹇", "水天需", "地火明夷"));
            put("地火明夷", Arrays.asList("雷水解", "天水讼", "火地晋"));
            put("风火家人", Arrays.asList("火水未济", "雷水解", "火泽睽"));
            put("火泽睽", Arrays.asList("水火既济", "水山蹇", "风火家人"));
            put("水山蹇", Arrays.asList("火水未济", "火泽睽", "雷水解"));
            put("雷水解", Arrays.asList("水火既济", "风火家人", "水山蹇"));
            put("山泽损", Arrays.asList("地雷复", "泽山咸", "风雷益"));
            put("风雷益", Arrays.asList("山地剥", "雷风恒", "山泽损"));
            put("泽天夬", Arrays.asList("乾为天", "山地剥", "天风姤"));
            put("天风姤", Arrays.asList("乾为天", "地雷复", "泽天夬"));
            put("泽地萃", Arrays.asList("风山渐", "山天大畜", "地风升"));
            put("地风升", Arrays.asList("雷泽归妹", "天雷无妄", "泽地萃"));
            put("泽水困", Arrays.asList("风火家人", "山火贲", "水风井"));
            put("水风井", Arrays.asList("火泽睽", "火雷噬嗑", "泽水困"));
            put("泽火革", Arrays.asList("天风姤", "山水蒙", "火风鼎"));
            put("火风鼎", Arrays.asList("泽天夬", "水雷屯", "泽火革"));
            put("震为雷", Arrays.asList("水山蹇", "巽为风", "艮为山"));
            put("艮为山", Arrays.asList("雷水解", "兑为泽", "震为雷"));
            put("风山渐", Arrays.asList("火水未济", "雷泽归妹", "雷泽归妹"));
            put("雷泽归妹", Arrays.asList("水火既济", "风山渐", "风山渐"));
            put("雷火丰", Arrays.asList("泽风大过", "风水涣", "火山旅"));
            put("火山旅", Arrays.asList("泽风大过", "水泽节", "雷火丰"));
            put("巽为风", Arrays.asList("火泽睽", "震为雷", "兑为泽"));
            put("兑为泽", Arrays.asList("风火家人", "艮为山", "巽为风"));
            put("风水涣", Arrays.asList("山雷颐", "雷火丰", "水泽节"));
            put("水泽节", Arrays.asList("山雷颐", "火山旅", "风水涣"));
            put("风泽中孚", Arrays.asList("山雷颐", "雷山小过", "雷山小过"));
            put("雷山小过", Arrays.asList("泽风大过", "风泽中孚", "风泽中孚"));
            put("水火既济", Arrays.asList("火水未济", "火水未济", "火水未济"));
            put("火水未济", Arrays.asList("水火既济", "水火既济", "水火既济"));
        }

    };

//----------------------------------------------------------------------------------------------------------------------------------------------------

    /**
     * 用卦与体卦的关系（用卦+体卦为键）
     */
    public static final Map<String, String> YONG_TI_GUAN_XI = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        {
            put("乾乾", YONG_TI_BI_HE); // 用卦为乾、体卦为乾
            put("乾坤", TI_SHENG_YONG); // 用卦为乾、体卦为坤
            put("乾震", YONG_KE_TI);    // 用卦为乾、体卦为震
            put("乾巽", YONG_KE_TI);    // 用卦为乾、体卦为巽
            put("乾坎", YONG_SHENG_TI); // 用卦为乾、体卦为坎
            put("乾离", TI_KE_YONG);    // 用卦为乾、体卦为离
            put("乾艮", TI_SHENG_YONG); // 用卦为乾、体卦为艮
            put("乾兑", YONG_TI_BI_HE); // 用卦为乾、体卦为兑
            put("兑乾", YONG_TI_BI_HE); // 用卦为兑、体卦为乾
            put("兑坤", TI_SHENG_YONG); // 用卦为兑、体卦为坤
            put("兑震", YONG_KE_TI);    // 用卦为兑、体卦为震
            put("兑巽", YONG_KE_TI);    // 用卦为兑、体卦为巽
            put("兑坎", YONG_SHENG_TI); // 用卦为兑、体卦为坎
            put("兑离", TI_KE_YONG);    // 用卦为兑、体卦为离
            put("兑艮", TI_SHENG_YONG); // 用卦为兑、体卦为艮
            put("兑兑", YONG_TI_BI_HE); // 用卦为兑、体卦为兑
            put("离乾", YONG_KE_TI);    // 用卦为离、体卦为乾
            put("离坤", YONG_SHENG_TI); // 用卦为离、体卦为坤
            put("离震", TI_SHENG_YONG); // 用卦为离、体卦为震
            put("离巽", TI_SHENG_YONG); // 用卦为离、体卦为巽
            put("离坎", TI_KE_YONG);    // 用卦为离、体卦为坎
            put("离离", YONG_TI_BI_HE); // 用卦为离、体卦为离
            put("离艮", YONG_SHENG_TI); // 用卦为离、体卦为艮
            put("离兑", YONG_KE_TI);    // 用卦为离、体卦为兑
            put("震乾", TI_KE_YONG);    // 用卦为震、体卦为乾
            put("震坤", YONG_KE_TI);    // 用卦为震、体卦为坤
            put("震震", YONG_TI_BI_HE); // 用卦为震、体卦为震
            put("震巽", YONG_TI_BI_HE); // 用卦为震、体卦为巽
            put("震坎", TI_SHENG_YONG); // 用卦为震、体卦为坎
            put("震离", YONG_SHENG_TI); // 用卦为震、体卦为离
            put("震艮", YONG_KE_TI);    // 用卦为震、体卦为艮
            put("震兑", TI_KE_YONG);    // 用卦为震、体卦为兑
            put("巽乾", TI_KE_YONG);    // 用卦为巽、体卦为乾
            put("巽坤", YONG_KE_TI);    // 用卦为巽、体卦为坤
            put("巽震", YONG_TI_BI_HE); // 用卦为巽、体卦为震
            put("巽巽", YONG_TI_BI_HE); // 用卦为巽、体卦为巽
            put("巽坎", TI_SHENG_YONG); // 用卦为巽、体卦为坎
            put("巽离", YONG_SHENG_TI); // 用卦为巽、体卦为离
            put("巽艮", YONG_KE_TI);    // 用卦为巽、体卦为艮
            put("巽兑", TI_KE_YONG);    // 用卦为巽、体卦为兑
            put("坎乾", TI_SHENG_YONG); // 用卦为坎、体卦为乾
            put("坎坤", TI_KE_YONG);    // 用卦为坎、体卦为坤
            put("坎震", YONG_SHENG_TI); // 用卦为坎、体卦为震
            put("坎巽", YONG_SHENG_TI); // 用卦为坎、体卦为巽
            put("坎坎", YONG_TI_BI_HE); // 用卦为坎、体卦为坎
            put("坎离", YONG_KE_TI);    // 用卦为坎、体卦为离
            put("坎艮", TI_KE_YONG);    // 用卦为坎、体卦为艮
            put("坎兑", TI_SHENG_YONG); // 用卦为坎、体卦为兑
            put("艮乾", YONG_SHENG_TI); // 用卦为艮、体卦为乾
            put("艮坤", YONG_TI_BI_HE); // 用卦为艮、体卦为坤
            put("艮震", TI_KE_YONG);    // 用卦为艮、体卦为震
            put("艮巽", TI_KE_YONG);    // 用卦为艮、体卦为巽
            put("艮坎", YONG_KE_TI);    // 用卦为艮、体卦为坎
            put("艮离", TI_SHENG_YONG); // 用卦为艮、体卦为离
            put("艮艮", YONG_TI_BI_HE); // 用卦为艮、体卦为艮
            put("艮兑", YONG_SHENG_TI); // 用卦为艮、体卦为兑
            put("坤乾", YONG_SHENG_TI); // 用卦为坤、体卦为乾
            put("坤坤", YONG_TI_BI_HE); // 用卦为坤、体卦为坤
            put("坤震", TI_KE_YONG);    // 用卦为坤、体卦为震
            put("坤巽", TI_KE_YONG);    // 用卦为坤、体卦为巽
            put("坤坎", YONG_KE_TI);    // 用卦为坤、体卦为坎
            put("坤离", TI_SHENG_YONG); // 用卦为坤、体卦为离
            put("坤艮", YONG_TI_BI_HE); // 用卦为坤、体卦为艮
            put("坤兑", YONG_SHENG_TI); // 用卦为坤、体卦为兑
        }
    };

    /**
     * 用卦生体卦
     */
    private static final String YONG_SHENG_TI = "用卦一般代表这件事情，体卦一般代表自身，用卦生体卦，大吉，这件事情极有可能成功（成功率在90%左右）。";

    /**
     * 用卦与体卦比和
     */
    private static final String YONG_TI_BI_HE = "用卦一般代表这件事情，体卦一般代表自身，用卦与体卦比和，次吉，这件事情很容易成功（成功率在70%左右）。";

    /**
     * 体卦克用卦
     */
    private static final String TI_KE_YONG = "用卦一般代表这件事情，体卦一般代表自身，体卦克用卦，中平，这件事情再努力一下就可能会成功（成功率在40%~60%左右）。";

    /**
     * 体卦生用卦
     */
    private static final String TI_SHENG_YONG = "用卦一般代表这件事情，体卦一般代表自身，体卦生用卦，小凶，这件事情成功的可能性比较小（成功率在30%左右）。";

    /**
     * 用卦克体卦
     */
    private static final String YONG_KE_TI = "用卦一般代表这件事情，体卦一般代表自身，用卦克体卦，大凶，这件事情极有可能不成功（成功率仅在10%左右）。";


}
