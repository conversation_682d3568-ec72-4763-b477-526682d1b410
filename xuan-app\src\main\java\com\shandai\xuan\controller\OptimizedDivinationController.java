package com.shandai.xuan.controller;

import com.shandai.xuan.dto.LiuYaoAiAnalysisRequest;
import com.shandai.xuan.service.LiuYaoAiAnalysisService;
import com.shandai.xuan.service.PerformanceOptimizedAIService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 优化版占卜控制器 - 提供多种分析模式
 */
@RestController
@RequestMapping("/api/optimized")
@CrossOrigin(origins = "*")
public class OptimizedDivinationController {

    private static final Logger logger = LoggerFactory.getLogger(OptimizedDivinationController.class);

    @Autowired
    private LiuYaoAiAnalysisService originalService;

    @Autowired
    private PerformanceOptimizedAIService optimizedService;

    /**
     * 原始AI分析（保持不变）
     */
    @PostMapping("/ai-analysis/original")
    public Map<String, Object> originalAiAnalysis(@RequestBody LiuYaoAiAnalysisRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            logger.info("开始原始AI分析");
            long startTime = System.currentTimeMillis();
            
            String result = originalService.executeAnalysis(request);
            
            long duration = System.currentTimeMillis() - startTime;
            
            response.put("success", true);
            response.put("result", result);
            response.put("duration", duration + "ms");
            response.put("mode", "original");
            
            logger.info("原始AI分析完成，耗时: {}ms", duration);
            
        } catch (Exception e) {
            logger.error("原始AI分析失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return response;
    }

    /**
     * 异步AI分析 - 立即返回RequestID
     */
    @PostMapping("/ai-analysis/async")
    public Map<String, Object> asyncAiAnalysis(@RequestBody LiuYaoAiAnalysisRequest request) {
        Map<String, Object> response = new HashMap<>();

        try {
            logger.info("开始异步AI分析");

            PerformanceOptimizedAIService.AsyncAnalysisResult result = optimizedService.executeAnalysisAsync(request);

            response.put("success", true);
            response.put("requestId", result.getRequestId());
            response.put("queuePosition", result.getQueuePosition());
            response.put("estimatedWaitMinutes", result.getEstimatedWaitMinutes());
            response.put("status", result.getStatus().toString());
            response.put("message", result.getQueuePosition() > 0 ?
                String.format("您前面还有%d个用户，预计等待%d分钟", result.getQueuePosition(), result.getEstimatedWaitMinutes()) :
                "AI分析已开始处理");
            response.put("mode", "async");
            response.put("timestamp", System.currentTimeMillis());

            logger.info("异步AI分析任务创建成功: {}", result.getRequestId());

        } catch (Exception e) {
            logger.error("异步AI分析失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }

        return response;
    }

    /**
     * 获取异步分析结果
     */
    @GetMapping("/ai-analysis/result/{requestId}")
    public Map<String, Object> getAsyncResult(@PathVariable String requestId) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 获取任务状态
            PerformanceOptimizedAIService.TaskStatus status = optimizedService.getTaskStatus(requestId);
            if (status == null) {
                response.put("success", false);
                response.put("error", "请求不存在: " + requestId);
                return response;
            }

            response.put("requestId", requestId);
            response.put("status", status.toString());

            // 如果任务已完成，获取结果
            if (status == PerformanceOptimizedAIService.TaskStatus.USER_NOTIFIED ||
                status == PerformanceOptimizedAIService.TaskStatus.COMPLETED) {

                CompletableFuture<String> resultFuture = optimizedService.getAsyncResult(requestId);
                if (resultFuture.isDone() && !resultFuture.isCompletedExceptionally()) {
                    String result = resultFuture.get();
                    response.put("success", true);
                    response.put("result", result);
                    response.put("completed", true);
                } else {
                    response.put("success", true);
                    response.put("completed", false);
                    response.put("message", "分析进行中...");
                }
            } else if (status == PerformanceOptimizedAIService.TaskStatus.FAILED) {
                response.put("success", false);
                response.put("error", "分析失败");
                response.put("completed", true);
            } else {
                response.put("success", true);
                response.put("completed", false);
                response.put("message", "分析进行中...");
            }

        } catch (Exception e) {
            logger.error("获取异步结果失败: {}", requestId, e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }

        return response;
    }

    /**
     * 快速AI分析（30秒超时）
     */
    @PostMapping("/ai-analysis/fast")
    public Map<String, Object> fastAiAnalysis(@RequestBody LiuYaoAiAnalysisRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            logger.info("开始快速AI分析");
            long startTime = System.currentTimeMillis();
            
            String result = optimizedService.executeFastAnalysis(request);
            
            long duration = System.currentTimeMillis() - startTime;
            
            response.put("success", true);
            response.put("result", result);
            response.put("duration", duration + "ms");
            response.put("mode", "fast");
            response.put("timeout", "30秒");
            
            logger.info("快速AI分析完成，耗时: {}ms", duration);
            
        } catch (Exception e) {
            logger.error("快速AI分析失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return response;
    }

    /**
     * 标准AI分析（60秒超时）
     */
    @PostMapping("/ai-analysis/standard")
    public Map<String, Object> standardAiAnalysis(@RequestBody LiuYaoAiAnalysisRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            logger.info("开始标准AI分析");
            long startTime = System.currentTimeMillis();
            
            String result = optimizedService.executeStandardAnalysis(request);
            
            long duration = System.currentTimeMillis() - startTime;
            
            response.put("success", true);
            response.put("result", result);
            response.put("duration", duration + "ms");
            response.put("mode", "standard");
            response.put("timeout", "60秒");
            
            logger.info("标准AI分析完成，耗时: {}ms", duration);
            
        } catch (Exception e) {
            logger.error("标准AI分析失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取服务状态
     */
    @GetMapping("/status")
    public Map<String, Object> getStatus() {
        Map<String, Object> response = new HashMap<>();

        response.put("originalService", "正常");
        response.put("optimizedService", optimizedService.getServiceStatus());
        response.put("performanceStats", optimizedService.getPerformanceStats());
        response.put("healthStatus", optimizedService.getHealthStatus());
        response.put("timestamp", System.currentTimeMillis());

        return response;
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Map<String, Object> healthCheck() {
        return optimizedService.getHealthStatus();
    }

    /**
     * 测试异步分析 - 使用固定RequestID
     */
    @PostMapping("/ai-analysis/test")
    public Map<String, Object> testAsyncAnalysis() {
        Map<String, Object> response = new HashMap<>();

        try {
            // 创建测试请求
            LiuYaoAiAnalysisRequest testRequest = new LiuYaoAiAnalysisRequest();
            // 这里需要设置测试数据...

            PerformanceOptimizedAIService.AsyncAnalysisResult testResult = optimizedService.executeAnalysisAsync(testRequest);

            response.put("success", true);
            response.put("requestId", testResult.getRequestId());
            response.put("message", "测试异步分析已开始");
            response.put("testMode", true);

        } catch (Exception e) {
            logger.error("测试异步分析失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }

        return response;
    }

    /**
     * 获取排队状态
     */
    @GetMapping("/queue-status")
    public Map<String, Object> getQueueStatus() {
        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> healthStatus = optimizedService.getHealthStatus();

            response.put("success", true);
            response.put("currentQueuePosition", healthStatus.get("queuePosition"));
            response.put("estimatedWaitMinutes", healthStatus.get("estimatedWaitMinutes"));
            response.put("activeProcessing", healthStatus.get("activeProcessing"));
            response.put("totalRequests", healthStatus.get("totalRequests"));
            response.put("systemStatus", healthStatus.get("status"));
            response.put("healthy", healthStatus.get("healthy"));
            response.put("timestamp", System.currentTimeMillis());

        } catch (Exception e) {
            logger.error("获取排队状态失败", e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }

        return response;
    }

    /**
     * 测试网络连接
     */
    @GetMapping("/network-test")
    public Map<String, Object> testNetwork() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 这里可以添加简单的网络测试逻辑
            response.put("success", true);
            response.put("message", "网络连接正常");
            response.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return response;
    }
}
