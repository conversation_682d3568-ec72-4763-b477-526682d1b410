package com.shandai.xuan.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * 模拟流式AI服务 - 用于测试快速分析功能
 */
@Service
@Primary
@ConditionalOnProperty(name = "ai.mock.enabled", havingValue = "true", matchIfMissing = false)
public class MockStreamingAIService extends StreamingAIService {
    
    private static final Logger logger = LoggerFactory.getLogger(MockStreamingAIService.class);
    
    public MockStreamingAIService(RestTemplate restTemplate) {
        super("mock-api-key", "mock-url", "mock-model", restTemplate);
    }
    
    @Override
    public String getFastAnalysis(String prompt) {
        logger.info("使用模拟快速AI分析服务");
        
        // 模拟快速分析延迟（比深度分析更快）
        try {
            Thread.sleep(1000); // 模拟1秒分析时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 返回模拟的快速六爻分析结果
        return generateMockFastAnalysis(prompt);
    }
    
    /**
     * 生成模拟的快速分析结果
     */
    private String generateMockFastAnalysis(String prompt) {
        return "【快速AI分析结果】\n\n" +
               "🔮 **卦象概览**\n" +
               "本卦显示当前情况正处于变化之中，需要谨慎应对。\n\n" +
               "⚡ **核心要点**\n" +
               "• 时机：当前时机尚未完全成熟\n" +
               "• 态度：保持耐心，稳中求进\n" +
               "• 建议：多观察，少行动\n\n" +
               "🎯 **简要建议**\n" +
               "建议您在当前阶段以观察为主，不宜急于求成。事情的发展需要时间，保持平常心最为重要。\n\n" +
               "💡 **提示**：这是模拟AI分析结果，用于功能测试。";
    }
}
