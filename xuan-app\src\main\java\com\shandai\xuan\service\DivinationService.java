package com.shandai.xuan.service;

import com.shandai.xuan.dto.BaZiRequest;
import com.shandai.xuan.dto.LiuYaoRequest;
import com.shandai.xuan.dto.TieBanRequest;
import com.xuan.core.bazi.BaZi;
import com.xuan.core.bazi.BaZiSetting;
import com.xuan.core.liuyao.LiuYao;
import com.xuan.core.liuyao.LiuYaoSetting;
import com.xuan.core.liuyao.LiuYaoUtil;
import com.xuan.tieban.TieBan;
import com.nlf.calendar.Lunar;
import com.nlf.calendar.Solar;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

@Service
public class DivinationService {

    @Autowired
    private AIService aiService;
    
    public Map<String, Object> calculateBaZi(BaZiRequest request) {
        try {
            // 创建日期
            Calendar c = Calendar.getInstance();
            c.set(request.getYear(), request.getMonth() - 1, request.getDay(),
                 request.getHour(), request.getMinute(), 0);

            // 创建八字设置
            BaZiSetting setting = new BaZiSetting();
            setting.setSex(request.getSex());
            setting.setName(request.getName());
            setting.setOccupy(request.getOccupy());
            setting.setAddress(request.getAddress());
            setting.setDate(c.getTime());
            setting.setDateType(request.getDateType());
            setting.setLeapMonth(request.getLeapMonth());
            setting.setQiYunLiuPai(request.getQiYunLiuPai());
            setting.setYearGanZhiSet(request.getYearGanZhiSet());
            setting.setMonthGanZhiSet(request.getMonthGanZhiSet());
            setting.setDayGanZhiSet(request.getDayGanZhiSet());
            setting.setHourGanZhiSet(request.getHourGanZhiSet());
            setting.setRenYuan(request.getRenYuan());

            // 计算八字
            BaZi baZi = new BaZi(setting);

            // 组装八字数据为 Map
            Map<String, Object> baziData = new HashMap<>();
            baziData.put("name", baZi.getName());
            baziData.put("sex", baZi.getSex());
            baziData.put("zao", baZi.getZao());
            baziData.put("occupy", baZi.getOccupy());
            baziData.put("address", baZi.getAddress());
            baziData.put("solarStr", baZi.getSolarStr());
            baziData.put("lunarStr", baZi.getLunarStr());
            baziData.put("week", baZi.getWeek());
            baziData.put("season", baZi.getSeason());
            baziData.put("zodiac", baZi.getZodiac());
            baziData.put("constellation", baZi.getConstellation());

            baziData.put("baZi", baZi.getBaZi()); // List<String>
            baziData.put("baZiWuXing", baZi.getBaZiWuXing()); // List<String>
            baziData.put("baZiNaYin", baZi.getBaZiNaYin()); // List<String>
            baziData.put("baZiXunKong", baZi.getBaZiXunKong()); // List<String>

            baziData.put("yearGanZhiShenSha", baZi.getYearGanZhiShenSha());
            baziData.put("monthGanZhiShenSha", baZi.getMonthGanZhiShenSha());
            baziData.put("dayGanZhiShenSha", baZi.getDayGanZhiShenSha());
            baziData.put("hourGanZhiShenSha", baZi.getHourGanZhiShenSha());

            baziData.put("tianGanLiuYi", baZi.getTianGanLiuYi());
            baziData.put("diZhiLiuYi", baZi.getDiZhiLiuYi());

            baziData.put("qiYun", baZi.getQiYun());
            baziData.put("qiYunDate", baZi.getQiYunDate());
            baziData.put("renYuan", baZi.getRenYuan());
            baziData.put("birthSolarTerms", baZi.getBirthSolarTerms());

            baziData.put("ming", baZi.getMing());
            baziData.put("mingGua", baZi.getMingGua());
            baziData.put("mingGuaInfo", baZi.getMingGuaInfo());

            baziData.put("baZiWuXingQueShi", baZi.getBaZiWuXingQueShi());
            baziData.put("baZiWuXingCount", baZi.getBaZiWuXingCount());
            baziData.put("wuXingWangShuai", baZi.getWuXingWangShuai());

            baziData.put("bodyIntensity", baZi.getBodyIntensity());
            baziData.put("xiYongShen", baZi.getXiYongShen());
            baziData.put("xiYongShenFangWei", baZi.getXiYongShenFangWei());

            baziData.put("dayZhuLunMing", baZi.getDayZhuLunMing());
            baziData.put("yinYuan", baZi.getYinYuan());
            baziData.put("wuXingFenXi", baZi.getWuXingFenXi());

            // 包装在result中，符合前端期望的数据结构
            Map<String, Object> result = new HashMap<>();
            result.put("bazi", baziData);

            return result;
        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "计算八字出错：" + e.getMessage());
            return errorResult;
        }
    }
    
    public Map<String, Object> calculateLiuYao(LiuYaoRequest request) {
        try {
            // 创建日期
            Calendar c = Calendar.getInstance();
            c.set(request.getYear(), request.getMonth() - 1, request.getDay(),
                 request.getHour(), request.getMinute(), 0);

            // 创建六爻设置
            LiuYaoSetting setting = new LiuYaoSetting();
            setting.setSex(request.getSex());
            setting.setName(request.getName());
            setting.setOccupy(request.getOccupy());
            setting.setAddress(request.getAddress());
            setting.setDate(c.getTime());
            setting.setDateType(request.getDateType());
            setting.setLeapMonth(request.getLeapMonth());
            setting.setQiGuaMode(request.getQiGuaMode());
            setting.setLiuYao(request.getLiuYao());
            setting.setWuYao(request.getWuYao());
            setting.setSiYao(request.getSiYao());
            setting.setSanYao(request.getSanYao());
            setting.setErYao(request.getErYao());
            setting.setYiYao(request.getYiYao());
            setting.setYearGanZhiSet(request.getYearGanZhiSet());

            // 计算六爻
            LiuYao liuYao = new LiuYao(setting);

            // 组装六爻数据为 Map
            Map<String, Object> liuyaoData = new HashMap<>();
            liuyaoData.put("solarStr", liuYao.getSolarStr());
            liuyaoData.put("lunarStr", liuYao.getLunarStr());
            liuyaoData.put("week", liuYao.getWeek());

            liuyaoData.put("baZi", liuYao.getBaZi()); // List<String>
            liuyaoData.put("baZiWuXing", liuYao.getBaZiWuXing()); // List<String>
            liuyaoData.put("baZiNaYin", liuYao.getBaZiNaYin()); // List<String>
            liuyaoData.put("baZiXunKong", liuYao.getBaZiXunKong()); // List<String>

            liuyaoData.put("liuYaoAs", liuYao.getLiuYaoAs()); // // List<String> (卦象符号)
            liuyaoData.put("liuYaoYaoXiangMark", liuYao.getLiuYaoYaoXiangMark()); // List<String>
            liuyaoData.put("liuYaoYaoXiangMarkName", liuYao.getLiuYaoYaoXiangMarkName()); // String

            liuyaoData.put("shangGua", liuYao.getShangGua()); // String
            liuyaoData.put("shangGuaAs", liuYao.getShangGuaAs()); // String (卦象符号)
            liuyaoData.put("xiaGua", liuYao.getXiaGua()); // String
            liuyaoData.put("xiaGuaAs", liuYao.getXiaGuaAs()); // String (卦象符号)
            // 返回benGuaLiuYaoName、benGuaLiuYaoShiYing、benGuaLiuYaoLiuQin、benGuaLiuYaoGanZhi、benGuaLiuYaoWuXing、
            // benGuaLiuYaoLiuShen、benGuaLiuYaoYaoCi
            liuyaoData.put("benGuaLiuYaoLiuShen", liuYao.getBenGuaLiuYaoLiuShen()); // List<String>
            liuyaoData.put("benGuaCangYaoLiuQin", liuYao.getBenGuaCangYaoLiuQin()); // List<String>
            liuyaoData.put("benGuaCangYaoGanZhi", liuYao.getBenGuaCangYaoGanZhi()); // List<String>
            liuyaoData.put("benGuaLiuYaoLiuQin", liuYao.getBenGuaLiuYaoLiuQin()); // List<String>
            liuyaoData.put("benGuaLiuYaoWuXing", liuYao.getBenGuaLiuYaoWuXing()); // List<String>
            liuyaoData.put("benGuaLiuYaoShiYing", liuYao.getBenGuaLiuYaoShiYing()); // List<String>
            liuyaoData.put("benGua", liuYao.getBenGua()); // String
            liuyaoData.put("benGuaAs", liuYao.getBenGuaAs()); // String (卦象符号)
            liuyaoData.put("benGuaGuaCi", liuYao.getBenGuaGuaCi()); // String
            liuyaoData.put("benGuaLiuYaoName", liuYao.getBenGuaLiuYaoName()); // List<String>
            liuyaoData.put("benGuaLiuYaoAs", liuYao.getBenGuaLiuYaoAs()); // List<String>
            liuyaoData.put("benguainformation", liuYao.getBenguainformation()); // List<List<String>>
            liuyaoData.put("liuYaoWangShuaiPercent", liuYao.getLiuYaoWangShuaiPercent()); // List<List<String>>
            liuyaoData.put("bianguainformation", liuYao.getBianguainformation()); // List<List<String>>
            liuyaoData.put("duangua", liuYao.getDuangua()); // 断卦

            // 变卦信息
            liuyaoData.put("bianGua", liuYao.getBianGua()); // String
            liuyaoData.put("bianGuaAs", liuYao.getBianGuaAs()); // String
            liuyaoData.put("bianGuaLiuYaoAs", liuYao.getBianGuaLiuYaoAs()); // List<String>
            liuyaoData.put("bianGuaLiuYaoName", liuYao.getBianGuaLiuYaoName()); // List<String>
            liuyaoData.put("bianGuaLiuYaoShiYing", liuYao.getBianGuaLiuYaoShiYing()); // List<String>
            liuyaoData.put("bianGuaLiuYaoLiuQin", liuYao.getBianGuaLiuYaoLiuQin()); // List<String>
            liuyaoData.put("bianGuaLiuYaoWuXing", liuYao.getBianGuaLiuYaoWuXing()); // List<String>
            liuyaoData.put("bianGuaLiuYaoLiuShen", liuYao.getBianGuaLiuYaoLiuShen()); // List<String>
            liuyaoData.put("bianGuaGuaCi", liuYao.getBianGuaGuaCi()); // String

            // 构造Prompt
            String prompt = buildLiuYaoPrompt(request, liuYao);
            String aiAnalysis = aiService.getAnalysis(prompt);
            liuyaoData.put("aiAnalysis", aiAnalysis);

            // 包装在result中，符合前端期望的数据结构
            Map<String, Object> result = new HashMap<>();
            result.put("liuyao", liuyaoData);

            return result;
        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "计算六爻出错：" + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 计算六爻数据（不包含AI分析）
     */
    public Map<String, Object> calculateLiuYaoWithoutAI(LiuYaoRequest request) {
        try {
            // 创建日期
            Calendar c = Calendar.getInstance();
            c.set(request.getYear(), request.getMonth() - 1, request.getDay(),
                 request.getHour(), request.getMinute(), 0);

            // 创建六爻设置
            LiuYaoSetting setting = new LiuYaoSetting();
            setting.setSex(request.getSex());
            setting.setName(request.getName());
            setting.setOccupy(request.getOccupy());
            setting.setAddress(request.getAddress());
            setting.setDate(c.getTime());
            setting.setDateType(request.getDateType());
            setting.setLeapMonth(request.getLeapMonth());
            setting.setQiGuaMode(request.getQiGuaMode());
            setting.setLiuYao(request.getLiuYao());
            setting.setWuYao(request.getWuYao());
            setting.setSiYao(request.getSiYao());
            setting.setSanYao(request.getSanYao());
            setting.setErYao(request.getErYao());
            setting.setYiYao(request.getYiYao());
            setting.setYearGanZhiSet(request.getYearGanZhiSet());

            // 计算六爻（不包含AI分析）
            LiuYao liuYao = new LiuYao(setting);

            // 组装完整的六爻数据为 Map（不包含AI分析）
            Map<String, Object> liuyaoData = new HashMap<>();
            liuyaoData.put("solarStr", liuYao.getSolarStr());
            liuyaoData.put("lunarStr", liuYao.getLunarStr());
            liuyaoData.put("week", liuYao.getWeek());

            liuyaoData.put("baZi", liuYao.getBaZi()); // List<String>
            liuyaoData.put("baZiWuXing", liuYao.getBaZiWuXing()); // List<String>
            liuyaoData.put("baZiNaYin", liuYao.getBaZiNaYin()); // List<String>
            liuyaoData.put("baZiXunKong", liuYao.getBaZiXunKong()); // List<String>

            liuyaoData.put("liuYaoAs", liuYao.getLiuYaoAs()); // List<String> (卦象符号)
            liuyaoData.put("liuYaoYaoXiangMark", liuYao.getLiuYaoYaoXiangMark()); // List<String>
            liuyaoData.put("liuYaoYaoXiangMarkName", liuYao.getLiuYaoYaoXiangMarkName()); // String

            liuyaoData.put("shangGua", liuYao.getShangGua()); // String
            liuyaoData.put("shangGuaAs", liuYao.getShangGuaAs()); // String (卦象符号)
            liuyaoData.put("xiaGua", liuYao.getXiaGua()); // String
            liuyaoData.put("xiaGuaAs", liuYao.getXiaGuaAs()); // String (卦象符号)

            liuyaoData.put("benGuaLiuYaoLiuShen", liuYao.getBenGuaLiuYaoLiuShen()); // List<String>
            liuyaoData.put("benGuaCangYaoLiuQin", liuYao.getBenGuaCangYaoLiuQin()); // List<String>
            liuyaoData.put("benGuaCangYaoGanZhi", liuYao.getBenGuaCangYaoGanZhi()); // List<String>
            liuyaoData.put("benGuaLiuYaoLiuQin", liuYao.getBenGuaLiuYaoLiuQin()); // List<String>
            liuyaoData.put("benGuaLiuYaoWuXing", liuYao.getBenGuaLiuYaoWuXing()); // List<String>
            liuyaoData.put("benGuaLiuYaoShiYing", liuYao.getBenGuaLiuYaoShiYing()); // List<String>
            liuyaoData.put("benGua", liuYao.getBenGua()); // String
            liuyaoData.put("benGuaAs", liuYao.getBenGuaAs()); // String (卦象符号)
            liuyaoData.put("benGuaGuaCi", liuYao.getBenGuaGuaCi()); // String
            liuyaoData.put("benGuaLiuYaoName", liuYao.getBenGuaLiuYaoName()); // List<String>
            liuyaoData.put("benGuaLiuYaoAs", liuYao.getBenGuaLiuYaoAs()); // List<String>
            liuyaoData.put("benguainformation", liuYao.getBenguainformation()); // List<List<String>>
            liuyaoData.put("liuYaoWangShuaiPercent", liuYao.getLiuYaoWangShuaiPercent()); // List<List<String>>
            liuyaoData.put("bianguainformation", liuYao.getBianguainformation()); // List<List<String>>
            liuyaoData.put("duangua", liuYao.getDuangua()); // 断卦

            // 变卦信息
            liuyaoData.put("bianGua", liuYao.getBianGua()); // String
            liuyaoData.put("bianGuaAs", liuYao.getBianGuaAs()); // String
            liuyaoData.put("bianGuaLiuYaoAs", liuYao.getBianGuaLiuYaoAs()); // List<String>
            liuyaoData.put("bianGuaLiuYaoName", liuYao.getBianGuaLiuYaoName()); // List<String>
            liuyaoData.put("bianGuaLiuYaoShiYing", liuYao.getBianGuaLiuYaoShiYing()); // List<String>
            liuyaoData.put("bianGuaLiuYaoLiuQin", liuYao.getBianGuaLiuYaoLiuQin()); // List<String>
            liuyaoData.put("bianGuaLiuYaoWuXing", liuYao.getBianGuaLiuYaoWuXing()); // List<String>
            liuyaoData.put("bianGuaLiuYaoLiuShen", liuYao.getBianGuaLiuYaoLiuShen()); // List<String>
            liuyaoData.put("bianGuaGuaCi", liuYao.getBianGuaGuaCi()); // String

            // 包装在result中，符合前端期望的数据结构
            Map<String, Object> result = new HashMap<>();
            result.put("liuyao", liuyaoData);

            return result;
        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "计算六爻出错：" + e.getMessage());
            return errorResult;
        }
    }

    private String buildLiuYaoPrompt(LiuYaoRequest request, LiuYao liuYao) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("你是一位精通六爻占卜的大师。请根据以下信息，为求测者提供专业的断卦分析。\n\n");
        prompt.append("--- 问事 ---\n");
        prompt.append(request.getOccupy()).append("\n\n");

        prompt.append("--- 卦象信息 ---\n");
        prompt.append("时间：").append(liuYao.getSolarStr()).append(" ").append(liuYao.getLunarStr()).append("\n");
        prompt.append("旬空：").append(String.join(" ", liuYao.getBaZiXunKong())).append("\n");
        prompt.append("本卦：").append(liuYao.getBenGua()).append(" (").append(liuYao.getBenGuaAs()).append(")\n");
        if (liuYao.getBianGua() != null && !liuYao.getBianGua().isEmpty()) {
            prompt.append("变卦：").append(liuYao.getBianGua()).append(" (").append(liuYao.getBianGuaAs()).append(")\n\n");
        }

        prompt.append("--- 本卦详解 ---\n");
        List<List<String>> benGuaInfo = liuYao.getBenguainformation();
        if (benGuaInfo != null && !benGuaInfo.isEmpty()) {
            for (List<String> yaoInfo : benGuaInfo) {
                if (yaoInfo != null) {
                    prompt.append(String.join("\t", yaoInfo)).append("\n");
                }
            }
        } else {
            prompt.append("本卦信息暂无\n");
        }
        prompt.append("\n");

        if (liuYao.getBianguainformation() != null && !liuYao.getBianguainformation().isEmpty()) {
            prompt.append("--- 变卦详解 ---\n");
            List<List<String>> bianGuaInfo = liuYao.getBianguainformation();
            for (List<String> yaoInfo : bianGuaInfo) {
                if (yaoInfo != null) {
                    prompt.append(String.join("\t", yaoInfo)).append("\n");
                }
            }
            prompt.append("\n");
        }

        prompt.append("--- 系统断卦参考 ---\n");
        prompt.append(liuYao.getDuangua()).append("\n\n");

        prompt.append("--- 你的分析 ---\n");
        prompt.append("请综合以上所有信息，围绕求测者的问题，从用神、原神、忌神、仇神、世爻、应爻、动爻、变爻等角度，进行详细、深入、专业的分析，并给出最终的吉凶判断和建议。");

        return prompt.toString();
    }


    /**
     * 获取铁板神数结果并封装为Map
     *
     * @param request 请求参数
     * @return 包含铁板神数结果的Map
     */
    public Map<String, Object> calculateTieBan(TieBanRequest request) {
        TieBan tieban = getTieBanResult(request);
        Map<String, Object> result = new HashMap<>();
        
        result.put("yearGan", tieban.getYearGan());
        result.put("yearZhi", tieban.getYearZhi());
        result.put("monthGan", tieban.getMonthGan());
        result.put("monthZhi", tieban.getMonthZhi());
        result.put("dayGan", tieban.getDayGan());
        result.put("dayZhi", tieban.getDayZhi());
        result.put("hourGan", tieban.getHourGan());
        result.put("hourZhi", tieban.getHourZhi());
        result.put("ages", tieban.getAges());
        result.put("comments", tieban.getComments());
        result.put("fsums", tieban.getFsums()); // 新增fsum值
        result.put("shangGuaNames", tieban.getShangGuaNames());
        result.put("xiaGuaNames", tieban.getXiaGuaNames());
        result.put("sums", tieban.getSums());

        // 添加太玄数计算结果
        result.put("taiXuanSums", tieban.getTaiXuanSums());
        result.put("taiXuanAges", tieban.getTaiXuanAges());
        result.put("taiXuanComments", tieban.getTaiXuanComments());

        // 添加四柱的卦象和干支信息
        result.put("niangua", tieban.getNiangua());
        result.put("nianzhuganzhi", tieban.getNianzhuganzhi());
        result.put("yuegua", tieban.getYuegua());
        result.put("yuezhuganzhi", tieban.getYuezhuganzhi());
        result.put("rigua", tieban.getRigua());
        result.put("rizhuganzhi", tieban.getRizhuganzhi());
        result.put("shigua", tieban.getShigua());
        result.put("shizhuganzhi", tieban.getShizhuganzhi());
        
        // 添加天干数断法的结果
        result.put("tianGanShuEntryNumbers", tieban.getTianGanShuEntryNumbers());
        result.put("tianGanShuAges", tieban.getTianGanShuAges());
        result.put("tianGanShuComments", tieban.getTianGanShuComments());

        return result;
    }


    /**
     * 获取铁板神数结果
     *
     * @param request 请求参数
     * @return 铁板神数对象
     */
    private TieBan getTieBanResult(TieBanRequest request) {
        // 1. 创建 LiuYaoSetting 以便利用 LiuYaoUtil 的完整干支计算逻辑
        LiuYaoSetting setting = new LiuYaoSetting(
                new Date(getCalendar(request.getYear(), request.getMonth(), request.getDay(),
                        request.getHour(), request.getMinute(), request.getSecond()).getTimeInMillis()),
                request.getDateType()
        );

        // 2. 从 LiuYaoUtil 获取所有日期和干支信息
        Map<String, Object> dateMap = LiuYaoUtil.isDateType(setting);
        Solar solar = (Solar) dateMap.get("solar");
        Lunar lunar = (Lunar) dateMap.get("lunar");
        Map<String, List<String>> ganZhiMap = LiuYaoUtil.isGanZhi(setting, lunar);

        // 3. 将计算好的、可靠的数据传递给 TieBan 的新构造函数
        return new TieBan(
                solar,
                lunar,
                ganZhiMap.get("yearGanZhi"),
                ganZhiMap.get("monthGanZhi"),
                ganZhiMap.get("dayGanZhi"),
                ganZhiMap.get("hourGanZhi")
        );
    }

    /**
     * 根据年月日时分秒获取日历对象
     *
     * @param year 年
     * @param month 月
     * @param day 日
     * @param hour 时
     * @param minute 分
     * @param second 秒
     * @return Calendar 对象
     */
    private Calendar getCalendar(int year, int month, int day, int hour, int minute, int second) {
        Calendar c = Calendar.getInstance();
        c.set(year, month - 1, day, hour, minute, second);
        return c;
    }
}