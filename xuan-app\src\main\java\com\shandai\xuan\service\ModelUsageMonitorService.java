package com.shandai.xuan.service;

import com.shandai.xuan.config.ModelConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

/**
 * 模型使用情况监控服务
 * 监控每个模型的RPM和每日请求使用情况
 */
@Service
@Slf4j
public class ModelUsageMonitorService {
    
    @Autowired
    private ModelConfig modelConfig;
    
    /**
     * 使用统计信息类
     */
    @Data
    public static class UsageStats {
        private String modelName;
        private int rpmLimit;
        private int dailyLimit;
        private AtomicInteger currentMinuteRequests = new AtomicInteger(0);
        private AtomicInteger currentDayRequests = new AtomicInteger(0);
        private AtomicLong lastRequestTime = new AtomicLong(0);
        private String lastRequestDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        
        public UsageStats(String modelName, int rpmLimit, int dailyLimit) {
            this.modelName = modelName;
            this.rpmLimit = rpmLimit;
            this.dailyLimit = dailyLimit;
        }
        
        /**
         * 获取RPM使用率百分比
         */
        public double getRpmUsagePercentage() {
            return (double) currentMinuteRequests.get() / rpmLimit * 100;
        }
        
        /**
         * 获取每日使用率百分比
         */
        public double getDailyUsagePercentage() {
            return (double) currentDayRequests.get() / dailyLimit * 100;
        }
        
        /**
         * 检查是否接近RPM限制
         */
        public boolean isNearRpmLimit(double threshold) {
            return getRpmUsagePercentage() >= threshold;
        }
        
        /**
         * 检查是否接近每日限制
         */
        public boolean isNearDailyLimit(double threshold) {
            return getDailyUsagePercentage() >= threshold;
        }
        
        /**
         * 检查是否可以发送请求
         */
        public boolean canSendRequest() {
            return currentMinuteRequests.get() < rpmLimit && currentDayRequests.get() < dailyLimit;
        }
    }
    
    // 存储每个模型的使用统计
    private final Map<String, UsageStats> usageStatsMap = new ConcurrentHashMap<>();
    
    // 当前活跃模型
    private volatile String currentActiveModel = "gemini-2.5-flash";
    
    /**
     * 初始化所有模型的使用统计
     */
    public void initializeUsageStats() {
        List<ModelConfig.ModelInfo> models = modelConfig.getPrimaryModels();
        for (ModelConfig.ModelInfo model : models) {
            usageStatsMap.put(model.getModelName(), 
                new UsageStats(model.getModelName(), model.getRpmLimit(), model.getDailyLimit()));
        }
        log.info("初始化了 {} 个模型的使用统计", usageStatsMap.size());
    }
    
    /**
     * 记录模型使用
     */
    @Async
    public void recordUsage(String modelName) {
        UsageStats stats = usageStatsMap.get(modelName);
        if (stats == null) {
            // 如果统计不存在，创建一个
            ModelConfig.ModelInfo modelInfo = modelConfig.getModelInfo(modelName);
            if (modelInfo != null) {
                stats = new UsageStats(modelName, modelInfo.getRpmLimit(), modelInfo.getDailyLimit());
                usageStatsMap.put(modelName, stats);
            } else {
                log.warn("未知模型: {}", modelName);
                return;
            }
        }
        
        long currentTime = System.currentTimeMillis();
        String currentDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        
        // 检查是否是新的一天，如果是则重置每日计数
        if (!currentDate.equals(stats.getLastRequestDate())) {
            stats.getCurrentDayRequests().set(0);
            stats.setLastRequestDate(currentDate);
            log.info("模型 {} 每日计数已重置", modelName);
        }
        
        // 增加计数
        stats.getCurrentMinuteRequests().incrementAndGet();
        stats.getCurrentDayRequests().incrementAndGet();
        stats.getLastRequestTime().set(currentTime);
        
        log.debug("记录模型 {} 使用: RPM={}/{}, 每日={}/{}", 
            modelName, 
            stats.getCurrentMinuteRequests().get(), stats.getRpmLimit(),
            stats.getCurrentDayRequests().get(), stats.getDailyLimit());
    }
    
    /**
     * 检查模型是否可用
     */
    public boolean isModelAvailable(String modelName, double threshold) {
        UsageStats stats = usageStatsMap.get(modelName);
        if (stats == null) {
            return true; // 如果没有统计信息，认为可用
        }
        
        return !stats.isNearRpmLimit(threshold) && !stats.isNearDailyLimit(threshold);
    }
    
    /**
     * 获取当前活跃模型
     */
    public String getCurrentActiveModel() {
        return currentActiveModel;
    }
    
    /**
     * 设置当前活跃模型
     */
    public void setCurrentActiveModel(String modelName) {
        this.currentActiveModel = modelName;
        log.info("切换到模型: {}", modelName);
    }
    
    /**
     * 获取所有模型的使用统计
     */
    public Map<String, UsageStats> getAllUsageStats() {
        return new ConcurrentHashMap<>(usageStatsMap);
    }
    
    /**
     * 获取指定模型的使用统计
     */
    public UsageStats getUsageStats(String modelName) {
        return usageStatsMap.get(modelName);
    }
    
    /**
     * 每分钟重置RPM计数器
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void resetMinuteCounters() {
        for (UsageStats stats : usageStatsMap.values()) {
            int oldCount = stats.getCurrentMinuteRequests().getAndSet(0);
            if (oldCount > 0) {
                log.debug("重置模型 {} 的分钟计数器: {}", stats.getModelName(), oldCount);
            }
        }
    }
    
    /**
     * 每小时检查并清理过期数据
     */
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void cleanupExpiredData() {
        String currentDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        for (UsageStats stats : usageStatsMap.values()) {
            if (!currentDate.equals(stats.getLastRequestDate())) {
                stats.getCurrentDayRequests().set(0);
                stats.setLastRequestDate(currentDate);
                log.info("清理模型 {} 的过期每日数据", stats.getModelName());
            }
        }
    }
    
    /**
     * 获取使用情况摘要
     */
    public String getUsageSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("=== 模型使用情况摘要 ===\n");
        summary.append(String.format("当前活跃模型: %s\n", currentActiveModel));
        summary.append("时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n\n");
        
        for (UsageStats stats : usageStatsMap.values()) {
            summary.append(String.format("模型: %s\n", stats.getModelName()));
            summary.append(String.format("  RPM: %d/%d (%.1f%%)\n", 
                stats.getCurrentMinuteRequests().get(), stats.getRpmLimit(), stats.getRpmUsagePercentage()));
            summary.append(String.format("  每日: %d/%d (%.1f%%)\n", 
                stats.getCurrentDayRequests().get(), stats.getDailyLimit(), stats.getDailyUsagePercentage()));
            summary.append(String.format("  可用: %s\n\n", stats.canSendRequest() ? "是" : "否"));
        }
        
        return summary.toString();
    }
}
