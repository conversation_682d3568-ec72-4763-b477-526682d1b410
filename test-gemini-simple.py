#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试Gemini API的不同提示词
"""

import requests
import json

# Gemini API配置
API_KEY = "AIzaSyAT2Xg1_6NZdWlpJ6j6dQ_qwpQcihARf7o"
API_URL = "https://gemini.scself1700.dpdns.org/v1/chat/completions"
MODEL = "gemini-2.5-flash"

def test_prompt(prompt, max_tokens=500):
    """测试单个提示词"""
    print(f"\n🔍 测试提示词: {prompt[:50]}...")
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": MODEL,
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.7,
        "max_tokens": max_tokens,
        "stream": False
    }
    
    try:
        response = requests.post(API_URL, headers=headers, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            # 打印完整响应用于调试
            print(f"📊 状态: {response.status_code}")
            print(f"🔍 完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if "choices" in result and len(result["choices"]) > 0:
                choice = result["choices"][0]
                message = choice.get("message", {})
                content = message.get("content")
                finish_reason = choice.get("finish_reason")
                
                print(f"✅ 内容: {content}")
                print(f"📝 结束原因: {finish_reason}")
                print(f"📊 Token使用: {result.get('usage', {})}")
                
                return content is not None and content.strip()
            
        else:
            print(f"❌ 请求失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"💥 错误: {e}")
        return False

def main():
    print("🚀 Gemini API简单提示词测试")
    print("="*50)
    
    # 测试不同类型的提示词
    test_cases = [
        ("你好", 50),
        ("1+1等于几？", 50),
        ("请说一句话", 50),
        ("介绍一下Python", 200),
        ("什么是人工智能？请简单回答。", 300),
        ("乾为天卦代表什么？", 500),
        ("请分析乾为天卦", 1000),
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for prompt, max_tokens in test_cases:
        if test_prompt(prompt, max_tokens):
            success_count += 1
        print("-" * 50)
    
    print(f"\n📊 测试结果: {success_count}/{total_count} 成功")
    
    if success_count == 0:
        print("\n❌ 所有测试都失败了，可能是中转站的问题")
    elif success_count < total_count:
        print(f"\n⚠️ 部分测试失败，可能与提示词长度或内容有关")
    else:
        print(f"\n🎉 所有测试都成功！")

if __name__ == "__main__":
    main()
