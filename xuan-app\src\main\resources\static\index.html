<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>玄学计算</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #666;
            font-weight: bold;
        }
        input[type="text"],
        input[type="number"],
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            white-space: pre-line;
            line-height: 1.6;
            max-height: 500px;
            overflow-y: auto;
        }
        .error {
            color: #d32f2f;
            background-color: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .form-row {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .form-row .form-group {
            flex: 1;
            min-width: 200px;
        }
        .gua-symbol {
            font-size: 16px;
        }
        .bengua-symbol {
            font-size: 160px;
            /* display: block; */
            text-align: center;
            line-height: 1;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>玄学计算系统</h1>
    
    <div class="container" id="bazi-form">
        <h2>八字计算</h2>
        <div class="form-row">
            <div class="form-group">
                <label>年：</label>
                <input type="number" id="bazi-year" placeholder="请输入年份">
            </div>
            <div class="form-group">
                <label>月：</label>
                <input type="number" id="bazi-month" min="1" max="12" placeholder="1-12">
            </div>
            <div class="form-group">
                <label>日：</label>
                <input type="number" id="bazi-day" min="1" max="31" placeholder="1-31">
            </div>
            <div class="form-group">
                <label>时：</label>
                <input type="number" id="bazi-hour" min="0" max="23" placeholder="0-23">
            </div>
            <div class="form-group">
                <label>分：</label>
                <input type="number" id="bazi-minute" min="0" max="59" placeholder="0-59">
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label>性别：</label>
                <select id="bazi-sex">
                    <option value="1">男</option>
                    <option value="0">女</option>
                </select>
            </div>
            <div class="form-group">
                <label>姓名：</label>
                <input type="text" id="bazi-name" placeholder="请输入姓名">
            </div>
            <div class="form-group">
                <label>占事：</label>
                <input type="text" id="bazi-occupy" placeholder="请输入占事">
            </div>
            <div class="form-group">
                <label>地区：</label>
                <input type="text" id="bazi-address" placeholder="请输入地区">
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label>日期类型：</label>
                <select id="bazi-date-type">
                    <option value="0">公历</option>
                    <option value="1">农历</option>
                </select>
            </div>
            <div class="form-group">
                <label>闰月：</label>
                <select id="bazi-leap-month">
                    <option value="0">不使用闰月</option>
                    <option value="1">使用闰月</option>
                </select>
            </div>
            <div class="form-group">
                <label>起运流派：</label>
                <select id="bazi-qiyun-liupai">
                    <option value="0">按天数和时辰数计算</option>
                    <option value="1">按分钟数计算</option>
                </select>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label>年干支设置：</label>
                <select id="bazi-year-ganzhi-set">
                    <option value="0">以正月初一作为新年的开始</option>
                    <option value="1">以立春当天作为新年的开始</option>
                    <option value="2">以立春交接的时刻作为新年的开始</option>
                </select>
            </div>
            <div class="form-group">
                <label>月干支设置：</label>
                <select id="bazi-month-ganzhi-set">
                    <option value="0">以节交接当天起算</option>
                    <option value="1">以节交接时刻起算</option>
                </select>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label>日干支设置：</label>
                <select id="bazi-day-ganzhi-set">
                    <option value="0">晚子时日干支算当天</option>
                    <option value="1">晚子时日干支算明天</option>
                </select>
            </div>
            <div class="form-group">
                <label>时干支设置：</label>
                <select id="bazi-hour-ganzhi-set">
                    <option value="0">支持早子时和晚子时</option>
                </select>
            </div>
            <div class="form-group">
                <label>人元司令分野：</label>
                <select id="bazi-renyuan">
                    <option value="0">子平真诠法决</option>
                    <option value="1">渊海子平法决</option>
                    <option value="2">星平会海法决</option>
                    <option value="3">三命通会法决</option>
                    <option value="4">神峰通考法决</option>
                    <option value="5">万育吾之法决</option>
                </select>
            </div>
        </div>
        <button onclick="calculateBaZi()">计算八字</button>
        <div id="bazi-result" class="result"></div>
    </div>

    <div class="container" id="liuyao-form">
        <h2>六爻计算</h2>
        <div class="form-row">
            <div class="form-group">
                <label>年：</label>
                <input type="number" id="liuyao-year" placeholder="请输入年份">
            </div>
            <div class="form-group">
                <label>月：</label>
                <input type="number" id="liuyao-month" min="1" max="12" placeholder="1-12">
            </div>
            <div class="form-group">
                <label>日：</label>
                <input type="number" id="liuyao-day" min="1" max="31" placeholder="1-31">
            </div>
            <div class="form-group">
                <label>时：</label>
                <input type="number" id="liuyao-hour" min="0" max="23" placeholder="0-23">
            </div>
            <div class="form-group">
                <label>分：</label>
                <input type="number" id="liuyao-minute" min="0" max="59" placeholder="0-59">
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label>性别：</label>
                <select id="liuyao-sex">
                    <option value="1">男</option>
                    <option value="0">女</option>
                </select>
            </div>
            <div class="form-group">
                <label>姓名：</label>
                <input type="text" id="liuyao-name" placeholder="请输入姓名">
            </div>
            <div class="form-group">
                <label>占事：</label>
                <input type="text" id="liuyao-occupy" placeholder="请输入占事">
            </div>
            <div class="form-group">
                <label>地区：</label>
                <input type="text" id="liuyao-address" placeholder="请输入地区">
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label>日期类型：</label>
                <select id="liuyao-date-type">
                    <option value="0">公历</option>
                    <option value="1">农历</option>
                </select>
            </div>
            <div class="form-group">
                <label>闰月：</label>
                <select id="liuyao-leap-month">
                    <option value="0">不使用闰月</option>
                    <option value="1">使用闰月</option>
                </select>
            </div>
            <div class="form-group">
                <label>起卦模式：</label>
                <select id="liuyao-qigua-mode">
                    <option value="0">日期</option>
                    <option value="1">自动</option>
                    <option value="2">手动</option>
                </select>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label>上爻(六爻)：</label>
                <select id="liuyao-liuyao">
                    <option value="0">—（2正1背）</option>
                    <option value="1">- -（1正2背）</option>
                    <option value="2">— ○（0正3背）</option>
                    <option value="3">- - ×（3正0背）</option>
                </select>
            </div>
            <div class="form-group">
                <label>五爻：</label>
                <select id="liuyao-wuyao">
                    <option value="0">—（2正1背）</option>
                    <option value="1">- -（1正2背）</option>
                    <option value="2">— ○（0正3背）</option>
                    <option value="3">- - ×（3正0背）</option>
                </select>
            </div>
            <div class="form-group">
                <label>四爻：</label>
                <select id="liuyao-siyao">
                    <option value="0">—（2正1背）</option>
                    <option value="1">- -（1正2背）</option>
                    <option value="2">— ○（0正3背）</option>
                    <option value="3">- - ×（3正0背）</option>
                </select>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label>三爻：</label>
                <select id="liuyao-sanyao">
                    <option value="0">—（2正1背）</option>
                    <option value="1">- -（1正2背）</option>
                    <option value="2">— ○（0正3背）</option>
                    <option value="3">- - ×（3正0背）</option>
                </select>
            </div>
            <div class="form-group">
                <label>二爻：</label>
                <select id="liuyao-eryao">
                    <option value="0">—（2正1背）</option>
                    <option value="1">- -（1正2背）</option>
                    <option value="2">— ○（0正3背）</option>
                    <option value="3">- - ×（3正0背）</option>
                </select>
            </div>
            <div class="form-group">
                <label>初爻(一爻)：</label>
                <select id="liuyao-yiyao">
                    <option value="0">—（2正1背）</option>
                    <option value="1">- -（1正2背）</option>
                    <option value="2">— ○（0正3背）</option>
                    <option value="3">- - ×（3正0背）</option>
                </select>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label>年干支设置：</label>
                <select id="liuyao-year-ganzhi-set">
                    <option value="0">以正月初一作为新年的开始</option>
                    <option value="1">以立春当天作为新年的开始</option>
                    <option value="2">以立春交接的时刻作为新年的开始</option>
                </select>
            </div>
        </div>
        <div class="form-row">
            <button onclick="calculateLiuYao()">计算六爻</button>
            <button onclick="calculateLiuYaoWithFastAI()" style="margin-left: 10px; background-color: #FF9800;">AI快速分析</button>
            <button onclick="calculateLiuYaoWithAI()" style="margin-left: 10px; background-color: #4CAF50;">AI深度分析（流式）</button>
        </div>
        <div id="liuyao-result" class="result"></div>
    </div>

    <div class="container" id="tieban-form">
        <h2>铁板神数计算</h2>
        <div class="form-row">
            <div class="form-group">
                <label>年：</label>
                <input type="number" id="tieban-year" placeholder="请输入年份">
            </div>
            <div class="form-group">
                <label>月：</label>
                <input type="number" id="tieban-month" min="1" max="12" placeholder="1-12">
            </div>
            <div class="form-group">
                <label>日：</label>
                <input type="number" id="tieban-day" min="1" max="31" placeholder="1-31">
            </div>
        </div>
        <div class="form-row">
             <div class="form-group">
                <label>时：</label>
                <input type="number" id="tieban-hour" min="0" max="23" placeholder="0-23">
            </div>
            <div class="form-group">
                <label>分：</label>
                <input type="number" id="tieban-minute" min="0" max="59" placeholder="0-59">
            </div>
            <div class="form-group">
                <label>秒：</label>
                <input type="number" id="tieban-second" value="0" min="0" max="59" placeholder="0-59">
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label>日期类型：</label>
                <select id="tieban-date-type">
                    <option value="0">公历</option>
                    <option value="1">农历</option>
                </select>
            </div>
        </div>
        <button onclick="calculateTieBan()">计算铁板神数</button>
        <div id="tieban-result" class="result"></div>
    </div>

    <script>
        function validateForm(formData, type) {
            const errors = [];
            
            if (!formData.year || formData.year < 1900 || formData.year > 2100) {
                errors.push("年份必须在1900-2100之间");
            }
            if (!formData.month || formData.month < 1 || formData.month > 12) {
                errors.push("月份必须在1-12之间");
            }
            if (!formData.day || formData.day < 1 || formData.day > 31) {
                errors.push("日期必须在1-31之间");
            }
            if (formData.hour === undefined || formData.hour < 0 || formData.hour > 23) {
                errors.push("小时必须在0-23之间");
            }
            if (formData.minute === undefined || formData.minute < 0 || formData.minute > 59) {
                errors.push("分钟必须在0-59之间");
            }
            
            if (!formData.name) {
                errors.push("请输入姓名");
            }
            if (!formData.occupy) {
                errors.push("请输入占事");
            }
            if (!formData.address) {
                errors.push("请输入地区");
            }
            
            return errors;
        }

        async function calculateBaZi() {
            const data = {
                year: parseInt(document.getElementById('bazi-year').value),
                month: parseInt(document.getElementById('bazi-month').value),
                day: parseInt(document.getElementById('bazi-day').value),
                hour: parseInt(document.getElementById('bazi-hour').value),
                minute: parseInt(document.getElementById('bazi-minute').value),
                sex: parseInt(document.getElementById('bazi-sex').value),
                name: document.getElementById('bazi-name').value,
                occupy: document.getElementById('bazi-occupy').value,
                address: document.getElementById('bazi-address').value,
                dateType: parseInt(document.getElementById('bazi-date-type').value),
                leapMonth: parseInt(document.getElementById('bazi-leap-month').value),
                qiYunLiuPai: parseInt(document.getElementById('bazi-qiyun-liupai').value),
                yearGanZhiSet: parseInt(document.getElementById('bazi-year-ganzhi-set').value),
                monthGanZhiSet: parseInt(document.getElementById('bazi-month-ganzhi-set').value),
                dayGanZhiSet: parseInt(document.getElementById('bazi-day-ganzhi-set').value),
                hourGanZhiSet: parseInt(document.getElementById('bazi-hour-ganzhi-set').value),
                renYuan: parseInt(document.getElementById('bazi-renyuan').value)
            };

            const errors = validateForm(data, 'bazi');
            if (errors.length > 0) {
                document.getElementById('bazi-result').innerHTML = 
                    '<div class="error">' + errors.join('<br>') + '</div>';
                return;
            }

            try {
                const response = await fetch('/api/divination/bazi', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                
                if (result.error) {
                    throw new Error(result.error);
                }
                const baziData = result.bazi;

                // 构建显示结果
                let displayText = '';
                displayText += `姓名：${baziData.name}\n`;
                displayText += `性别：${baziData.sex}（${baziData.zao}）\n`;
                displayText += `占事：${baziData.occupy}\n`;
                displayText += `地区：${baziData.address}\n`;
                displayText += `公历：${baziData.solarStr}\n`;
                displayText += `农历：${baziData.lunarStr}\n`;
                displayText += `星期：${baziData.week}\n`;
                displayText += `季节：${baziData.season}\n`;
                displayText += `生肖：${baziData.zodiac}\n`;
                displayText += `星座：${baziData.constellation}\n\n`;

                displayText += `八字：${baziData.baZi.join('')}\n`;
                displayText += `八字五行：${baziData.baZiWuXing.join('')}\n`;
                displayText += `八字纳音：${baziData.baZiNaYin.join('')}\n`;
                displayText += `八字旬空：${baziData.baZiXunKong.join('')}\n\n`;

                displayText += `年柱神煞：${baziData.yearGanZhiShenSha}\n`;
                displayText += `月柱神煞：${baziData.monthGanZhiShenSha}\n`;
                displayText += `日柱神煞：${baziData.dayGanZhiShenSha}\n`;
                displayText += `时柱神煞：${baziData.hourGanZhiShenSha}\n\n`;

                displayText += `天干留意：${baziData.tianGanLiuYi}\n`;
                displayText += `地支留意：${baziData.diZhiLiuYi}\n\n`;

                displayText += `起运：${baziData.qiYun}\n`;
                displayText += `起运日期：${baziData.qiYunDate}\n`;
                displayText += `人元司令分野：${baziData.renYuan}\n`;
                displayText += `出生节气：${baziData.birthSolarTerms}\n\n`;

                displayText += `命：${baziData.ming}\n`;
                displayText += `命卦：${baziData.mingGua}\n`;
                displayText += `命卦信息：${baziData.mingGuaInfo}\n\n`;

                displayText += `五行缺失：${baziData.baZiWuXingQueShi}\n`;
                displayText += `五行数量：${baziData.baZiWuXingCount}\n`;
                displayText += `五行旺衰：${baziData.wuXingWangShuai}\n\n`;

                displayText += `身体强弱：${baziData.bodyIntensity}\n`;
                displayText += `喜用神：${baziData.xiYongShen}\n`;
                displayText += `喜用神方位：${baziData.xiYongShenFangWei}\n\n`;

                displayText += `日柱论命：${baziData.dayZhuLunMing}\n\n`;
                displayText += `姻缘：${baziData.yinYuan}\n\n`;
                displayText += `五行分析：${baziData.wuXingFenXi}\n`;

                document.getElementById('bazi-result').innerText = displayText;
            } catch (error) {
                document.getElementById('bazi-result').innerHTML = 
                    '<div class="error">计算出错：' + error.message + '</div>';
            }
        }

        async function calculateLiuYao() {
            const data = {
                year: parseInt(document.getElementById('liuyao-year').value),
                month: parseInt(document.getElementById('liuyao-month').value),
                day: parseInt(document.getElementById('liuyao-day').value),
                hour: parseInt(document.getElementById('liuyao-hour').value),
                minute: parseInt(document.getElementById('liuyao-minute').value),
                sex: parseInt(document.getElementById('liuyao-sex').value),
                name: document.getElementById('liuyao-name').value,
                occupy: document.getElementById('liuyao-occupy').value,
                address: document.getElementById('liuyao-address').value,
                dateType: parseInt(document.getElementById('liuyao-date-type').value),
                leapMonth: parseInt(document.getElementById('liuyao-leap-month').value),
                qiGuaMode: parseInt(document.getElementById('liuyao-qigua-mode').value),
                liuYao: parseInt(document.getElementById('liuyao-liuyao').value),
                wuYao: parseInt(document.getElementById('liuyao-wuyao').value),
                siYao: parseInt(document.getElementById('liuyao-siyao').value),
                sanYao: parseInt(document.getElementById('liuyao-sanyao').value),
                erYao: parseInt(document.getElementById('liuyao-eryao').value),
                yiYao: parseInt(document.getElementById('liuyao-yiyao').value),
                yearGanZhiSet: parseInt(document.getElementById('liuyao-year-ganzhi-set').value)
            };

            const errors = validateForm(data, 'liuyao');
            if (errors.length > 0) {
                document.getElementById('liuyao-result').innerHTML = 
                    '<div class="error">' + errors.join('<br>') + '</div>';
                return;
            }

            try {
                console.log('发送请求数据:', data); // 记录发送的数据

                const response = await fetch('/api/divination/liuyao/calculate-only', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                console.log('响应状态:', response.status); // 记录响应状态
                console.log('响应头:', Object.fromEntries(response.headers.entries())); // 记录响应头

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('错误响应内容:', errorText);
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('解析后的JSON数据:', result);

                if (result.error) {
                    throw new Error(result.error);
                }
                const liuyaoData = result.liuyao;

                // 构建显示结果
                let displayText = '';
                displayText += `公历：${liuyaoData.solarStr}\n`;
                displayText += `农历：${liuyaoData.lunarStr}\n`;
                displayText += `星期：${liuyaoData.week}\n`;
                displayText += `八字：${liuyaoData.baZi ? liuyaoData.baZi.join('') : ''}\n`;
                displayText += `八字五行：${liuyaoData.baZiWuXing ? liuyaoData.baZiWuXing.join('') : ''}\n`;
                displayText += `八字纳音：${liuyaoData.baZiNaYin ? liuyaoData.baZiNaYin.join('') : ''}\n`;
                displayText += `八字旬空：${liuyaoData.baZiXunKong ? liuyaoData.baZiXunKong.join('') : ''}\n\n`;

                displayText += `六爻爻象YAS：\n${liuyaoData.liuYaoAs ? liuyaoData.liuYaoAs.map(symbol => `<span class="gua-symbol">${symbol}</span>`).join('\n') : ''}\n`;
                displayText += `六爻爻象标识MARK：${liuyaoData.liuYaoYaoXiangMark ? liuyaoData.liuYaoYaoXiangMark.join(', ') : ''}\n`;
                displayText += `六爻爻象标识名称MARKNAME：${liuyaoData.liuYaoYaoXiangMarkName || ''}\n\n`;

                displayText += `上卦：${liuyaoData.shangGua || ''}\n`;
                displayText += `上卦卦象：${liuyaoData.shangGuaAs ? `<span class="gua-symbol">${liuyaoData.shangGuaAs}</span>` : ''}\n`;
                displayText += `下卦：${liuyaoData.xiaGua || ''}\n`;
                displayText += `下卦卦象：${liuyaoData.xiaGuaAs ? `<span class="gua-symbol">${liuyaoData.xiaGuaAs}</span>` : ''}\n\n`;

                displayText += `本卦：${liuyaoData.benGua || ''}\n`;
                displayText += `本卦旺衰百分比：${liuyaoData.liuYaoWangShuaiPercent ? liuyaoData.liuYaoWangShuaiPercent.join(', ') : ''}\n`;
                displayText += `测试：${liuyaoData.benguainformation || ''}\n`;
                displayText += `测2：${liuyaoData.duangua || ''}\n`;
                displayText += `世应：${liuyaoData.benGuaLiuYaoShiYing || ''}\n`;
                displayText += `六兽：${liuyaoData.benGuaLiuYaoLiuShen || ''}\n`;
                displayText += `六亲：${liuyaoData.benGuaLiuYaoLiuQin || ''}\n`;
                displayText += `藏爻六亲：${liuyaoData.benGuaCangYaoLiuQin || ''}\n`;
                displayText += `藏爻干支：${liuyaoData.benGuaCangYaoGanZhi || ''}\n`;
                displayText += `五行：${liuyaoData.benGuaLiuYaoWuXing || ''}\n`;
                displayText += `本卦卦象：${liuyaoData.benGuaAs ? `<span class="bengua-symbol">${liuyaoData.benGuaAs}</span>` : ''}\n`;
                displayText += `本卦卦辞：${liuyaoData.benGuaGuaCi || ''}\n`;
                displayText += `本卦爻名：${liuyaoData.benGuaLiuYaoName ? liuyaoData.benGuaLiuYaoName.join(', ') : ''}\n`;
                displayText += `本卦爻象：\n${liuyaoData.benGuaLiuYaoAs ? liuyaoData.benGuaLiuYaoAs.map(symbol => `<span class="gua-symbol">${symbol}</span>`).join('\n') : ''}\n`;

                document.getElementById('liuyao-result').innerHTML = displayText;
                // 清除之前的AI分析结果（如果存在）
                const existingAiSection = document.getElementById('ai-analysis-section');
                if (existingAiSection) {
                    existingAiSection.remove();
                }
            } catch (error) {
                console.error('请求处理错误:', error);
                document.getElementById('liuyao-result').innerHTML = 
                    '<div class="error">计算出错：' + error.message + '</div>';
            }
        }

        async function calculateTieBan() {
            const resultContainer = document.getElementById('tieban-result');
            try {
                 const data = {
                    year: parseInt(document.getElementById('tieban-year').value),
                    month: parseInt(document.getElementById('tieban-month').value),
                    day: parseInt(document.getElementById('tieban-day').value),
                    hour: parseInt(document.getElementById('tieban-hour').value),
                    minute: parseInt(document.getElementById('tieban-minute').value),
                    second: parseInt(document.getElementById('tieban-second').value),
                    dateType: parseInt(document.getElementById('tieban-date-type').value)
                };

                // Simple validation
                for(const key in data) {
                    if(isNaN(data[key])) {
                         throw new Error(`输入无效: ${key} 不能为空或非数字`);
                    }
                }
                
                resultContainer.innerText = '正在计算...';

                const response = await fetch('/api/divination/tieban', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error(`HTTP 错误! 状态: ${response.status}`);
                }
                
                const result = await response.json();

                if (result.error) {
                    throw new Error(result.error);
                }

                const yearGan = result.yearGan;
                const yearZhi = result.yearZhi;
                const monthGan = result.monthGan;
                const monthZhi = result.monthZhi;
                const dayGan = result.dayGan;
                const dayZhi = result.dayZhi;
                const hourGan = result.hourGan;
                const hourZhi = result.hourZhi;

                let tableHtml = `
                    <h3>四柱详情</h3>
                    <table border="1" style="width:100%; border-collapse: collapse; text-align: center;">
                        <thead>
                            <tr>
                                <th>柱</th>
                                <th>干支</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                const pillars = ['年', '月', '日', '时'];
                const gan = [yearGan, monthGan, dayGan, hourGan];
                const zhi = [yearZhi, monthZhi, dayZhi, hourZhi];

                for (let i = 0; i < 4; i++) {
                    tableHtml += `
                        <tr>
                            <td>${pillars[i]}柱</td>
                            <td>${gan[i] || ''}${zhi[i] || ''}</td>
                        </tr>
                    `;
                }
                tableHtml += `</tbody></table>`;

                // 八卦加则计算法
                tableHtml += `
                    <h3 style="margin-top: 20px;">八卦加则计算法</h3>
                    <table border="1" style="width:100%; border-collapse: collapse; text-align: center;">
                        <thead>
                            <tr>
                                <th>柱</th>
                                <th>上卦</th>
                                <th>下卦</th>
                                <th>卦象</th>
                                <th>卦爻干支</th>
                                <th>六爻和数</th>
                                <th>条文数</th>
                                <th>断语年龄</th>
                                <th>断语批注</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                for (let i = 0; i < 4; i++) {
                    const shangGua = result.shangGuaNames[i];
                    const xiaGua = result.xiaGuaNames[i];
                    const sum = result.sums[i];
                    const fsum = result.fsums[i];
                    const age = result.ages[i];
                    const comment = result.comments[i];
                    const guaSymbol = result[['niangua', 'yuegua', 'rigua', 'shigua'][i]];
                    const liuYaoGanZhi = result[['nianzhuganzhi', 'yuezhuganzhi', 'rizhuganzhi', 'shizhuganzhi'][i]];

                    tableHtml += `
                        <tr>
                            <td>${pillars[i]}柱</td>
                            <td>${shangGua || 'N/A'}</td>
                            <td>${xiaGua || 'N/A'}</td>
                            <td class="gua-symbol">${guaSymbol || 'N/A'}</td>
                            <td>${(liuYaoGanZhi || []).join(', ') || 'N/A'}</td>
                            <td>${sum !== -1 ? sum : 'N/A'}</td>
                            <td>${fsum !== -1 ? fsum : 'N/A'}</td>
                            <td>${age || 'N/A'}</td>
                            <td>${comment || 'N/A'}</td>
                        </tr>
                    `;
                }
                tableHtml += `</tbody></table>`;

                 // 太玄数法
                tableHtml += `
                    <h3 style="margin-top: 20px;">太玄数法</h3>
                    <table border="1" style="width:100%; border-collapse: collapse; text-align: center;">
                        <thead>
                            <tr>
                                <th>柱</th>
                                <th>太玄条目</th>
                                <th>太玄年龄</th>
                                <th>太玄批注</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                for (let i = 0; i < 4; i++) {
                     tableHtml += `
                        <tr>
                           <td>${pillars[i]}柱</td>
                           <td>${result.taiXuanSums[i] !== -1 ? result.taiXuanSums[i] : 'N/A'}</td>
                           <td>${result.taiXuanAges[i] || 'N/A'}</td>
                           <td>${result.taiXuanComments[i] || 'N/A'}</td>
                        </tr>
                    `;
                }
                tableHtml += `</tbody></table>`;


                // 天干数断法
                tableHtml += `
                    <h3 style="margin-top: 20px;">天干数断法</h3>
                    <table border="1" style="width:100%; border-collapse: collapse; text-align: center;">
                        <thead>
                            <tr>
                                <th>条目</th>
                                <th>天干数条目</th>
                                <th>年龄</th>
                                <th>批注</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                for (let i = 0; i < 4; i++) {
                     tableHtml += `
                        <tr>
                           <td>条目 ${i+1}</td>
                           <td>${result.tianGanShuEntryNumbers[i] !== -1 ? result.tianGanShuEntryNumbers[i] : 'N/A'}</td>
                           <td>${result.tianGanShuAges[i] || 'N/A'}</td>
                           <td>${result.tianGanShuComments[i] || 'N/A'}</td>
                        </tr>
                    `;
                }
                tableHtml += `</tbody></table>`;


                resultContainer.innerHTML = tableHtml;

            } catch (error) {
                console.error('铁板神数计算错误:', error);
                resultContainer.innerHTML = 
                    '<div class="error">计算出错：' + error.message + '</div>';
            }
        }

        // 六爻AI快速分析
        async function calculateLiuYaoWithFastAI() {
            const resultContainer = document.getElementById('liuyao-result');

            try {
                // 清除之前的AI分析结果（如果存在）
                const existingAiSection = document.getElementById('ai-analysis-section');
                if (existingAiSection) {
                    existingAiSection.remove();
                }

                // 在现有结果后追加AI分析状态
                const currentContent = resultContainer.innerHTML;
                resultContainer.innerHTML = currentContent + '<div id="ai-analysis-section" style="margin-top: 20px; border-left: 4px solid #FF9800; padding-left: 15px;"><div style="color: #FF9800; font-weight: bold;">⚡ AI正在快速分析中，请稍候...</div></div>';

                // 获取表单数据
                const data = {
                    year: parseInt(document.getElementById('liuyao-year').value),
                    month: parseInt(document.getElementById('liuyao-month').value),
                    day: parseInt(document.getElementById('liuyao-day').value),
                    hour: parseInt(document.getElementById('liuyao-hour').value),
                    minute: parseInt(document.getElementById('liuyao-minute').value),
                    sex: parseInt(document.getElementById('liuyao-sex').value),
                    name: document.getElementById('liuyao-name').value,
                    occupy: document.getElementById('liuyao-occupy').value,
                    address: document.getElementById('liuyao-address').value,
                    dateType: parseInt(document.getElementById('liuyao-date-type').value),
                    leapMonth: parseInt(document.getElementById('liuyao-leap-month').value),
                    qiGuaMode: parseInt(document.getElementById('liuyao-qigua-mode').value),
                    liuYao: parseInt(document.getElementById('liuyao-liuyao').value),
                    wuYao: parseInt(document.getElementById('liuyao-wuyao').value),
                    siYao: parseInt(document.getElementById('liuyao-siyao').value),
                    sanYao: parseInt(document.getElementById('liuyao-sanyao').value),
                    erYao: parseInt(document.getElementById('liuyao-eryao').value),
                    yiYao: parseInt(document.getElementById('liuyao-yiyao').value),
                    yearGanZhiSet: parseInt(document.getElementById('liuyao-year-ganzhi-set').value)
                };

                // 验证必填字段（注意：时和分可以为0）
                if (!data.year || !data.month || !data.day || isNaN(data.hour) || isNaN(data.minute)) {
                    throw new Error('请填写完整的日期时间信息');
                }

                // 发送请求到快速AI分析接口
                const response = await fetch('/api/divination/liuyao/fast-ai', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    // 更新AI分析结果
                    let displayHtml = '<h3 style="color: #FF9800;">⚡ AI快速分析结果</h3>';
                    displayHtml += '<div style="background-color: #fff3e0; padding: 15px; border-radius: 5px; line-height: 1.6;">';
                    displayHtml += result.aiAnalysis.replace(/\n/g, '<br>');
                    displayHtml += '</div>';
                    displayHtml += '<p style="color: #666; font-size: 12px; margin-top: 10px;">快速分析时间: ' + new Date().toLocaleString() + '</p>';

                    // 更新AI分析区域的内容
                    const aiSection = document.getElementById('ai-analysis-section');
                    if (aiSection) {
                        aiSection.innerHTML = displayHtml;
                    }
                } else {
                    const aiSection = document.getElementById('ai-analysis-section');
                    if (aiSection) {
                        aiSection.innerHTML = '<div style="color: red;">快速AI分析失败: ' + (result.error || '未知错误') + '</div>';
                    }
                }

            } catch (error) {
                const aiSection = document.getElementById('ai-analysis-section');
                if (aiSection) {
                    aiSection.innerHTML = '<div style="color: red;">快速AI分析出错: ' + error.message + '</div>';
                }
            }
        }

        // 六爻AI深度分析（流式显示）
        async function calculateLiuYaoWithAI() {
            const resultContainer = document.getElementById('liuyao-result');

            try {
                // 清除之前的AI分析结果（如果存在）
                const existingAiSection = document.getElementById('ai-analysis-section');
                if (existingAiSection) {
                    existingAiSection.remove();
                }

                // 在现有结果后追加AI分析区域
                const currentContent = resultContainer.innerHTML;
                resultContainer.innerHTML = currentContent + `
                    <div id="ai-analysis-section" style="margin-top: 20px; border-left: 4px solid #4CAF50; padding-left: 15px;">
                        <h3 style="color: #4CAF50;">🤖 AI深度分析</h3>
                        <div id="ai-status" style="color: #4CAF50; font-weight: bold; margin-bottom: 10px;">正在初始化...</div>
                        <div id="ai-content" style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; line-height: 1.6; min-height: 50px; white-space: pre-wrap;"></div>
                        <div id="ai-timestamp" style="margin-top: 10px; font-size: 12px; color: #666;"></div>
                    </div>
                `;

                // 收集表单数据
                const data = {
                    year: parseInt(document.getElementById('liuyao-year').value),
                    month: parseInt(document.getElementById('liuyao-month').value),
                    day: parseInt(document.getElementById('liuyao-day').value),
                    hour: parseInt(document.getElementById('liuyao-hour').value),
                    minute: parseInt(document.getElementById('liuyao-minute').value),
                    sex: parseInt(document.getElementById('liuyao-sex').value),
                    name: document.getElementById('liuyao-name').value,
                    occupy: document.getElementById('liuyao-occupy').value,
                    address: document.getElementById('liuyao-address').value,
                    dateType: parseInt(document.getElementById('liuyao-date-type').value),
                    leapMonth: parseInt(document.getElementById('liuyao-leap-month').value),
                    qiGuaMode: parseInt(document.getElementById('liuyao-qigua-mode').value),
                    liuYao: parseInt(document.getElementById('liuyao-liuyao').value),
                    wuYao: parseInt(document.getElementById('liuyao-wuyao').value),
                    siYao: parseInt(document.getElementById('liuyao-siyao').value),
                    sanYao: parseInt(document.getElementById('liuyao-sanyao').value),
                    erYao: parseInt(document.getElementById('liuyao-eryao').value),
                    yiYao: parseInt(document.getElementById('liuyao-yiyao').value),
                    yearGanZhiSet: parseInt(document.getElementById('liuyao-year-ganzhi-set').value)
                };

                // 验证必填字段
                if (!data.year || !data.month || !data.day || data.hour === undefined || data.hour === null || data.minute === undefined || data.minute === null) {
                    throw new Error('请填写完整的日期时间信息');
                }

                // 启动流式AI分析
                await startStreamingAIAnalysis(data);

            } catch (error) {
                console.error('AI分析错误:', error);
                const aiSection = document.getElementById('ai-analysis-section');
                if (aiSection) {
                    aiSection.innerHTML = '<div class="error">🤖 AI分析出错：' + error.message + '</div>';
                }
            }
        }

        // 流式AI分析函数
        async function startStreamingAIAnalysis(data) {
            const statusElement = document.getElementById('ai-status');
            const contentElement = document.getElementById('ai-content');
            const timestampElement = document.getElementById('ai-timestamp');

            console.log('开始流式AI分析，发送数据:', data);

            try {
                const response = await fetch('/api/divination/liuyao/optimized-streaming-ai', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify(data)
                });

                console.log('收到响应，状态:', response.status, '类型:', response.headers.get('content-type'));

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let chunkCount = 0;

                console.log('开始读取流式数据...');

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) {
                        console.log('流式数据读取完成，总共处理了', chunkCount, '个数据块');
                        break;
                    }

                    chunkCount++;
                    const chunk = decoder.decode(value, { stream: true });
                    console.log(`收到数据块 ${chunkCount}:`, chunk.substring(0, 100) + (chunk.length > 100 ? '...' : ''));

                    buffer += chunk;
                    const lines = buffer.split('\n');
                    buffer = lines.pop(); // 保留不完整的行

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                console.log('解析成功，收到流式数据:', data.type, data);
                                handleStreamingData(data, statusElement, contentElement, timestampElement);
                            } catch (e) {
                                console.warn('解析SSE数据失败:', line, e);
                            }
                        } else if (line.trim()) {
                            console.log('收到非data行:', line);
                        }
                    }
                }

            } catch (error) {
                statusElement.textContent = '❌ 连接失败';
                contentElement.textContent = '无法连接到AI服务：' + error.message;
                timestampElement.textContent = '错误时间: ' + new Date().toLocaleString();
            }
        }

        // 处理流式数据
        function handleStreamingData(data, statusElement, contentElement, timestampElement) {
            switch (data.type) {
                case 'start':
                    statusElement.textContent = data.message;
                    contentElement.textContent = '';
                    timestampElement.textContent = '开始时间: ' + new Date().toLocaleString();
                    break;
                case 'progress':
                    statusElement.textContent = data.message;
                    break;
                case 'basic_info':
                    // 显示基础六爻信息
                    console.log('收到基础信息:', data.data);
                    console.log('基础信息包含的字段:', Object.keys(data.data || {}));
                    if (data.data && data.data.benGua) {
                        console.log('本卦:', data.data.benGua);
                        console.log('变卦:', data.data.bianGua);
                        console.log('卦辞:', data.data.benGuaGuaCi);
                    }
                    displayBasicInfo(data.data);
                    break;
                case 'ai_start':
                    statusElement.textContent = data.message;
                    break;
                case 'queue':
                    statusElement.textContent = data.message;
                    break;
                case 'processing':
                    statusElement.textContent = data.message;
                    break;
                case 'chunk':
                    contentElement.textContent += data.content;
                    // 自动滚动到底部
                    contentElement.scrollTop = contentElement.scrollHeight;
                    break;
                case 'complete':
                    statusElement.textContent = data.message;
                    timestampElement.textContent = '完成时间: ' + new Date().toLocaleString();
                    break;
                case 'error':
                    statusElement.textContent = data.message;
                    contentElement.textContent = '分析过程中出现错误，请重试。';
                    timestampElement.textContent = '错误时间: ' + new Date().toLocaleString();
                    break;
            }
        }

        // 显示基础六爻信息
        function displayBasicInfo(liuYaoData) {
            console.log('displayBasicInfo被调用，数据:', liuYaoData);

            // 检查数据是否存在
            if (!liuYaoData) {
                console.error('liuYaoData为空');
                return;
            }

            // 尝试多个可能的结果容器ID
            let resultDiv = document.getElementById('liuyao-result') ||
                           document.getElementById('result') ||
                           document.querySelector('.result-container');

            if (!resultDiv) {
                console.error('找不到结果容器元素，尝试的ID: liuyao-result, result');
                // 创建一个临时容器
                resultDiv = document.createElement('div');
                resultDiv.id = 'temp-result-container';
                document.body.appendChild(resultDiv);
                console.log('创建了临时结果容器');
            } else {
                console.log('找到结果容器:', resultDiv.id || resultDiv.className);
            }

            // 移除之前的基础信息（如果有）
            const existingBasicInfo = document.getElementById('basic-info-section');
            if (existingBasicInfo) {
                existingBasicInfo.remove();
            }

            // 创建基础信息区域
            const basicInfoSection = document.createElement('div');
            basicInfoSection.id = 'basic-info-section';
            basicInfoSection.style.cssText = `
                margin-top: 20px;
                border-left: 4px solid #4CAF50;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 5px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            `;

            // 简化显示，确保快速渲染
            basicInfoSection.innerHTML = `
                <h3 style="color: #4CAF50; margin-top: 0;">📊 六爻基础信息</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <h4>🎯 卦象信息</h4>
                        <p><strong>本卦：</strong>${liuYaoData.benGua || '计算中...'}</p>
                        <p><strong>变卦：</strong>${liuYaoData.bianGua || '计算中...'}</p>
                        <p><strong>互卦：</strong>${liuYaoData.huGua || '计算中...'}</p>
                        <p><strong>错卦：</strong>${liuYaoData.cuoGua || '计算中...'}</p>
                        <p><strong>综卦：</strong>${liuYaoData.zongGua || '计算中...'}</p>
                    </div>
                    <div>
                        <h4>📜 卦辞</h4>
                        <div style="background: #fff; padding: 10px; border-radius: 3px; font-style: italic;">
                            ${liuYaoData.benGuaGuaCi || '正在计算卦辞...'}
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px;">
                    <h4>🔮 六爻详情</h4>
                    <div style="background: #fff; padding: 15px; border-radius: 3px;">
                        <p><strong>六爻排列：</strong>${generateSimpleLiuYaoDisplay(liuYaoData)}</p>
                        <p><strong>动爻情况：</strong>${generateDongYaoInfo(liuYaoData)}</p>
                        <p><strong>世应位置：</strong>${generateShiYingInfo(liuYaoData)}</p>
                    </div>
                </div>

                <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 5px;">
                    <p style="margin: 0; color: #2e7d32; font-weight: bold;">✨ 基础信息已显示，AI正在深度分析中...</p>
                </div>
            `;

            // 插入到AI分析区域之前
            const aiSection = document.getElementById('ai-analysis-section');
            if (aiSection) {
                // 如果AI分析区域存在，插入到它之前
                resultDiv.insertBefore(basicInfoSection, aiSection);
                console.log('基础信息插入到AI分析区域之前');
            } else {
                // 如果AI分析区域不存在，直接添加到结果区域
                resultDiv.appendChild(basicInfoSection);
                console.log('基础信息添加到结果区域末尾');
            }

            // 滚动到基础信息区域
            basicInfoSection.scrollIntoView({ behavior: 'smooth', block: 'start' });

            console.log('基础信息显示完成');
        }

        // 简化的六爻显示
        function generateSimpleLiuYaoDisplay(liuYaoData) {
            if (!liuYaoData.liuYaoAs || !Array.isArray(liuYaoData.liuYaoAs)) {
                return '正在计算...';
            }
            // 从上爻到初爻显示（数组是从初爻到上爻，所以要reverse）
            const yaoSymbols = [...liuYaoData.liuYaoAs].reverse();
            return yaoSymbols.join(' ');
        }

        // 动爻信息
        function generateDongYaoInfo(liuYaoData) {
            if (!liuYaoData.liuYaoYaoXiangMarkName || !Array.isArray(liuYaoData.liuYaoYaoXiangMarkName)) {
                return '正在计算...';
            }
            const dongYao = liuYaoData.liuYaoYaoXiangMarkName.filter(mark => mark && mark.trim() !== '' && mark !== ' ');
            return dongYao.length > 0 ? `有${dongYao.length}个动爻` : '无动爻';
        }

        // 世应信息
        function generateShiYingInfo(liuYaoData) {
            if (!liuYaoData.benGuaLiuYaoShiYing || !Array.isArray(liuYaoData.benGuaLiuYaoShiYing)) {
                return '正在计算...';
            }
            const shiIndex = liuYaoData.benGuaLiuYaoShiYing.findIndex(item => item === '世');
            const yingIndex = liuYaoData.benGuaLiuYaoShiYing.findIndex(item => item === '应');
            const yaoNames = ['初爻', '二爻', '三爻', '四爻', '五爻', '上爻'];

            let result = '';
            if (shiIndex >= 0) result += `世在${yaoNames[shiIndex]}`;
            if (yingIndex >= 0) result += (result ? '，' : '') + `应在${yaoNames[yingIndex]}`;
            return result || '正在计算...';
        }

        // 删除复杂的表格生成，使用简化显示

        // 测试基础信息显示功能
        function testBasicInfoDisplay() {
            console.log('测试基础信息显示功能');
            const testData = {
                benGua: "乾为天",
                bianGua: "天风姤",
                huGua: "乾为天",
                cuoGua: "坤为地",
                zongGua: "坤为地",
                benGuaGuaCi: "乾：元，亨，利，贞。",
                liuYaoAs: ["—", "—", "—", "—", "—", "—"],
                liuYaoYaoXiangMarkName: ["", "", "", "", "", ""],
                benGuaLiuYaoShiYing: ["", "", "应", "", "", "世"]
            };
            displayBasicInfo(testData);
        }

        // 添加测试按钮（临时）
        window.testBasicInfo = testBasicInfoDisplay;

        // 设置默认值
        window.onload = function() {
            const now = new Date();
            
            // 设置八字表单默认值
            document.getElementById('bazi-year').value = now.getFullYear();
            document.getElementById('bazi-month').value = now.getMonth() + 1;
            document.getElementById('bazi-day').value = now.getDate();
            document.getElementById('bazi-hour').value = now.getHours();
            document.getElementById('bazi-minute').value = now.getMinutes();
            
            // 设置六爻表单默认值
            document.getElementById('liuyao-year').value = now.getFullYear();
            document.getElementById('liuyao-month').value = now.getMonth() + 1;
            document.getElementById('liuyao-day').value = now.getDate();
            document.getElementById('liuyao-hour').value = now.getHours();
            document.getElementById('liuyao-minute').value = now.getMinutes();

            // 设置铁板神数表单默认值
            document.getElementById('tieban-year').value = now.getFullYear();
            document.getElementById('tieban-month').value = now.getMonth() + 1;
            document.getElementById('tieban-day').value = now.getDate();
            document.getElementById('tieban-hour').value = now.getHours();
            document.getElementById('tieban-minute').value = now.getMinutes();
        };
    </script>
</body>
</html> 