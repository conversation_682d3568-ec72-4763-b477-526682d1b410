package com.shandai.xuan.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shandai.xuan.util.PerformanceMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.ResourceAccessException;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

@Service
public class AIService {

    private static final Logger logger = LoggerFactory.getLogger(AIService.class);

    private final String apiKey;
    private final String apiUrl;
    private final String defaultModel;
    private final double temperature;
    private final int maxTokens;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Autowired
    private PerformanceMonitor performanceMonitor;

    @Autowired
    private ModelUsageMonitorService usageMonitorService;

    @Autowired
    private ModelSwitchService modelSwitchService;

    public AIService(@Value("${gemini.api.key}") String apiKey,
                     @Value("${gemini.api.url}") String apiUrl,
                     @Value("${ai.analysis.default-model}") String defaultModel,
                     @Value("${ai.analysis.temperature}") double temperature,
                     @Value("${ai.analysis.max-tokens}") int maxTokens,
                     RestTemplate restTemplate) {
        this.apiKey = apiKey;
        this.apiUrl = apiUrl;
        this.defaultModel = defaultModel;
        this.temperature = temperature;
        this.maxTokens = maxTokens;
        this.restTemplate = restTemplate;
        this.objectMapper = new ObjectMapper();
    }

    public String getAnalysis(String prompt) {
        return getAnalysisWithRetry(prompt, 3);
    }

    /**
     * 使用指定模型进行分析
     */
    public String getAnalysisWithModel(String prompt, String modelName) {
        return getAnalysisWithRetryAndModel(prompt, 3, modelName);
    }

    private String getAnalysisWithRetry(String prompt, int maxRetries) {
        // 使用当前活跃模型
        String currentModel = usageMonitorService.getCurrentActiveModel();
        return getAnalysisWithRetryAndModel(prompt, maxRetries, currentModel);
    }

    private String getAnalysisWithRetryAndModel(String prompt, int maxRetries, String modelName) {
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.info("AI分析尝试 {}/{} - 使用模型: {}", attempt, maxRetries, modelName);
                String result = performAnalysisWithModel(prompt, modelName);

                // 检查结果是否有效
                if (result != null && !result.trim().isEmpty() &&
                    !result.contains("内容为空") && !result.contains("被截断")) {
                    // 记录成功的使用
                    usageMonitorService.recordUsage(modelName);
                    return result;
                }

                if (attempt < maxRetries) {
                    logger.warn("第{}次尝试结果无效，准备重试: {}", attempt, result);
                    Thread.sleep(2000); // 等待2秒后重试
                }
            } catch (Exception e) {
                logger.error("第{}次AI分析尝试失败 - 模型: {}", attempt, modelName, e);

                // 如果是限流错误，尝试切换模型
                if (isRateLimitError(e) && attempt < maxRetries) {
                    String nextModel = modelSwitchService.getNextRecommendedModel();
                    if (nextModel != null && !nextModel.equals(modelName)) {
                        logger.info("检测到限流错误，尝试切换到模型: {}", nextModel);
                        modelSwitchService.manualSwitchToModel(nextModel);
                        modelName = nextModel; // 使用新模型重试
                    }
                }

                if (attempt == maxRetries) {
                    return "AI分析失败，已重试" + maxRetries + "次：" + e.getMessage();
                }
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return "AI分析被中断";
                }
            }
        }

        return "AI分析失败：所有重试都未成功，可能是中转站问题。建议稍后重试或联系管理员。";
    }

    /**
     * 检查是否是限流错误
     */
    private boolean isRateLimitError(Exception e) {
        String message = e.getMessage();
        if (message == null) return false;

        return message.contains("rate limit") ||
               message.contains("quota exceeded") ||
               message.contains("429") ||
               message.contains("too many requests");
    }

    private String performAnalysis(String prompt) {
        // 使用当前活跃模型
        String currentModel = usageMonitorService.getCurrentActiveModel();
        return performAnalysisWithModel(prompt, currentModel);
    }

    private String performAnalysisWithModel(String prompt, String modelName) {
        try {
            // 构建请求体
            performanceMonitor.startStep("请求构建");
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", modelName);
            requestBody.put("temperature", temperature);

            // 如果maxTokens设置得很高（>16384），则不设置限制，让模型自由发挥
            if (maxTokens <= 16384) {
                requestBody.put("max_tokens", maxTokens);
                logger.info("设置token限制: {}", maxTokens);
            } else {
                logger.info("不设置token限制，让模型自由生成");
                // 不设置max_tokens参数，让API使用默认值
            }

            requestBody.put("top_p", 0.9);
            requestBody.put("stream", false); // 确保不使用流式响应

            // 构建消息列表
            List<Map<String, String>> messages = new ArrayList<>();
            Map<String, String> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", prompt);
            messages.add(message);
            requestBody.put("messages", messages);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + apiKey);
            headers.set("Content-Type", "application/json");

            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            performanceMonitor.endStep("请求构建");

            logger.info("开始调用AI API，模型：{}，最大token：{}", modelName, requestBody.get("max_tokens"));

            // 发送请求
            performanceMonitor.startStep("网络请求");
            ResponseEntity<String> response = restTemplate.exchange(
                apiUrl,
                HttpMethod.POST,
                requestEntity,
                String.class
            );
            long networkTime = performanceMonitor.endStep("网络请求");

            logger.info("AI API网络请求完成，耗时：{}ms", networkTime);

            // 解析响应
            performanceMonitor.startStep("响应解析");
            String responseBody = response.getBody();
            logger.debug("AI API原始响应: {}", responseBody);
            JsonNode responseJson = objectMapper.readTree(responseBody);
            JsonNode choices = responseJson.get("choices");
            if (choices != null && choices.size() > 0) {
                JsonNode firstChoice = choices.get(0);
                JsonNode responseMessage = firstChoice.get("message");
                if (responseMessage != null) {
                    JsonNode content = responseMessage.get("content");
                    if (content != null && !content.isNull()) {
                        String contentText = content.asText();
                        if (contentText != null && !contentText.trim().isEmpty()) {
                            performanceMonitor.endStep("响应解析");
                            return contentText;
                        }
                    }

                    // 检查是否有finish_reason为length的情况（内容被截断）
                    JsonNode finishReason = firstChoice.get("finish_reason");
                    if (finishReason != null && "length".equals(finishReason.asText())) {
                        performanceMonitor.endStep("响应解析");
                        return "AI分析内容过长被截断，请尝试简化问题或增加max_tokens设置。";
                    }

                    // 检查是否有其他finish_reason
                    if (finishReason != null) {
                        String reason = finishReason.asText();
                        performanceMonitor.endStep("响应解析");
                        return "AI分析完成，但内容为空。结束原因：" + reason + "。请尝试调整提示词或参数。";
                    }
                }
            }

            performanceMonitor.failStep("响应解析", "响应格式不正确或内容为空");
            return "AI分析时出现错误：响应格式不正确或内容为空。请检查API配置和网络连接。";

        } catch (ResourceAccessException e) {
            // 网络超时或连接问题
            performanceMonitor.failStep("网络请求", "网络超时或连接问题: " + e.getMessage());
            logger.error("AI API调用超时或网络错误", e);
            String errorMsg = e.getMessage();
            if (errorMsg != null && errorMsg.contains("Read timed out")) {
                return "AI分析超时，模型正在处理复杂的六爻分析，请稍后重试。建议简化占事描述或稍后再试。";
            } else {
                return "网络连接错误，请检查网络连接后重试：" + (errorMsg != null ? errorMsg : "未知网络错误");
            }
        } catch (Exception e) {
            // 其他异常
            performanceMonitor.failStep("AI调用", "未知错误: " + e.getMessage());
            logger.error("AI分析时出现未知错误", e);
            return "AI分析时出现错误：" + e.getMessage();
        }
    }
} 