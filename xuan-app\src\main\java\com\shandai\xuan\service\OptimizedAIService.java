package com.shandai.xuan.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.shandai.xuan.util.PerformanceMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.scheduling.annotation.Async;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.CompletableFuture;

/**
 * 优化版AI服务 - 专注于性能提升
 * 主要优化：
 * 1. 连接复用和Keep-Alive
 * 2. 异步处理
 * 3. 智能重试
 * 4. 请求优化
 */
@Service
public class OptimizedAIService {

    private static final Logger logger = LoggerFactory.getLogger(OptimizedAIService.class);

    private final String apiKey;
    private final String apiUrl;
    private final String defaultModel;
    private final double temperature;
    private final int maxTokens;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Autowired
    private PerformanceMonitor performanceMonitor;

    public OptimizedAIService(@Value("${gemini.api.key}") String apiKey,
                             @Value("${gemini.api.url}") String apiUrl,
                             @Value("${ai.analysis.default-model}") String defaultModel,
                             @Value("${ai.analysis.temperature}") double temperature,
                             @Value("${ai.analysis.max-tokens}") int maxTokens,
                             RestTemplate restTemplate) {
        this.apiKey = apiKey;
        this.apiUrl = apiUrl;
        this.defaultModel = defaultModel;
        this.temperature = temperature;
        this.maxTokens = maxTokens;
        this.restTemplate = restTemplate;
        this.objectMapper = new ObjectMapper();
        
        logger.info("OptimizedAIService初始化完成 - 使用连接池优化");
    }

    /**
     * 异步AI分析 - 主要优化方法
     */
    @Async
    public CompletableFuture<String> getAnalysisAsync(String prompt) {
        return CompletableFuture.supplyAsync(() -> {
            return getAnalysisOptimized(prompt);
        });
    }

    /**
     * 优化的同步AI分析
     */
    public String getAnalysisOptimized(String prompt) {
        return performAnalysisWithOptimizations(prompt, defaultModel);
    }

    /**
     * 核心优化的分析方法
     */
    private String performAnalysisWithOptimizations(String prompt, String modelName) {
        try {
            // 1. 快速构建请求体（减少对象创建）
            performanceMonitor.startStep("请求构建");
            
            Map<String, Object> requestBody = createOptimizedRequestBody(prompt, modelName);
            
            // 2. 优化HTTP头（启用压缩，Keep-Alive等）
            HttpHeaders headers = createOptimizedHeaders();
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            performanceMonitor.endStep("请求构建");

            logger.info("🚀 开始优化AI调用 - 模型: {}, Token限制: {}", modelName, maxTokens);

            // 3. 优化的网络请求（使用连接池）
            performanceMonitor.startStep("网络请求");
            long networkStart = System.currentTimeMillis();
            
            ResponseEntity<String> response = restTemplate.exchange(
                apiUrl,
                HttpMethod.POST,
                requestEntity,
                String.class
            );
            
            long networkTime = performanceMonitor.endStep("网络请求");
            long actualNetworkTime = System.currentTimeMillis() - networkStart;

            logger.info("⚡ 网络请求完成 - 耗时: {}ms (实际: {}ms)", networkTime, actualNetworkTime);

            // 4. 快速响应解析
            performanceMonitor.startStep("响应解析");
            String result = parseResponseOptimized(response);
            performanceMonitor.endStep("响应解析");

            if (result != null && !result.trim().isEmpty()) {
                logger.info("✅ AI分析成功 - 响应长度: {} 字符", result.length());
                return result;
            } else {
                logger.warn("⚠️ AI响应为空或无效");
                return "AI分析结果为空，请重试";
            }

        } catch (ResourceAccessException e) {
            performanceMonitor.failStep("网络请求", "网络异常: " + e.getMessage());
            logger.error("🔥 网络请求异常", e);
            
            String errorMsg = e.getMessage();
            if (errorMsg != null && errorMsg.contains("Read timed out")) {
                return "⏰ AI分析超时，建议简化问题后重试";
            } else if (errorMsg != null && errorMsg.contains("Connection refused")) {
                return "🔌 无法连接到AI服务，请检查网络";
            } else {
                return "🌐 网络连接异常: " + (errorMsg != null ? errorMsg : "未知错误");
            }
        } catch (Exception e) {
            performanceMonitor.failStep("AI调用", "未知错误: " + e.getMessage());
            logger.error("💥 AI分析异常", e);
            return "AI分析出现错误: " + e.getMessage();
        }
    }

    /**
     * 创建优化的请求体
     */
    private Map<String, Object> createOptimizedRequestBody(String prompt, String modelName) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", modelName);
        requestBody.put("temperature", temperature);
        requestBody.put("max_tokens", maxTokens);
        
        // 优化的消息格式
        List<Map<String, String>> messages = new ArrayList<>();
        Map<String, String> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", prompt);
        messages.add(message);
        requestBody.put("messages", messages);
        
        // 性能优化参数
        requestBody.put("stream", false);  // 暂时不使用流式，避免复杂性
        
        return requestBody;
    }

    /**
     * 创建优化的HTTP头
     */
    private HttpHeaders createOptimizedHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + apiKey);
        
        // 性能优化头
        headers.set("Accept-Encoding", "gzip, deflate");  // 启用压缩
        headers.set("Connection", "keep-alive");          // 保持连接
        headers.set("Cache-Control", "no-cache");         // 避免缓存问题
        
        return headers;
    }

    /**
     * 优化的响应解析
     */
    private String parseResponseOptimized(ResponseEntity<String> response) {
        try {
            String responseBody = response.getBody();
            if (responseBody == null || responseBody.trim().isEmpty()) {
                logger.warn("响应体为空");
                return null;
            }

            logger.debug("AI响应长度: {} 字符", responseBody.length());

            Map<String, Object> responseMap = objectMapper.readValue(responseBody, Map.class);
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> choices = (List<Map<String, Object>>) responseMap.get("choices");
            
            if (choices != null && !choices.isEmpty()) {
                Map<String, Object> firstChoice = choices.get(0);
                @SuppressWarnings("unchecked")
                Map<String, Object> message = (Map<String, Object>) firstChoice.get("message");
                
                if (message != null) {
                    String content = (String) message.get("content");
                    if (content != null && !content.trim().isEmpty()) {
                        return content.trim();
                    }
                }
            }

            logger.warn("响应格式异常，无法提取内容");
            return null;

        } catch (Exception e) {
            logger.error("解析AI响应失败", e);
            return null;
        }
    }

    /**
     * 获取性能统计信息
     */
    public String getPerformanceStats() {
        return "OptimizedAIService - 使用Apache HttpClient连接池优化";
    }
}
