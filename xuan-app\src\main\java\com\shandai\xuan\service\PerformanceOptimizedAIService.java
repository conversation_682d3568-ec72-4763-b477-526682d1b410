package com.shandai.xuan.service;

import com.shandai.xuan.dto.LiuYaoAiAnalysisRequest;
import com.shandai.xuan.dto.LiuYaoData;
import com.shandai.xuan.util.PerformanceMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PreDestroy;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 性能优化的AI分析服务 - 防御型设计
 * 主要功能：
 * 1. 异步响应 + 优先级队列
 * 2. 用户体验优先
 * 3. 防御型多用户并发设计
 * 4. 资源保护和自动清理
 */
@Service
public class PerformanceOptimizedAIService {

    private static final Logger logger = LoggerFactory.getLogger(PerformanceOptimizedAIService.class);

    @Autowired
    private LiuYaoAiAnalysisService originalService;

    @Autowired
    private PerformanceMonitor performanceMonitor;

    // === 防御型设计：资源限制 ===
    private static final int MAX_CONCURRENT_PROCESSING = 5; // 最大并发处理数（AI调用）
    private static final int MAX_QUEUE_SIZE = 100; // 最大排队数量
    private static final int REQUEST_TIMEOUT_MINUTES = 15; // 请求超时时间
    private static final int CLEANUP_INTERVAL_MINUTES = 5; // 清理间隔
    private static final int QUEUE_WARNING_THRESHOLD = 20; // 排队警告阈值

    // === 线程池：分离用户响应和后台任务 ===
    private final ExecutorService userResponseExecutor = Executors.newFixedThreadPool(5,
        r -> new Thread(r, "UserResponse-" + System.currentTimeMillis()));
    private final ExecutorService backgroundTaskExecutor = Executors.newFixedThreadPool(3,
        r -> new Thread(r, "Background-" + System.currentTimeMillis()));

    // === 防御型设计：并发安全的数据结构 ===
    private final ConcurrentHashMap<String, AsyncAnalysisTask> activeTasks = new ConcurrentHashMap<>();
    private final PriorityBlockingQueue<BackgroundTask> backgroundQueue = new PriorityBlockingQueue<>();
    private final BlockingQueue<AsyncAnalysisTask> waitingQueue = new LinkedBlockingQueue<>(); // 排队队列
    private final AtomicInteger activeProcessingCount = new AtomicInteger(0); // 正在处理的AI请求数
    private final AtomicInteger totalRequestCount = new AtomicInteger(0); // 总请求数（包括排队）
    private final AtomicLong requestIdCounter = new AtomicLong(0);

    // === 防御型设计：任务状态枚举 ===
    public enum TaskStatus {
        QUEUED,           // 排队中
        PROCESSING,       // AI处理中
        AI_COMPLETED,     // AI完成
        USER_NOTIFIED,    // 用户已通知
        BACKGROUND_PROCESSING, // 后台处理中
        COMPLETED,        // 全部完成
        FAILED,           // 失败
        TIMEOUT           // 超时
    }

    // === 防御型设计：任务优先级 ===
    public enum TaskPriority {
        USER_RESPONSE(1), CACHE_STORAGE(2), PERFORMANCE_LOG(3), CLEANUP(4);
        private final int priority;
        TaskPriority(int priority) { this.priority = priority; }
        public int getPriority() { return priority; }
    }

    // === 异步分析任务 ===
    public static class AsyncAnalysisTask {
        private final String requestId;
        private final LiuYaoAiAnalysisRequest request;
        private final LocalDateTime createTime;
        private volatile TaskStatus status;
        private volatile String result;
        private volatile String errorMessage;
        private final CompletableFuture<String> userResponseFuture;

        public AsyncAnalysisTask(String requestId, LiuYaoAiAnalysisRequest request) {
            this.requestId = requestId;
            this.request = request;
            this.createTime = LocalDateTime.now();
            this.status = TaskStatus.QUEUED;
            this.userResponseFuture = new CompletableFuture<>();
        }

        // Getters and setters with thread safety
        public synchronized TaskStatus getStatus() { return status; }
        public synchronized void setStatus(TaskStatus status) {
            this.status = status;
            logger.debug("Task {} status changed to {}", requestId, status);
        }
        public synchronized String getResult() { return result; }
        public synchronized void setResult(String result) { this.result = result; }
        public String getRequestId() { return requestId; }
        public LiuYaoAiAnalysisRequest getRequest() { return request; }
        public LocalDateTime getCreateTime() { return createTime; }
        public CompletableFuture<String> getUserResponseFuture() { return userResponseFuture; }
        public synchronized String getErrorMessage() { return errorMessage; }
        public synchronized void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public boolean isExpired() {
            return createTime.plusMinutes(REQUEST_TIMEOUT_MINUTES).isBefore(LocalDateTime.now());
        }
    }

    // === 后台任务 ===
    public static class BackgroundTask implements Comparable<BackgroundTask> {
        private final String requestId;
        private final TaskPriority priority;
        private final Runnable task;
        private final LocalDateTime createTime;

        public BackgroundTask(String requestId, TaskPriority priority, Runnable task) {
            this.requestId = requestId;
            this.priority = priority;
            this.task = task;
            this.createTime = LocalDateTime.now();
        }

        @Override
        public int compareTo(BackgroundTask other) {
            return Integer.compare(this.priority.getPriority(), other.priority.getPriority());
        }

        public void execute() {
            try {
                task.run();
            } catch (Exception e) {
                logger.error("Background task {} failed", requestId, e);
            }
        }

        public String getRequestId() { return requestId; }
        public TaskPriority getPriority() { return priority; }
        public LocalDateTime getCreateTime() { return createTime; }
    }

    /**
     * 异步AI分析 - 智能排队机制
     */
    public AsyncAnalysisResult executeAnalysisAsync(LiuYaoAiAnalysisRequest request) {
        // 防御型检查：队列大小限制
        if (totalRequestCount.get() >= MAX_QUEUE_SIZE) {
            logger.warn("系统队列已满: {}", MAX_QUEUE_SIZE);
            // 清理过期任务后再次检查
            cleanupExpiredTasks();
            if (totalRequestCount.get() >= MAX_QUEUE_SIZE) {
                throw new RuntimeException("系统繁忙，队列已满，请稍后重试");
            }
        }

        // 生成安全的RequestID
        String requestId = generateSecureRequestId();

        // 创建异步任务
        AsyncAnalysisTask asyncTask = new AsyncAnalysisTask(requestId, request);
        activeTasks.put(requestId, asyncTask);
        totalRequestCount.incrementAndGet();

        // 计算排队信息
        int queuePosition = Math.max(0, totalRequestCount.get() - activeProcessingCount.get() - 1); // 当前任务不算在排队中
        int estimatedWaitMinutes = Math.max(0, queuePosition * 2); // 假设每个任务2分钟

        logger.info("创建异步AI分析任务: {}, 排队位置: {}, 预计等待: {}分钟",
                   requestId, queuePosition, estimatedWaitMinutes);

        // 如果可以立即处理，直接处理
        if (activeProcessingCount.get() < MAX_CONCURRENT_PROCESSING) {
            activeProcessingCount.incrementAndGet();
            asyncTask.setStatus(TaskStatus.PROCESSING);
            userResponseExecutor.submit(() -> processAsyncAnalysis(asyncTask));
        } else {
            // 否则加入排队
            asyncTask.setStatus(TaskStatus.QUEUED);
            waitingQueue.offer(asyncTask);
            // 启动队列处理器
            processWaitingQueue();
        }

        return new AsyncAnalysisResult(requestId, queuePosition, estimatedWaitMinutes, asyncTask.getStatus());
    }

    /**
     * 异步分析结果类
     */
    public static class AsyncAnalysisResult {
        private final String requestId;
        private final int queuePosition;
        private final int estimatedWaitMinutes;
        private final TaskStatus status;

        public AsyncAnalysisResult(String requestId, int queuePosition, int estimatedWaitMinutes, TaskStatus status) {
            this.requestId = requestId;
            this.queuePosition = queuePosition;
            this.estimatedWaitMinutes = estimatedWaitMinutes;
            this.status = status;
        }

        public String getRequestId() { return requestId; }
        public int getQueuePosition() { return queuePosition; }
        public int getEstimatedWaitMinutes() { return estimatedWaitMinutes; }
        public TaskStatus getStatus() { return status; }
    }

    /**
     * 处理排队队列
     */
    private void processWaitingQueue() {
        userResponseExecutor.submit(() -> {
            while (!waitingQueue.isEmpty() && activeProcessingCount.get() < MAX_CONCURRENT_PROCESSING) {
                try {
                    AsyncAnalysisTask task = waitingQueue.poll();
                    if (task != null && !task.isExpired()) {
                        activeProcessingCount.incrementAndGet();
                        task.setStatus(TaskStatus.PROCESSING);
                        logger.info("从队列中取出任务开始处理: {}", task.getRequestId());
                        processAsyncAnalysis(task);
                    }
                } catch (Exception e) {
                    logger.error("处理排队任务异常", e);
                }
            }
        });
    }

    /**
     * 核心异步处理逻辑 - 用户体验优先
     */
    private void processAsyncAnalysis(AsyncAnalysisTask asyncTask) {
        String requestId = asyncTask.getRequestId();

        try {
            asyncTask.setStatus(TaskStatus.PROCESSING);
            logger.info("开始处理异步AI分析: {}", requestId);

            // 执行AI分析（这是耗时操作）
            performanceMonitor.startStep("异步AI调用-" + requestId);
            String aiResult = originalService.executeAnalysis(asyncTask.getRequest());
            long aiDuration = performanceMonitor.endStep("异步AI调用-" + requestId);

            // AI结果一返回，立即设置状态和结果
            asyncTask.setStatus(TaskStatus.AI_COMPLETED);
            asyncTask.setResult(aiResult);

            // 立即完成用户响应Future
            asyncTask.getUserResponseFuture().complete(aiResult);
            asyncTask.setStatus(TaskStatus.USER_NOTIFIED);

            logger.info("AI分析完成，立即通知用户: {}, 耗时: {}ms", requestId, aiDuration);

            // 将后台任务加入队列（缓存、统计等）- 异步执行，不阻塞用户响应
            CompletableFuture.runAsync(() -> queueBackgroundTasks(asyncTask, aiResult, aiDuration), backgroundTaskExecutor);

        } catch (Exception e) {
            logger.error("异步AI分析失败: {}", requestId, e);
            asyncTask.setStatus(TaskStatus.FAILED);
            asyncTask.setErrorMessage(e.getMessage());
            asyncTask.getUserResponseFuture().completeExceptionally(e);
        } finally {
            // 防御型设计：确保计数器正确
            activeProcessingCount.decrementAndGet();
            totalRequestCount.decrementAndGet();

            // 处理下一个排队任务
            processWaitingQueue();
        }
    }

    /**
     * 将后台任务加入优先级队列
     */
    private void queueBackgroundTasks(AsyncAnalysisTask asyncTask, String aiResult, long aiDuration) {
        String requestId = asyncTask.getRequestId();

        // 缓存存储任务（中优先级）
        backgroundQueue.offer(new BackgroundTask(requestId, TaskPriority.CACHE_STORAGE, () -> {
            try {
                logger.debug("执行缓存存储: {}", requestId);
                // 这里可以添加缓存逻辑
                Thread.sleep(100); // 模拟缓存操作
            } catch (Exception e) {
                logger.error("缓存存储失败: {}", requestId, e);
            }
        }));

        // 性能统计任务（低优先级）
        backgroundQueue.offer(new BackgroundTask(requestId, TaskPriority.PERFORMANCE_LOG, () -> {
            try {
                logger.debug("执行性能统计: {}", requestId);
                // 这里可以添加性能统计逻辑
                Thread.sleep(50); // 模拟统计操作
            } catch (Exception e) {
                logger.error("性能统计失败: {}", requestId, e);
            }
        }));

        // 启动后台任务处理器
        processBackgroundQueue();
    }

    /**
     * 处理后台任务队列
     */
    private void processBackgroundQueue() {
        backgroundTaskExecutor.submit(() -> {
            while (!backgroundQueue.isEmpty()) {
                try {
                    BackgroundTask task = backgroundQueue.poll(1, TimeUnit.SECONDS);
                    if (task != null) {
                        task.execute();
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    logger.error("后台任务处理异常", e);
                }
            }
        });
    }

    /**
     * 生成安全的RequestID
     */
    private String generateSecureRequestId() {
        long counter = requestIdCounter.incrementAndGet();
        long timestamp = System.currentTimeMillis();
        return String.format("req_%d_%d_%s",
            timestamp,
            counter,
            UUID.randomUUID().toString().substring(0, 8));
    }

    /**
     * 清理过期任务 - 防御型设计
     */
    private void cleanupExpiredTasks() {
        int cleanedCount = 0;
        Iterator<Map.Entry<String, AsyncAnalysisTask>> iterator = activeTasks.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, AsyncAnalysisTask> entry = iterator.next();
            AsyncAnalysisTask task = entry.getValue();

            if (task.isExpired() || task.getStatus() == TaskStatus.COMPLETED) {
                iterator.remove();
                cleanedCount++;
                logger.debug("清理过期/完成任务: {}", task.getRequestId());
            }
        }

        if (cleanedCount > 0) {
            logger.info("清理了 {} 个过期/完成任务", cleanedCount);
        }
    }

    /**
     * 获取异步分析结果
     */
    public CompletableFuture<String> getAsyncResult(String requestId) {
        AsyncAnalysisTask task = activeTasks.get(requestId);
        if (task == null) {
            CompletableFuture<String> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(new RuntimeException("请求不存在: " + requestId));
            return failedFuture;
        }
        return task.getUserResponseFuture();
    }

    /**
     * 获取任务状态
     */
    public TaskStatus getTaskStatus(String requestId) {
        AsyncAnalysisTask task = activeTasks.get(requestId);
        return task != null ? task.getStatus() : null;
    }

    /**
     * 带超时控制的AI分析（保持向后兼容）
     */
    public String executeAnalysisWithTimeout(LiuYaoAiAnalysisRequest request, int timeoutSeconds) {
        logger.info("开始超时控制的AI分析，超时时间: {}秒", timeoutSeconds);
        
        performanceMonitor.startStep("超时控制分析");
        
        try {
            // 使用CompletableFuture实现超时控制
            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return originalService.executeAnalysis(request);
                } catch (Exception e) {
                    logger.error("AI分析异常", e);
                    throw new RuntimeException(e);
                }
            }, userResponseExecutor);

            // 等待结果，最多等待指定时间
            String result = future.get(timeoutSeconds, TimeUnit.SECONDS);
            
            long duration = performanceMonitor.endStep("超时控制分析");
            logger.info("AI分析成功完成，耗时: {}ms", duration);
            
            return result;

        } catch (TimeoutException e) {
            performanceMonitor.failStep("超时控制分析", "超时 " + timeoutSeconds + "秒");
            logger.warn("AI分析超时，{}秒内未完成", timeoutSeconds);
            
            return generateTimeoutResponse(request);
            
        } catch (Exception e) {
            performanceMonitor.failStep("超时控制分析", e.getMessage());
            logger.error("AI分析失败", e);
            
            return generateErrorResponse(e.getMessage());
        }
    }

    /**
     * 普通分析（保持原有逻辑）
     */
    public String executeAnalysis(LiuYaoAiAnalysisRequest request) {
        return originalService.executeAnalysis(request);
    }

    /**
     * 快速分析（30秒超时）
     */
    public String executeFastAnalysis(LiuYaoAiAnalysisRequest request) {
        return executeAnalysisWithTimeout(request, 30);
    }

    /**
     * 标准分析（60秒超时）
     */
    public String executeStandardAnalysis(LiuYaoAiAnalysisRequest request) {
        return executeAnalysisWithTimeout(request, 60);
    }

    /**
     * 深度分析（120秒超时）
     */
    public String executeDeepAnalysis(LiuYaoAiAnalysisRequest request) {
        return executeAnalysisWithTimeout(request, 120);
    }

    /**
     * 生成超时降级响应
     */
    private String generateTimeoutResponse(LiuYaoAiAnalysisRequest request) {
        logger.info("生成超时降级响应");
        
        String occupy = request.getLiuYaoRequest().getOccupy();
        
        if (occupy != null) {
            if (occupy.contains("工作") || occupy.contains("事业")) {
                return "### AI分析超时降级响应\n\n" +
                       "由于网络延迟，AI分析暂时超时。基于您的工作相关问题，建议：\n\n" +
                       "1. **当前状况**：事业发展需要耐心和坚持\n" +
                       "2. **行动建议**：保持积极态度，寻求合作机会\n" +
                       "3. **注意事项**：避免急躁，稳步前进\n\n" +
                       "*注：这是系统降级响应，建议稍后重试获取完整AI分析*";
            } else if (occupy.contains("感情") || occupy.contains("爱情")) {
                return "### AI分析超时降级响应\n\n" +
                       "由于网络延迟，AI分析暂时超时。基于您的感情相关问题，建议：\n\n" +
                       "1. **当前状况**：感情发展需要真诚沟通\n" +
                       "2. **行动建议**：保持开放心态，增进理解\n" +
                       "3. **注意事项**：避免误解，多些包容\n\n" +
                       "*注：这是系统降级响应，建议稍后重试获取完整AI分析*";
            }
        }
        
        return "### AI分析超时降级响应\n\n" +
               "由于网络延迟，AI分析暂时超时。一般性建议：\n\n" +
               "1. **当前状况**：事态发展需要观察和等待\n" +
               "2. **行动建议**：保持冷静，谨慎决策\n" +
               "3. **注意事项**：多方考虑，稳妥行事\n\n" +
               "*注：这是系统降级响应，建议稍后重试获取完整AI分析*";
    }

    /**
     * 生成错误降级响应
     */
    private String generateErrorResponse(String errorMessage) {
        logger.info("生成错误降级响应");
        
        return "### AI分析暂时不可用\n\n" +
               "系统遇到技术问题，暂时无法提供AI分析。\n\n" +
               "**建议：**\n" +
               "1. 请稍后重试\n" +
               "2. 检查网络连接\n" +
               "3. 如问题持续，请联系技术支持\n\n" +
               "*错误信息：" + errorMessage + "*";
    }

    /**
     * 检查服务状态
     */
    public String getServiceStatus() {
        try {
            // 创建一个简单的测试请求
            // 这里不实际执行，只是检查服务是否可用
            return "AI服务正常运行";
        } catch (Exception e) {
            return "AI服务异常：" + e.getMessage();
        }
    }

    /**
     * 获取性能统计
     */
    public String getPerformanceStats() {
        return String.format("用户响应线程池 - 活跃: %d, 队列: %d | 后台任务线程池 - 活跃: %d, 队列: %d | 正在处理: %d | 总请求: %d | 排队: %d | 后台队列: %d",
            ((ThreadPoolExecutor) userResponseExecutor).getActiveCount(),
            ((ThreadPoolExecutor) userResponseExecutor).getQueue().size(),
            ((ThreadPoolExecutor) backgroundTaskExecutor).getActiveCount(),
            ((ThreadPoolExecutor) backgroundTaskExecutor).getQueue().size(),
            activeProcessingCount.get(),
            totalRequestCount.get(),
            waitingQueue.size(),
            backgroundQueue.size());
    }

    /**
     * 定期清理过期任务 - 防御型设计
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void scheduledCleanup() {
        try {
            cleanupExpiredTasks();
            logger.debug("定期清理任务完成");
        } catch (Exception e) {
            logger.error("定期清理任务失败", e);
        }
    }

    /**
     * 关闭服务时清理资源 - 防御型设计
     */
    @PreDestroy
    public void shutdown() {
        logger.info("开始关闭PerformanceOptimizedAIService...");

        // 关闭用户响应线程池
        userResponseExecutor.shutdown();
        try {
            if (!userResponseExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                userResponseExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            userResponseExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        // 关闭后台任务线程池
        backgroundTaskExecutor.shutdown();
        try {
            if (!backgroundTaskExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                backgroundTaskExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            backgroundTaskExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        // 清理所有活跃任务
        activeTasks.clear();
        backgroundQueue.clear();

        logger.info("PerformanceOptimizedAIService关闭完成");
    }

    /**
     * 获取系统健康状态
     */
    public Map<String, Object> getHealthStatus() {
        Map<String, Object> health = new HashMap<>();
        health.put("activeProcessing", activeProcessingCount.get());
        health.put("totalRequests", totalRequestCount.get());
        health.put("waitingQueue", waitingQueue.size());
        health.put("activeTasks", activeTasks.size());
        health.put("backgroundQueueSize", backgroundQueue.size());
        health.put("userResponsePoolActive", ((ThreadPoolExecutor) userResponseExecutor).getActiveCount());
        health.put("backgroundPoolActive", ((ThreadPoolExecutor) backgroundTaskExecutor).getActiveCount());
        health.put("maxConcurrentProcessing", MAX_CONCURRENT_PROCESSING);
        health.put("maxQueueSize", MAX_QUEUE_SIZE);

        // 健康状态评估
        boolean healthy = activeProcessingCount.get() < MAX_CONCURRENT_PROCESSING &&
                         totalRequestCount.get() < MAX_QUEUE_SIZE * 0.8;
        health.put("healthy", healthy);
        health.put("status", healthy ? "HEALTHY" : "OVERLOADED");

        // 排队信息
        int queuePosition = Math.max(0, totalRequestCount.get() - activeProcessingCount.get());
        int estimatedWaitMinutes = queuePosition * 2;
        health.put("queuePosition", queuePosition);
        health.put("estimatedWaitMinutes", estimatedWaitMinutes);

        return health;
    }
}
