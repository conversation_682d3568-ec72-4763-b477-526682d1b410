#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试Gemini API配置
"""

import requests
import json
import time

# Gemini API配置
API_KEY = "AIzaSyAT2Xg1_6NZdWlpJ6j6dQ_qwpQcihARf7o"
API_URL = "https://gemini.scself1700.dpdns.org/v1/chat/completions"
MODEL = "gemini-2.5-flash"

def test_gemini_api():
    """测试Gemini API连接"""
    print("🤖 开始测试Gemini API配置...")
    print(f"API URL: {API_URL}")
    print(f"模型: {MODEL}")
    print(f"API密钥: {API_KEY[:10]}...")
    print("-" * 50)
    
    # 构建请求
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": MODEL,
        "messages": [
            {
                "role": "user",
                "content": "你好，请简单回复一下，确认连接正常。"
            }
        ],
        "temperature": 0.7,
        "max_tokens": 100,
        "stream": False
    }
    
    try:
        print("📡 发送请求...")
        start_time = time.time()
        
        response = requests.post(
            API_URL,
            headers=headers,
            json=payload,
            timeout=30
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ 请求耗时: {duration:.2f}秒")
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功！")
            print("\n📝 响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 提取AI回复
            if "choices" in result and len(result["choices"]) > 0:
                ai_reply = result["choices"][0]["message"]["content"]
                print(f"\n🤖 AI回复: {ai_reply}")
            
            return True
            
        else:
            print(f"❌ API调用失败！状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时！")
        return False
    except requests.exceptions.ConnectionError:
        print("🔌 连接错误！请检查网络连接和API地址。")
        return False
    except Exception as e:
        print(f"💥 未知错误: {str(e)}")
        return False

def test_liuyao_prompt():
    """测试六爻分析提示词"""
    print("\n" + "="*50)
    print("🔮 测试六爻分析功能...")
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    liuyao_prompt = "请分析乾为天卦的基本含义，包括：1.卦象特点 2.象征意义 3.适用情况。请控制在500字以内。"
    
    payload = {
        "model": MODEL,
        "messages": [
            {
                "role": "user",
                "content": liuyao_prompt
            }
        ],
        "temperature": 0.7,
        "max_tokens": 1000,  # 增加token限制
        "stream": False
    }
    
    try:
        print("📡 发送六爻分析请求...")
        start_time = time.time()
        
        response = requests.post(
            API_URL,
            headers=headers,
            json=payload,
            timeout=60
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ 分析耗时: {duration:.2f}秒")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 六爻分析成功！")

            # 调试：打印完整响应
            print("\n🔍 完整响应结构:")
            print(json.dumps(result, indent=2, ensure_ascii=False))

            if "choices" in result and len(result["choices"]) > 0:
                choice = result["choices"][0]
                message = choice.get("message", {})
                analysis = message.get("content", "无分析内容")
                print(f"\n🔮 六爻分析结果:\n{analysis}")
                print(f"\n📊 Token使用情况: {result.get('usage', {})}")

            return True
            
        else:
            print(f"❌ 六爻分析失败！状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"💥 六爻分析错误: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Gemini API配置测试工具")
    print("="*50)
    
    # 基础连接测试
    basic_success = test_gemini_api()
    
    if basic_success:
        # 六爻分析测试
        liuyao_success = test_liuyao_prompt()
        
        if liuyao_success:
            print("\n🎉 所有测试通过！Gemini API配置正确。")
        else:
            print("\n⚠️ 基础连接正常，但六爻分析测试失败。")
    else:
        print("\n❌ 基础连接测试失败，请检查API配置。")
    
    print("\n" + "="*50)
    print("测试完成！")
