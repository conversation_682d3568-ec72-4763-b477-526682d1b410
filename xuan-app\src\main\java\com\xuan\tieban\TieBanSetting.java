package com.xuan.tieban;

import java.util.Date;

/**
 * 铁板神数-设置
 * <p>
 * 用于初始化 TieBan 实例的配置信息。
 */
public class TieBanSetting {

    /**
     * 日期
     */
    private Date date;
    /**
     * 日期类型（0:公历, 1:农历）
     */
    private int dateType;
    /**
     * 年干支
     */
    private String yearGanZhi;
    /**
     * 月干支
     */
    private String monthGanZhi;
    /**
     * 日干支
     */
    private String dayGanZhi;
    /**
     * 时干支
     */
    private String hourGanZhi;

    /**
     * 使用当前公历日期初始化
     */
    public TieBanSetting() {
        this.date = new Date();
        this.dateType = 0;
    }

    /**
     * 使用公历日期初始化
     *
     * @param date 公历日期
     */
    public TieBanSetting(Date date) {
        this.date = date;
        this.dateType = 0;
    }

    /**
     * 使用日期和日期类型初始化
     *
     * @param date     日期
     * @param dateType 日期类型（0:公历, 1:农历）
     */
    public TieBanSetting(Date date, int dateType) {
        this.date = date;
        this.dateType = dateType;
    }

    // --- Getters and Setters ---

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public int getDateType() {
        return dateType;
    }

    public void setDateType(int dateType) {
        this.dateType = dateType;
    }

    public String getYearGanZhi() {
        return yearGanZhi;
    }

    public void setYearGanZhi(String yearGanZhi) {
        this.yearGanZhi = yearGanZhi;
    }

    public String getMonthGanZhi() {
        return monthGanZhi;
    }

    public void setMonthGanZhi(String monthGanZhi) {
        this.monthGanZhi = monthGanZhi;
    }

    public String getDayGanZhi() {
        return dayGanZhi;
    }

    public void setDayGanZhi(String dayGanZhi) {
        this.dayGanZhi = dayGanZhi;
    }

    public String getHourGanZhi() {
        return hourGanZhi;
    }

    public void setHourGanZhi(String hourGanZhi) {
        this.hourGanZhi = hourGanZhi;
    }
} 