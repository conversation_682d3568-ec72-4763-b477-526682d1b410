# Refactoring Plan for TieBanMap

**Goal:** Resolve the `...is exceeding the 65535 bytes limit` error permanently and create a clean, maintainable data access interface.

**Context:** The `TieBanMap.java` class contains a very large amount of hardcoded data, causing Java method size limits to be exceeded, both in the static initializer (`<clinit>`) and in regular methods.

### Action Items:

1.  **Separate Data from Code (Completed):**
    *   [x] Extracted all `TIE_BAN_MAP` data from `TieBanMap.java`.
    *   [x] Converted the data into a structured `tieban_data.json` file.
    *   [x] Placed the JSON file in `src/main/resources/com/xuan/tieban/`.

2.  **Refactor Java Code:**
    *   [ ] **Modify `TieBanMap.java`:** Replace the entire class content with a new implementation that loads data from `tieban_data.json` at runtime.
    *   [ ] **Use Gson Library:** The new implementation will use the `com.google.code.gson` library for JSON parsing.
    *   [ ] **Provide a Clean Interface:** The refactored class will expose a simple public method, `public static List<String> getTieBans(int key)`, for data access. The internal map key should be a `String` to match the JSON structure.

3.  **Handle Dependencies:**
    *   [ ] **Check for Gson:** Verify if the Gson dependency is already present in the project's build configuration (`pom.xml` or `build.gradle`).
    *   [ ] **Add Gson if Missing:** If the dependency is not found, add it to the build configuration file. 