package com.shandai.xuan.controller;

import com.shandai.xuan.dto.LiuYaoAiAnalysisRequest;
import com.shandai.xuan.dto.LiuYaoRequest;
import com.shandai.xuan.service.LiuYaoAiAnalysisService;
import com.shandai.xuan.util.PerformanceMonitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 性能测试控制器
 * 用于测试AI分析的性能监控功能
 */
@RestController
@RequestMapping("/api/performance")
public class PerformanceTestController {

    @Autowired
    private LiuYaoAiAnalysisService liuYaoAiAnalysisService;

    @Autowired
    private PerformanceMonitor performanceMonitor;

    /**
     * 测试AI分析性能监控
     */
    @PostMapping("/test-ai-analysis")
    public Map<String, Object> testAiAnalysisPerformance(@RequestBody(required = false) LiuYaoRequest request) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 如果没有提供请求参数，使用默认的测试数据
            if (request == null) {
                request = createDefaultTestRequest();
            }

            // 构建AI分析请求
            LiuYaoAiAnalysisRequest aiRequest = new LiuYaoAiAnalysisRequest();
            aiRequest.setLiuYaoRequest(request);
            aiRequest.setQuestion(request.getOccupy());
            aiRequest.setAnalysisType("comprehensive");

            // 执行AI分析（会自动进行性能监控）
            String aiAnalysis = liuYaoAiAnalysisService.executeAnalysis(aiRequest);

            result.put("success", true);
            result.put("aiAnalysis", aiAnalysis);
            result.put("message", "AI分析完成，请查看控制台输出的性能报告");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", "性能测试失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 简单的性能监控测试
     */
    @GetMapping("/test-monitor")
    public Map<String, Object> testPerformanceMonitor() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 清理之前的数据
            performanceMonitor.clear();

            // 模拟一些步骤
            performanceMonitor.startStep("测试步骤1");
            Thread.sleep(100); // 模拟100ms的处理时间
            performanceMonitor.endStep("测试步骤1");

            performanceMonitor.startStep("测试步骤2");
            Thread.sleep(50); // 模拟50ms的处理时间
            performanceMonitor.endStep("测试步骤2");

            performanceMonitor.startStep("测试步骤3");
            Thread.sleep(200); // 模拟200ms的处理时间
            performanceMonitor.endStep("测试步骤3");

            // 输出性能报告
            performanceMonitor.printSummary();

            result.put("success", true);
            result.put("message", "性能监控测试完成，请查看控制台输出");

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", "性能监控测试失败：" + e.getMessage());
        } finally {
            performanceMonitor.clear();
        }

        return result;
    }

    /**
     * 创建默认的测试请求数据
     */
    private LiuYaoRequest createDefaultTestRequest() {
        LiuYaoRequest request = new LiuYaoRequest();
        
        // 设置基本信息
        request.setName("测试用户");
        request.setSex(1); // 性别（0:女。1:男）
        request.setOccupy("测试占事：工作发展如何？");
        request.setAddress("北京");

        // 设置时间（当前时间）
        java.util.Calendar cal = java.util.Calendar.getInstance();
        request.setYear(cal.get(java.util.Calendar.YEAR));
        request.setMonth(cal.get(java.util.Calendar.MONTH) + 1);
        request.setDay(cal.get(java.util.Calendar.DAY_OF_MONTH));
        request.setHour(cal.get(java.util.Calendar.HOUR_OF_DAY));
        request.setMinute(cal.get(java.util.Calendar.MINUTE));

        // 设置其他参数
        request.setDateType(0); // 公历
        request.setLeapMonth(0); // 闰月（0:不使用闰月。1:使用闰月）
        request.setQiGuaMode(0);
        request.setYearGanZhiSet(0); // 年干支设置（0:以正月初一作为新年的开始。1:以立春当天作为新年的开始。2:以立春交接的时刻作为新年的开始）
        
        // 设置六爻数据（随机生成）
        request.setLiuYao((int)(Math.random() * 2) + 1);
        request.setWuYao((int)(Math.random() * 2) + 1);
        request.setSiYao((int)(Math.random() * 2) + 1);
        request.setSanYao((int)(Math.random() * 2) + 1);
        request.setErYao((int)(Math.random() * 2) + 1);
        request.setYiYao((int)(Math.random() * 2) + 1);
        
        return request;
    }
}
