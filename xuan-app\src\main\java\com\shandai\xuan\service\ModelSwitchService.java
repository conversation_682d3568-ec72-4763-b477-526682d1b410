package com.shandai.xuan.service;

import com.shandai.xuan.config.AiAnalysisConfig;
import com.shandai.xuan.config.ModelConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 模型自动切换服务
 * 监控模型使用情况并在接近限制时自动切换
 */
@Service
@Slf4j
public class ModelSwitchService {
    
    @Autowired
    private ModelUsageMonitorService usageMonitorService;
    
    @Autowired
    private ModelConfig modelConfig;
    
    @Autowired
    private AiAnalysisConfig aiAnalysisConfig;
    
    // 切换锁，防止并发切换
    private final AtomicBoolean switchingInProgress = new AtomicBoolean(false);
    
    // 最后切换时间
    private volatile long lastSwitchTime = 0;
    
    // 切换冷却时间（毫秒）- 防止频繁切换
    private static final long SWITCH_COOLDOWN_MS = 30000; // 30秒
    
    @PostConstruct
    public void init() {
        // 初始化使用统计
        usageMonitorService.initializeUsageStats();
        log.info("模型切换服务已初始化");
    }
    
    /**
     * 检查是否需要切换模型
     */
    @Scheduled(fixedRateString = "#{${ai.analysis.monitoring-interval-seconds:30} * 1000}")
    public void checkAndSwitchModel() {
        if (!aiAnalysisConfig.isAutoModelSwitchEnabled()) {
            return;
        }
        
        // 检查冷却时间
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastSwitchTime < SWITCH_COOLDOWN_MS) {
            return;
        }
        
        // 防止并发切换
        if (!switchingInProgress.compareAndSet(false, true)) {
            return;
        }
        
        try {
            String currentModel = usageMonitorService.getCurrentActiveModel();
            double threshold = aiAnalysisConfig.getUsageThreshold();
            
            // 检查当前模型是否需要切换
            if (shouldSwitchModel(currentModel, threshold)) {
                String nextModel = findNextAvailableModel(currentModel, threshold);
                if (nextModel != null && !nextModel.equals(currentModel)) {
                    switchToModel(nextModel);
                    lastSwitchTime = currentTime;
                } else {
                    log.warn("没有找到可用的备用模型，当前模型: {}", currentModel);
                }
            }
        } finally {
            switchingInProgress.set(false);
        }
    }
    
    /**
     * 检查是否应该切换模型
     */
    private boolean shouldSwitchModel(String modelName, double threshold) {
        ModelUsageMonitorService.UsageStats stats = usageMonitorService.getUsageStats(modelName);
        if (stats == null) {
            return false;
        }
        
        boolean nearRpmLimit = stats.isNearRpmLimit(threshold);
        boolean nearDailyLimit = stats.isNearDailyLimit(threshold);
        
        if (nearRpmLimit || nearDailyLimit) {
            log.info("模型 {} 接近使用限制 - RPM: {:.1f}%, 每日: {:.1f}%, 阈值: {:.1f}%", 
                modelName, stats.getRpmUsagePercentage(), stats.getDailyUsagePercentage(), threshold);
            return true;
        }
        
        return false;
    }
    
    /**
     * 查找下一个可用的模型
     */
    private String findNextAvailableModel(String currentModel, double threshold) {
        List<ModelConfig.ModelInfo> primaryModels = modelConfig.getPrimaryModels();
        
        // 找到当前模型的位置
        int currentIndex = -1;
        for (int i = 0; i < primaryModels.size(); i++) {
            if (primaryModels.get(i).getModelName().equals(currentModel)) {
                currentIndex = i;
                break;
            }
        }
        
        // 从下一个模型开始查找可用的模型
        for (int i = currentIndex + 1; i < primaryModels.size(); i++) {
            String modelName = primaryModels.get(i).getModelName();
            if (usageMonitorService.isModelAvailable(modelName, threshold)) {
                log.info("找到可用的备用模型: {}", modelName);
                return modelName;
            }
        }
        
        // 如果所有后续模型都不可用，检查是否可以回到第一个模型
        if (currentIndex > 0) {
            for (int i = 0; i < currentIndex; i++) {
                String modelName = primaryModels.get(i).getModelName();
                if (usageMonitorService.isModelAvailable(modelName, threshold)) {
                    log.info("回退到可用模型: {}", modelName);
                    return modelName;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 切换到指定模型
     */
    public boolean switchToModel(String modelName) {
        ModelConfig.ModelInfo modelInfo = modelConfig.getModelInfo(modelName);
        if (modelInfo == null) {
            log.error("尝试切换到未知模型: {}", modelName);
            return false;
        }
        
        String oldModel = usageMonitorService.getCurrentActiveModel();
        usageMonitorService.setCurrentActiveModel(modelName);
        
        log.info("=== 模型切换 ===");
        log.info("时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        log.info("从: {} 切换到: {}", oldModel, modelName);
        log.info("新模型限制: RPM={}, 每日={}", modelInfo.getRpmLimit(), modelInfo.getDailyLimit());
        log.info("切换原因: 当前模型接近使用限制");
        
        return true;
    }
    
    /**
     * 手动切换到指定模型
     */
    public boolean manualSwitchToModel(String modelName) {
        if (switchingInProgress.get()) {
            log.warn("模型切换正在进行中，请稍后再试");
            return false;
        }
        
        ModelConfig.ModelInfo modelInfo = modelConfig.getModelInfo(modelName);
        if (modelInfo == null) {
            log.error("尝试手动切换到未知模型: {}", modelName);
            return false;
        }
        
        switchingInProgress.set(true);
        try {
            String oldModel = usageMonitorService.getCurrentActiveModel();
            usageMonitorService.setCurrentActiveModel(modelName);
            lastSwitchTime = System.currentTimeMillis();
            
            log.info("=== 手动模型切换 ===");
            log.info("时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            log.info("从: {} 切换到: {}", oldModel, modelName);
            log.info("新模型限制: RPM={}, 每日={}", modelInfo.getRpmLimit(), modelInfo.getDailyLimit());
            
            return true;
        } finally {
            switchingInProgress.set(false);
        }
    }
    
    /**
     * 获取当前模型状态
     */
    public String getCurrentModelStatus() {
        String currentModel = usageMonitorService.getCurrentActiveModel();
        ModelUsageMonitorService.UsageStats stats = usageMonitorService.getUsageStats(currentModel);
        
        if (stats == null) {
            return String.format("当前模型: %s (无使用统计)", currentModel);
        }
        
        return String.format("当前模型: %s | RPM: %d/%d (%.1f%%) | 每日: %d/%d (%.1f%%) | 可用: %s",
            currentModel,
            stats.getCurrentMinuteRequests().get(), stats.getRpmLimit(), stats.getRpmUsagePercentage(),
            stats.getCurrentDayRequests().get(), stats.getDailyLimit(), stats.getDailyUsagePercentage(),
            stats.canSendRequest() ? "是" : "否");
    }
    
    /**
     * 重置所有模型的使用统计
     */
    public void resetAllUsageStats() {
        log.info("重置所有模型的使用统计");
        usageMonitorService.initializeUsageStats();
    }
    
    /**
     * 获取下一个推荐的模型
     */
    public String getNextRecommendedModel() {
        String currentModel = usageMonitorService.getCurrentActiveModel();
        double threshold = aiAnalysisConfig.getUsageThreshold();
        return findNextAvailableModel(currentModel, threshold);
    }
    
    /**
     * 检查是否正在切换
     */
    public boolean isSwitchingInProgress() {
        return switchingInProgress.get();
    }
}
