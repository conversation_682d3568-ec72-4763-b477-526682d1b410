# 技术栈

## 语言与运行环境
- Java 8 (JDK 1.8)
- Maven 构建

## 核心框架
- Spring Boot 2.7.14

## 三方依赖
| 组件 | 版本 | 说明 |
| --- | --- | --- |
| spring-boot-starter-web | 2.7.14 | Web 框架 |
| spring-boot-starter-test | 2.7.14 | 测试 |
| spring-core | 5.3.29 | Spring 核心 |
| tomcat-embed-core | 9.0.78 | 内嵌 Tomcat |
| junit | 4.13.2 | 单元测试 |
| lombok | 1.18.30 (provided) | 代码生成 |
| fastjson | 1.2.58 | JSON 解析 |
| commons-io | 2.6 | IO 工具 |
| commons-lang | 2.6 | 字符串等工具 |
| commons-validator | 1.6 | 校验 |
| commons-lang3 | 3.8.1 | 工具 |
| ip2region | 1.7.2 | IP 离线定位 |
| lunar | 1.3.14 | 农历 / 八字计算 |
| hutool-core | 5.8.20 | 通用工具 |

## 项目模块规划
- 六爻 (已开发：断卦模块 部分)
- 八字 (待补充)
- 铁板神数 (未开始) 