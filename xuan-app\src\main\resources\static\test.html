<!DOCTYPE html>
<html>
<head>
    <title>测试AI分析功能</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 10px 0; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .loading { color: #007bff; font-weight: bold; }
    </style>
</head>
<body>
    <h1>六爻AI分析功能测试</h1>
    
    <h2>测试数据</h2>
    <p>年月日时：2025年7月27日0时40分</p>
    <p>占事：考学</p>
    <p>六爻：上爻静，五爻动，四爻静，三爻动，二爻静，初爻动</p>
    
    <button onclick="testAIAnalysis()">测试AI分析</button>
    <button onclick="testBasicLiuYao()">测试基础六爻计算</button>
    
    <div id="result"></div>

    <script>
        async function testAIAnalysis() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="loading">🤖 正在测试AI分析功能...</div>';
            
            const testData = {
                year: 2025,
                month: 7,
                day: 27,
                hour: 0,
                minute: 40,
                sex: 1,
                name: "测试用户",
                occupy: "考学",
                address: "北京",
                dateType: 0,
                leapMonth: 0,
                qiGuaMode: 0,
                liuYao: 0,
                wuYao: 1,
                siYao: 0,
                sanYao: 1,
                erYao: 0,
                yiYao: 1,
                yearGanZhiSet: 0
            };
            
            try {
                const response = await fetch('/api/divination/liuyao/simple-ai', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ AI分析测试成功！</h3>
                            <h4>AI分析结果：</h4>
                            <div style="white-space: pre-line; line-height: 1.6;">${result.aiAnalysis}</div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ AI分析测试失败</h3>
                            <p>错误信息：${result.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 网络请求失败</h3>
                        <p>错误信息：${error.message}</p>
                        <p>请确保应用程序正在运行在 http://localhost:8080</p>
                    </div>
                `;
            }
        }
        
        async function testBasicLiuYao() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="loading">📊 正在测试基础六爻计算...</div>';
            
            const testData = {
                year: 2025,
                month: 7,
                day: 27,
                hour: 0,
                minute: 40,
                sex: 1,
                name: "测试用户",
                occupy: "考学",
                address: "北京",
                dateType: 0,
                leapMonth: 0,
                qiGuaMode: 0,
                liuYao: 0,
                wuYao: 1,
                siYao: 0,
                sanYao: 1,
                erYao: 0,
                yiYao: 1,
                yearGanZhiSet: 0
            };
            
            try {
                const response = await fetch('/api/divination/liuyao', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ 基础六爻计算测试成功！</h3>
                            <h4>计算结果：</h4>
                            <p><strong>本卦：</strong>${result.data.benGua}</p>
                            <p><strong>变卦：</strong>${result.data.bianGua}</p>
                            <p><strong>断卦：</strong>${result.data.duangua}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 基础六爻计算测试失败</h3>
                            <p>错误信息：${result.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 网络请求失败</h3>
                        <p>错误信息：${error.message}</p>
                        <p>请确保应用程序正在运行在 http://localhost:8080</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
