#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI分析性能优化测试脚本
测试优化前后的性能差异
"""

import requests
import time
import json
import statistics
from datetime import datetime

# 配置
APP_BASE_URL = "http://localhost:8080"
TEST_ROUNDS = 5  # 测试轮数

def print_header(title):
    """打印测试标题"""
    print("\n" + "="*80)
    print(f"🧪 {title}")
    print("="*80)

def print_section(title):
    """打印章节标题"""
    print(f"\n📋 {title}")
    print("-"*60)

def create_test_data():
    """创建测试数据"""
    return {
        "year": 2025,
        "month": 1,
        "day": 29,
        "hour": 21,
        "minute": 30,
        "occupy": "测试AI分析性能优化效果",
        "yiYao": "阳",
        "erYao": "阴", 
        "sanYao": "阳",
        "siYao": "阴",
        "wuYao": "阳",
        "liuYao": "阴"
    }

def test_original_ai_service(test_data):
    """测试原始AI服务性能"""
    print_section("测试原始AI服务")
    
    times = []
    success_count = 0
    
    for i in range(TEST_ROUNDS):
        print(f"🔄 第 {i+1}/{TEST_ROUNDS} 轮测试...")
        
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{APP_BASE_URL}/api/divination/liuyao/ai-analysis",
                json=test_data,
                headers={"Content-Type": "application/json"},
                timeout=120
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    times.append(duration)
                    success_count += 1
                    print(f"  ✅ 成功 - 耗时: {duration:.2f}秒")
                else:
                    print(f"  ❌ 失败: {result.get('error', '未知错误')}")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"  💥 异常: {e}")
        
        # 间隔1秒避免频繁请求
        if i < TEST_ROUNDS - 1:
            time.sleep(1)
    
    return times, success_count

def test_optimized_ai_service(test_data):
    """测试优化后的AI服务性能"""
    print_section("测试优化AI服务")
    
    # 注意：这里需要创建一个新的控制器端点来使用OptimizedAIService
    # 暂时使用相同的端点，但会受益于RestTemplate优化
    
    times = []
    success_count = 0
    
    for i in range(TEST_ROUNDS):
        print(f"🚀 第 {i+1}/{TEST_ROUNDS} 轮测试（优化版）...")
        
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{APP_BASE_URL}/api/divination/liuyao/ai-analysis",
                json=test_data,
                headers={
                    "Content-Type": "application/json",
                    "Accept-Encoding": "gzip, deflate",  # 启用压缩
                    "Connection": "keep-alive"            # 保持连接
                },
                timeout=120
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    times.append(duration)
                    success_count += 1
                    print(f"  ⚡ 成功 - 耗时: {duration:.2f}秒")
                else:
                    print(f"  ❌ 失败: {result.get('error', '未知错误')}")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"  💥 异常: {e}")
        
        # 间隔0.5秒，测试连接复用效果
        if i < TEST_ROUNDS - 1:
            time.sleep(0.5)
    
    return times, success_count

def analyze_performance(original_times, optimized_times):
    """分析性能数据"""
    print_section("性能分析报告")
    
    if not original_times or not optimized_times:
        print("❌ 数据不足，无法进行性能分析")
        return
    
    # 计算统计数据
    orig_avg = statistics.mean(original_times)
    orig_min = min(original_times)
    orig_max = max(original_times)
    orig_std = statistics.stdev(original_times) if len(original_times) > 1 else 0
    
    opt_avg = statistics.mean(optimized_times)
    opt_min = min(optimized_times)
    opt_max = max(optimized_times)
    opt_std = statistics.stdev(optimized_times) if len(optimized_times) > 1 else 0
    
    # 计算改进
    avg_improvement = ((orig_avg - opt_avg) / orig_avg) * 100
    min_improvement = ((orig_min - opt_min) / orig_min) * 100
    
    print(f"📊 原始服务统计:")
    print(f"   平均耗时: {orig_avg:.2f}秒")
    print(f"   最快耗时: {orig_min:.2f}秒")
    print(f"   最慢耗时: {orig_max:.2f}秒")
    print(f"   标准差:   {orig_std:.2f}秒")
    
    print(f"\n⚡ 优化服务统计:")
    print(f"   平均耗时: {opt_avg:.2f}秒")
    print(f"   最快耗时: {opt_min:.2f}秒")
    print(f"   最慢耗时: {opt_max:.2f}秒")
    print(f"   标准差:   {opt_std:.2f}秒")
    
    print(f"\n🎯 性能改进:")
    print(f"   平均提升: {avg_improvement:+.1f}%")
    print(f"   最快提升: {min_improvement:+.1f}%")
    print(f"   时间节省: {orig_avg - opt_avg:.2f}秒")
    
    if avg_improvement > 0:
        print(f"   🎉 优化成功！平均提升 {avg_improvement:.1f}%")
    elif avg_improvement < -5:
        print(f"   ⚠️ 性能下降 {abs(avg_improvement):.1f}%，需要进一步调优")
    else:
        print(f"   📊 性能基本持平，可能需要更多测试")

def test_network_optimization():
    """测试网络层优化效果"""
    print_section("网络层优化测试")
    
    # 测试连接建立时间
    print("🔌 测试连接建立时间...")
    
    connection_times = []
    for i in range(3):
        start = time.time()
        try:
            response = requests.get(f"{APP_BASE_URL}/actuator/health", timeout=10)
            end = time.time()
            connection_times.append(end - start)
            print(f"   连接 {i+1}: {(end-start)*1000:.0f}ms")
        except Exception as e:
            print(f"   连接 {i+1}: 失败 - {e}")
    
    if connection_times:
        avg_connection = statistics.mean(connection_times) * 1000
        print(f"   平均连接时间: {avg_connection:.0f}ms")

def main():
    """主测试函数"""
    print_header("AI分析性能优化测试")
    print(f"🕒 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 测试目标: 验证RestTemplate连接池优化效果")
    print(f"🔄 测试轮数: {TEST_ROUNDS}")
    
    # 创建测试数据
    test_data = create_test_data()
    print(f"📝 测试数据: {test_data['occupy']}")
    
    # 测试网络优化
    test_network_optimization()
    
    # 测试原始服务
    print("\n" + "🔵 阶段1: 基准测试")
    original_times, original_success = test_original_ai_service(test_data)
    
    # 等待一段时间，让连接池生效
    print("\n⏳ 等待5秒，让连接池优化生效...")
    time.sleep(5)
    
    # 测试优化服务
    print("\n" + "🟢 阶段2: 优化测试")
    optimized_times, optimized_success = test_optimized_ai_service(test_data)
    
    # 分析结果
    analyze_performance(original_times, optimized_times)
    
    # 总结
    print_section("测试总结")
    print(f"✅ 原始服务成功率: {original_success}/{TEST_ROUNDS} ({original_success/TEST_ROUNDS*100:.1f}%)")
    print(f"⚡ 优化服务成功率: {optimized_success}/{TEST_ROUNDS} ({optimized_success/TEST_ROUNDS*100:.1f}%)")
    
    if original_times and optimized_times:
        improvement = ((statistics.mean(original_times) - statistics.mean(optimized_times)) / statistics.mean(original_times)) * 100
        if improvement > 10:
            print(f"🎉 优化效果显著！性能提升 {improvement:.1f}%")
        elif improvement > 0:
            print(f"✅ 优化有效，性能提升 {improvement:.1f}%")
        else:
            print(f"📊 优化效果不明显，可能需要进一步调优")
    
    print(f"\n📋 建议:")
    print(f"   1. 如果性能提升明显，可以进一步调优连接池参数")
    print(f"   2. 考虑实施流式响应以获得更好的用户体验")
    print(f"   3. 监控生产环境中的实际性能表现")

if __name__ == "__main__":
    main()
