package com.shandai.xuan.dto;

import lombok.Data;

/**
 * 铁板神数-请求
 * <p>
 * 用于接收前端发起的铁板神数计算请求的数据。
 */
@Data
public class TieBanRequest {

    /**
     * 年（公历或农历）
     */
    private int year;

    /**
     * 月（公历或农历）
     */
    private int month;

    /**
     * 日（公历或农历）
     */
    private int day;

    /**
     * 时
     */
    private int hour;

    /**
     * 分
     */
    private int minute;

    /**
     * 秒
     */
    private int second;

    /**
     * 日期类型（0:公历, 1:农历）
     */
    private int dateType;

} 