package com.shandai.xuan.dto;

import lombok.Data;
import lombok.Builder;
import java.util.List;

/**
 * 六爻数据值对象 - 用于AI分析的结构化数据
 */
@Data
@Builder
public class LiuYaoData {
    
    // 基本卦象信息
    private String benGua;           // 本卦
    private String bianGua;          // 变卦
    private String huGua;            // 互卦
    private String cuoGua;           // 错卦
    private String zongGua;          // 综卦
    
    // 六爻详细信息
    private List<String> benGuaLiuYaoLiuQin;    // 本卦六亲
    private List<String> benGuaLiuYaoShiYing;   // 本卦世应
    private List<String> benGuaLiuYaoWuXing;    // 本卦五行
    private List<String> benGuaLiuYaoLiuShen;   // 本卦六神
    private List<String> benGuaLiuYaoGanZhi;    // 本卦干支
    private List<String> liuYaoYaoXiangMarkName; // 六爻动静标记
    
    // 变卦信息（如果有动爻）
    private List<String> bianGuaLiuYaoLiuQin;   // 变卦六亲
    private List<String> bianGuaLiuYaoWuXing;   // 变卦五行
    
    // 神煞信息
    private String yiMa;             // 驿马
    private String tianMa;           // 天马
    private String tianYiGuiRen;     // 天乙贵人
    private String xianChi;          // 咸池
    
    // 八字信息
    private List<String> baZi;       // 八字
    private List<String> baZiWuXing; // 八字五行
    
    // 时间信息
    private String yearGanZhi;       // 年干支
    private String monthGanZhi;      // 月干支
    private String dayGanZhi;        // 日干支
    private String hourGanZhi;       // 时干支
    
    // 卦辞信息
    private String benGuaGuaCi;      // 本卦卦辞
    private String bianGuaGuaCi;     // 变卦卦辞
    
    // 系统断卦结果（作为参考）
    private String systemAnalysis;   // 系统断卦结果
    
    // 占事信息
    private String occupy;           // 占事类型
    private String question;         // 具体问题

    // 本卦内部详细信息
    private List<List<String>> benguainformation;  // 本卦内部信息（包含动静、旺衰、世应等详细分析）
}
