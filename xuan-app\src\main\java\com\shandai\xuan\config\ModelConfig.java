package com.shandai.xuan.config;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 模型配置管理器
 * 管理所有可用的Gemini模型及其使用限制
 */
@Component
public class ModelConfig {
    
    /**
     * 模型信息类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ModelInfo {
        /**
         * 模型名称
         */
        private String modelName;
        
        /**
         * 每分钟请求限制 (RPM)
         */
        private int rpmLimit;
        
        /**
         * 每日请求限制
         */
        private int dailyLimit;
        
        /**
         * 优先级（数字越小优先级越高）
         */
        private int priority;
        
        /**
         * 是否可用
         */
        private boolean available = true;
        
        /**
         * 模型描述
         */
        private String description;
    }
    
    /**
     * 获取所有可用模型配置（按优先级排序）
     */
    public List<ModelInfo> getAllModels() {
        return Arrays.asList(
            // 主要模型 - gemini-2.5-flash
            new ModelInfo("gemini-2.5-flash", 10, 500, 1, true, "Gemini 2.5 Flash - 主要模型"),
            
            // 备用模型1 - gemini-2.5-flash-lite
            new ModelInfo("gemini-2.5-flash-lite", 15, 500, 2, true, "Gemini 2.5 Flash Lite - 备用模型1"),
            
            // 备用模型2 - gemini-2.0-flash
            new ModelInfo("gemini-2.0-flash", 15, 1500, 3, true, "Gemini 2.0 Flash - 备用模型2"),
            
            // 备用模型3 - gemini-2.0-flash-lite
            new ModelInfo("gemini-2.0-flash-lite", 30, 1500, 4, true, "Gemini 2.0 Flash Lite - 备用模型3"),
            
            // 最后备用 - gemini-2.0-flash-preview-image-generation
            new ModelInfo("gemini-2.0-flash-preview-image-generation", 10, 1500, 5, true, "Gemini 2.0 Flash Preview Image Generation - 最后备用"),
            
            // 其他可用模型（保守估计限制）
            new ModelInfo("gemini-1.5-flash", 10, 500, 6, true, "Gemini 1.5 Flash"),
            new ModelInfo("gemini-1.5-flash-latest", 10, 500, 7, true, "Gemini 1.5 Flash Latest"),
            new ModelInfo("gemini-1.5-flash-002", 10, 500, 8, true, "Gemini 1.5 Flash 002"),
            new ModelInfo("gemini-1.5-flash-8b", 10, 500, 9, true, "Gemini 1.5 Flash 8B"),
            new ModelInfo("gemini-1.5-flash-8b-001", 10, 500, 10, true, "Gemini 1.5 Flash 8B 001"),
            new ModelInfo("gemini-1.5-flash-8b-latest", 10, 500, 11, true, "Gemini 1.5 Flash 8B Latest"),
            
            // Pro模型（更保守的限制）
            new ModelInfo("gemini-1.5-pro", 5, 200, 12, true, "Gemini 1.5 Pro"),
            new ModelInfo("gemini-1.5-pro-latest", 5, 200, 13, true, "Gemini 1.5 Pro Latest"),
            new ModelInfo("gemini-1.5-pro-002", 5, 200, 14, true, "Gemini 1.5 Pro 002"),
            
            // 实验性模型
            new ModelInfo("gemini-2.0-flash-exp", 10, 500, 15, true, "Gemini 2.0 Flash Experimental"),
            new ModelInfo("gemini-2.0-flash-001", 15, 1500, 16, true, "Gemini 2.0 Flash 001"),
            new ModelInfo("gemini-2.0-flash-lite-001", 30, 1500, 17, true, "Gemini 2.0 Flash Lite 001"),
            new ModelInfo("gemini-2.0-flash-lite-preview-02-05", 30, 1500, 18, true, "Gemini 2.0 Flash Lite Preview"),
            new ModelInfo("gemini-2.0-flash-lite-preview", 30, 1500, 19, true, "Gemini 2.0 Flash Lite Preview"),
            
            // 2.0 Pro模型
            new ModelInfo("gemini-2.0-pro-exp", 5, 200, 20, true, "Gemini 2.0 Pro Experimental"),
            new ModelInfo("gemini-2.0-pro-exp-02-05", 5, 200, 21, true, "Gemini 2.0 Pro Experimental 02-05"),
            
            // 思考模型
            new ModelInfo("gemini-2.0-flash-thinking-exp-01-21", 10, 500, 22, true, "Gemini 2.0 Flash Thinking Experimental"),
            new ModelInfo("gemini-2.0-flash-thinking-exp", 10, 500, 23, true, "Gemini 2.0 Flash Thinking Experimental"),
            new ModelInfo("gemini-2.0-flash-thinking-exp-1219", 10, 500, 24, true, "Gemini 2.0 Flash Thinking Experimental 1219"),
            
            // 其他特殊模型
            new ModelInfo("gemini-exp-1206", 5, 200, 25, true, "Gemini Experimental 1206"),
            new ModelInfo("learnlm-2.0-flash-experimental", 10, 500, 26, true, "LearnLM 2.0 Flash Experimental"),
            
            // Gemma模型
            new ModelInfo("gemma-3-1b-it", 20, 1000, 27, true, "Gemma 3 1B IT"),
            new ModelInfo("gemma-3-4b-it", 15, 800, 28, true, "Gemma 3 4B IT"),
            new ModelInfo("gemma-3-12b-it", 10, 500, 29, true, "Gemma 3 12B IT"),
            new ModelInfo("gemma-3-27b-it", 5, 200, 30, true, "Gemma 3 27B IT"),
            new ModelInfo("gemma-3n-e4b-it", 15, 800, 31, true, "Gemma 3N E4B IT"),
            new ModelInfo("gemma-3n-e2b-it", 20, 1000, 32, true, "Gemma 3N E2B IT")
        );
    }
    
    /**
     * 获取主要使用的模型列表（按用户指定的优先级）
     */
    public List<ModelInfo> getPrimaryModels() {
        return Arrays.asList(
            new ModelInfo("gemini-2.5-flash", 10, 500, 1, true, "Gemini 2.5 Flash - 主要模型"),
            new ModelInfo("gemini-2.5-flash-lite", 15, 500, 2, true, "Gemini 2.5 Flash Lite - 备用模型1"),
            new ModelInfo("gemini-2.0-flash", 15, 1500, 3, true, "Gemini 2.0 Flash - 备用模型2"),
            new ModelInfo("gemini-2.0-flash-lite", 30, 1500, 4, true, "Gemini 2.0 Flash Lite - 备用模型3"),
            new ModelInfo("gemini-2.0-flash-preview-image-generation", 10, 1500, 5, true, "Gemini 2.0 Flash Preview Image Generation - 最后备用")
        );
    }
    
    /**
     * 根据模型名称获取模型信息
     */
    public ModelInfo getModelInfo(String modelName) {
        return getAllModels().stream()
                .filter(model -> model.getModelName().equals(modelName))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取下一个可用的模型
     */
    public ModelInfo getNextAvailableModel(String currentModel) {
        List<ModelInfo> primaryModels = getPrimaryModels();
        
        // 找到当前模型的位置
        int currentIndex = -1;
        for (int i = 0; i < primaryModels.size(); i++) {
            if (primaryModels.get(i).getModelName().equals(currentModel)) {
                currentIndex = i;
                break;
            }
        }
        
        // 返回下一个可用模型
        if (currentIndex >= 0 && currentIndex < primaryModels.size() - 1) {
            return primaryModels.get(currentIndex + 1);
        }
        
        // 如果已经是最后一个模型，返回null
        return null;
    }
}
