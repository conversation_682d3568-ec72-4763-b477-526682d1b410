package com.shandai.xuan.service;

import com.shandai.xuan.dto.LiuYaoData;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试本卦内部信息(benguainformation)集成到AI分析功能
 */
@SpringBootTest
public class BenguainformationIntegrationTest {

    @Autowired
    private LiuYaoPromptTemplateService promptTemplateService;

    @Test
    public void testBenguainformationDataStructure() {
        // 创建包含benguainformation的测试数据
        LiuYaoData testData = createTestLiuYaoData();

        // 验证benguainformation是否被正确设置
        assertNotNull(testData.getBenguainformation(), "benguainformation应该被设置");
        assertEquals(6, testData.getBenguainformation().size(), "应该有6爻的信息");

        // 验证每一爻的信息格式
        for (int i = 0; i < 6; i++) {
            List<String> yaoInfo = testData.getBenguainformation().get(i);
            assertNotNull(yaoInfo, "第" + (i+1) + "爻信息不应为空");
            assertTrue(yaoInfo.size() >= 10, "每爻应该包含至少10个信息字段");
        }

        System.out.println("✓ benguainformation数据结构测试通过");
    }

    @Test
    public void testPromptTemplateWithBenguainformation() {
        // 创建测试数据
        LiuYaoData testData = createTestLiuYaoData();
        
        // 构建提示词
        String prompt = promptTemplateService.buildAnalysisPrompt(testData, "comprehensive");
        
        // 验证提示词包含详细的六爻信息
        assertNotNull(prompt, "提示词不应为空");

        // 打印完整提示词用于调试
        System.out.println("=== 完整提示词内容 ===");
        System.out.println(prompt);
        System.out.println("=== 提示词结束 ===");

        assertTrue(prompt.contains("六爻详解"), "应该包含六爻详解标题");

        // 检查是否包含动静信息
        boolean hasDongJing = prompt.contains("动爻") || prompt.contains("静爻") || prompt.contains("动") || prompt.contains("静");
        if (!hasDongJing) {
            System.out.println("警告：提示词中未找到动静信息");
        }

        // 检查是否包含世应信息
        boolean hasShiYing = prompt.contains("世") || prompt.contains("应");
        if (!hasShiYing) {
            System.out.println("警告：提示词中未找到世应信息");
        }

        // 检查是否包含旺衰信息
        boolean hasWangShuai = prompt.contains("旺衰");
        if (!hasWangShuai) {
            System.out.println("警告：提示词中未找到旺衰信息");
        }

        System.out.println("✓ 提示词模板测试完成");
        System.out.println("生成的提示词长度: " + prompt.length() + " 字符");
    }

    @Test
    public void testPromptTemplateWithoutBenguainformation() {
        // 创建没有benguainformation的测试数据
        LiuYaoData testData = createTestLiuYaoDataWithoutBenguainformation();
        
        // 构建提示词
        String prompt = promptTemplateService.buildAnalysisPrompt(testData, "comprehensive");
        
        // 验证降级逻辑正常工作
        assertNotNull(prompt, "提示词不应为空");
        assertTrue(prompt.contains("六爻详解"), "应该包含六爻详解标题");
        
        System.out.println("✓ 降级逻辑测试通过");
    }



    /**
     * 创建包含benguainformation的测试数据
     */
    private LiuYaoData createTestLiuYaoData() {
        List<List<String>> benguainformation = new ArrayList<>();
        
        // 模拟6爻的信息
        benguainformation.add(Arrays.asList("动爻", "化回头生", "非空", "", "化进", "世", "父母", "子", "水", "85%"));
        benguainformation.add(Arrays.asList("静爻", "", "非空", "", "", "", "兄弟", "寅", "木", "60%"));
        benguainformation.add(Arrays.asList("静爻", "", "非空", "", "", "", "官鬼", "辰", "土", "45%"));
        benguainformation.add(Arrays.asList("静爻", "", "非空", "", "", "应", "父母", "午", "火", "30%"));
        benguainformation.add(Arrays.asList("静爻", "", "非空", "", "", "", "兄弟", "申", "金", "70%"));
        benguainformation.add(Arrays.asList("静爻", "", "非空", "", "", "", "官鬼", "戌", "土", "55%"));
        
        return LiuYaoData.builder()
                .benGua("乾为天")
                .bianGua("天风姤")
                .occupy("考学")
                .dayGanZhi("甲子")
                .benguainformation(benguainformation)
                .build();
    }

    /**
     * 创建不包含benguainformation的测试数据
     */
    private LiuYaoData createTestLiuYaoDataWithoutBenguainformation() {
        return LiuYaoData.builder()
                .benGua("乾为天")
                .bianGua("天风姤")
                .occupy("考学")
                .dayGanZhi("甲子")
                .benGuaLiuYaoShiYing(Arrays.asList("", "", "", "应", "", "世"))
                .liuYaoYaoXiangMarkName(Arrays.asList("动", "", "", "", "", ""))
                .build();
    }
}
