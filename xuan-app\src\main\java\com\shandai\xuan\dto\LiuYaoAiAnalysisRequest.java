package com.shandai.xuan.dto;

import lombok.Data;

/**
 * 六爻AI分析请求
 */
@Data
public class LiuYaoAiAnalysisRequest {
    
    /**
     * 基础六爻请求数据
     */
    private LiuYaoRequest liuYaoRequest;
    
    /**
     * 用户问题/占事详情
     */
    private String question;
    
    /**
     * 分析类型
     */
    private String analysisType = "comprehensive"; // comprehensive, simple, detailed
    
    /**
     * 是否需要详细解释
     */
    private boolean needDetailedExplanation = true;
    
    /**
     * 用户ID（可选，用于个性化分析）
     */
    private String userId;
}
