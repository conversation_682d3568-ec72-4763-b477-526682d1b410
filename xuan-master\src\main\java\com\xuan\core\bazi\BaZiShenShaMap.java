package com.xuan.core.bazi;

import java.util.HashMap;
import java.util.Map;

/**
 * 八字-神煞常量
 *
 * <AUTHOR>
 */
public class BaZiShenShaMap {

    /* 常用神煞 */

    /**
     * 太极贵人（天干+地支为键）
     */
    public static final Map<String, String> TAI_JI_GUI_REN = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        /*
            查法：年干或日干+地支

                01、年干或日干为甲或乙，地支见子或午。
                02、年干或日干为丙或丁，地支见卯或酉。
                03、年干或日干为戊或己，地支见辰或戌或丑或未。
                04、年干或日干为庚或辛，地支见寅或亥。
                05、年干或日干为壬或癸，地支见巳或申。
         */ {
            put("甲子", "太极贵人");
            put("甲午", "太极贵人");
            put("乙子", "太极贵人");
            put("乙午", "太极贵人");
            put("丙卯", "太极贵人");
            put("丙酉", "太极贵人");
            put("丁卯", "太极贵人");
            put("丁酉", "太极贵人");
            put("戊辰", "太极贵人");
            put("戊戌", "太极贵人");
            put("戊丑", "太极贵人");
            put("戊未", "太极贵人");
            put("己辰", "太极贵人");
            put("己戌", "太极贵人");
            put("己丑", "太极贵人");
            put("己未", "太极贵人");
            put("庚寅", "太极贵人");
            put("庚亥", "太极贵人");
            put("辛寅", "太极贵人");
            put("辛亥", "太极贵人");
            put("壬巳", "太极贵人");
            put("壬申", "太极贵人");
            put("癸巳", "太极贵人");
            put("癸申", "太极贵人");
        }
    };

    /**
     * 天乙贵人（天干+地支为键）
     */
    public static final Map<String, String> TIAN_YI_GUI_REN = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        /*
            查法：年干或日干+地支

                01、年干或日干为甲，地支见丑或未。
                02、年干或日干为乙，地支见子或申。
                03、年干或日干为丙，地支见酉或亥。
                04、年干或日干为丁，地支见酉或亥。
                05、年干或日干为戊，地支见丑或未。
                06、年干或日干为己，地支见子或申。
                07、年干或日干为庚，地支见丑或未。
                08、年干或日干为辛，地支见寅或午。
                09、年干或日干为壬，地支见卯或巳。
                10、年干或日干为癸，地支见卯或巳。
         */ {
            put("甲丑", "天乙贵人");
            put("甲未", "天乙贵人");
            put("乙子", "天乙贵人");
            put("乙申", "天乙贵人");
            put("丙酉", "天乙贵人");
            put("丙亥", "天乙贵人");
            put("丁酉", "天乙贵人");
            put("丁亥", "天乙贵人");
            put("戊丑", "天乙贵人");
            put("戊未", "天乙贵人");
            put("己子", "天乙贵人");
            put("己申", "天乙贵人");
            put("庚丑", "天乙贵人");
            put("庚未", "天乙贵人");
            put("辛寅", "天乙贵人");
            put("辛午", "天乙贵人");
            put("壬卯", "天乙贵人");
            put("壬巳", "天乙贵人");
            put("癸卯", "天乙贵人");
            put("癸巳", "天乙贵人");
        }
    };

    /**
     * 福星贵人（天干+地支为键）
     */
    public static final Map<String, String> FU_XING_GUI_REN = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        /*
            查法：年干或日干+地支

                01、年干或日干为甲，地支见子或寅。
                02、年干或日干为乙，地支见丑或卯。
                03、年干或日干为丙，地支见子或寅。
                04、年干或日干为丁，地支见亥。
                05、年干或日干为戊，地支见申。
                06、年干或日干为己，地支见未。
                07、年干或日干为庚，地支见午。
                08、年干或日干为辛，地支见巳。
                09、年干或日干为壬，地支见辰。
                10、年干或日干为癸，地支见丑或卯。
         */ {
            put("甲子", "福星贵人");
            put("甲寅", "福星贵人");
            put("乙丑", "福星贵人");
            put("乙卯", "福星贵人");
            put("丙子", "福星贵人");
            put("丙寅", "福星贵人");
            put("丁亥", "福星贵人");
            put("戊申", "福星贵人");
            put("己未", "福星贵人");
            put("庚午", "福星贵人");
            put("辛巳", "福星贵人");
            put("壬辰", "福星贵人");
            put("癸丑", "福星贵人");
            put("癸卯", "福星贵人");
        }
    };

    /**
     * 文昌贵人（天干+地支为键）
     */
    public static final Map<String, String> WEN_CHANG_GUI_REN = new HashMap<String, String>() {
        private static final long serialVersionUID = -1;

        /*
            查法：年干或日干+地支

                01、年干或日干为甲，地支见巳。
                02、年干或日干为乙，地支见午。
                03、年干或日干为丙，地支见申。
                04、年干或日干为丁，地支见酉。
                05、年干或日干为戊，地支见申。
                06、年干或日干为己，地支见酉。
                07、年干或日干为庚，地支见亥。
                08、年干或日干为辛，地支见子。
                09、年干或日干为壬，地支见寅。
                10、年干或日干为癸，地支见卯。
         */ {
            put("甲巳", "文昌贵人");
            put("乙午", "文昌贵人");
            put("丙申", "文昌贵人");
            put("丁酉", "文昌贵人");
            put("戊申", "文昌贵人");
            put("己酉", "文昌贵人");
            put("庚亥", "文昌贵人");
            put("辛子", "文昌贵人");
            put("壬寅", "文昌贵人");
            put("癸卯", "文昌贵人");
        }
    };

    /**
     * 天厨贵人（天干+地支为键）
     */
    public static final Map<String, String> TIAN_CHU_GUI_REN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年干或日干+地支

                01、年干或日干为甲，地支见巳。
                02、年干或日干为乙，地支见午。
                03、年干或日干为丙，地支见巳。
                04、年干或日干为丁，地支见午。
                05、年干或日干为戊，地支见申。
                06、年干或日干为己，地支见酉。
                07、年干或日干为庚，地支见亥。
                08、年干或日干为辛，地支见子。
                09、年干或日干为壬，地支见寅。
                10、年干或日干为癸，地支见卯。
         */ {
            put("甲巳", "天厨贵人");
            put("乙午", "天厨贵人");
            put("丙巳", "天厨贵人");
            put("丁午", "天厨贵人");
            put("戊申", "天厨贵人");
            put("己酉", "天厨贵人");
            put("庚亥", "天厨贵人");
            put("辛子", "天厨贵人");
            put("壬寅", "天厨贵人");
            put("癸卯", "天厨贵人");
        }
    };

    /**
     * 国印（天干+地支为键）
     */
    public static final Map<String, String> GUO_YIN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年干或日干+地支

                01、年干或日干为甲，地支见戌。
                02、年干或日干为乙，地支见亥。
                03、年干或日干为丙，地支见丑。
                04、年干或日干为丁，地支见寅。
                05、年干或日干为戊，地支见丑。
                06、年干或日干为己，地支见寅。
                07、年干或日干为庚，地支见辰。
                08、年干或日干为辛，地支见巳。
                09、年干或日干为壬，地支见未。
                10、年干或日干为癸，地支见申。
         */ {
            put("甲戌", "国印");
            put("乙亥", "国印");
            put("丙丑", "国印");
            put("丁寅", "国印");
            put("戊丑", "国印");
            put("己寅", "国印");
            put("庚辰", "国印");
            put("辛巳", "国印");
            put("壬未", "国印");
            put("癸申", "国印");
        }
    };

    /**
     * 金舆（天干+地支为键）
     */
    public static final Map<String, String> JIN_YU = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年干或日干+地支

                01、年干或日干为甲，地支见辰。
                02、年干或日干为乙，地支见巳。
                03、年干或日干为丙，地支见未。
                04、年干或日干为丁，地支见申。
                05、年干或日干为戊，地支见未。
                06、年干或日干为己，地支见申。
                07、年干或日干为庚，地支见戌。
                08、年干或日干为辛，地支见亥。
                09、年干或日干为壬，地支见丑。
                10、年干或日干为癸，地支见寅。
         */ {
            put("甲辰", "金舆");
            put("乙巳", "金舆");
            put("丙未", "金舆");
            put("丁申", "金舆");
            put("戊未", "金舆");
            put("己申", "金舆");
            put("庚戌", "金舆");
            put("辛亥", "金舆");
            put("壬丑", "金舆");
            put("癸寅", "金舆");
        }
    };

    /**
     * 红艳煞（天干+地支为键）
     */
    public static final Map<String, String> HONG_YAN_SHA = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：日干+地支

                01、日干为甲，地支见午。
                02、日干为乙，地支见申。
                03、日干为丙，地支见寅。
                04、日干为丁，地支见未。
                05、日干为戊，地支见辰。
                06、日干为己，地支见辰。
                07、日干为庚，地支见戌。
                08、日干为辛，地支见酉。
                09、日干为壬，地支见子。
                10、日干为癸，地支见申。
         */ {
            put("甲午", "红艳煞");
            put("乙申", "红艳煞");
            put("丙寅", "红艳煞");
            put("丁未", "红艳煞");
            put("戊辰", "红艳煞");
            put("己辰", "红艳煞");
            put("庚戌", "红艳煞");
            put("辛酉", "红艳煞");
            put("壬子", "红艳煞");
            put("癸申", "红艳煞");
        }
    };

    /**
     * 羊刃（天干+地支为键）
     */
    public static final Map<String, String> YANG_REN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：日干+地支

                01、日干为甲，地支见卯。
                02、日干为乙，地支见寅。
                03、日干为丙，地支见午。
                04、日干为丁，地支见巳。
                05、日干为戊，地支见午。
                06、日干为己，地支见巳。
                07、日干为庚，地支见酉。
                08、日干为辛，地支见申。
                09、日干为壬，地支见子。
                10、日干为癸，地支见亥。
         */ {
            put("甲卯", "羊刃");
            put("乙寅", "羊刃");
            put("丙午", "羊刃");
            put("丁巳", "羊刃");
            put("戊午", "羊刃");
            put("己巳", "羊刃");
            put("庚酉", "羊刃");
            put("辛申", "羊刃");
            put("壬子", "羊刃");
            put("癸亥", "羊刃");
        }
    };

    /**
     * 飞刃（天干+地支为键）
     */
    public static final Map<String, String> FEI_REN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：日干+地支

                01、日干为甲，地支见酉。
                02、日干为乙，地支见寅。
                03、日干为丙，地支见子。
                04、日干为丁，地支见亥。
                05、日干为戊，地支见子。
                06、日干为己，地支见亥。
                07、日干为庚，地支见卯。
                08、日干为辛，地支见寅。
                09、日干为壬，地支见午。
                10、日干为癸，地支见巳。
         */ {
            put("甲酉", "飞刃");
            put("乙寅", "飞刃");
            put("丙子", "飞刃");
            put("丁亥", "飞刃");
            put("戊子", "飞刃");
            put("己亥", "飞刃");
            put("庚卯", "飞刃");
            put("辛寅", "飞刃");
            put("壬午", "飞刃");
            put("癸巳", "飞刃");
        }
    };

    /**
     * 流霞（天干+地支为键）
     */
    public static final Map<String, String> LIU_XIA = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：日干+地支

                01、日干为甲，地支见酉。
                02、日干为乙，地支见戌。
                03、日干为丙，地支见未。
                04、日干为丁，地支见申。
                05、日干为戊，地支见巳。
                06、日干为己，地支见午。
                07、日干为庚，地支见辰。
                08、日干为辛，地支见卯。
                09、日干为壬，地支见亥。
                10、日干为癸，地支见寅。
         */ {
            put("甲酉", "流霞");
            put("乙戌", "流霞");
            put("丙未", "流霞");
            put("丁申", "流霞");
            put("戊巳", "流霞");
            put("己午", "流霞");
            put("庚辰", "流霞");
            put("辛卯", "流霞");
            put("壬亥", "流霞");
            put("癸寅", "流霞");
        }
    };

    /**
     * 禄神（天干+地支为键）
     */
    public static final Map<String, String> LU_SHEN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：日干+地支

                01、日干为甲，地支见寅。
                02、日干为乙，地支见卯。
                03、日干为丙，地支见巳。
                04、日干为丁，地支见午。
                05、日干为戊，地支见巳。
                06、日干为己，地支见午。
                07、日干为庚，地支见申。
                08、日干为辛，地支见酉。
                09、日干为壬，地支见亥。
                10、日干为癸，地支见子。
         */ {
            put("甲寅", "禄神");
            put("乙卯", "禄神");
            put("丙巳", "禄神");
            put("丁午", "禄神");
            put("戊巳", "禄神");
            put("己午", "禄神");
            put("庚申", "禄神");
            put("辛酉", "禄神");
            put("壬亥", "禄神");
            put("癸子", "禄神");
        }
    };

//----------------------------------------------------------------------------------------------------------------------------------------------------

    /**
     * 驿马（地支+地支为键）
     */
    public static final Map<String, String> YI_MA = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支或日支+地支

                01、年支或日支为子，地支见寅。
                02、年支或日支为丑，地支见亥。
                03、年支或日支为寅，地支见申。
                04、年支或日支为卯，地支见巳。
                05、年支或日支为辰，地支见寅。
                06、年支或日支为巳，地支见亥。
                07、年支或日支为午，地支见申。
                08、年支或日支为未，地支见巳。
                09、年支或日支为申，地支见寅。
                10、年支或日支为酉，地支见亥。
                11、年支或日支为戌，地支见申。
                12、年支或日支为亥，地支见巳。
         */ {
            put("子寅", "驿马");
            put("丑亥", "驿马");
            put("寅申", "驿马");
            put("卯巳", "驿马");
            put("辰寅", "驿马");
            put("巳亥", "驿马");
            put("午申", "驿马");
            put("未巳", "驿马");
            put("申寅", "驿马");
            put("酉亥", "驿马");
            put("戌申", "驿马");
            put("亥巳", "驿马");
        }
    };

    /**
     * 劫煞（地支+地支为键）
     */
    public static final Map<String, String> JIE_SHA = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支或日支+地支

                01、年支或日支为子，地支见巳。
                02、年支或日支为丑，地支见寅。
                03、年支或日支为寅，地支见亥。
                04、年支或日支为卯，地支见申。
                05、年支或日支为辰，地支见巳。
                06、年支或日支为巳，地支见寅。
                07、年支或日支为午，地支见亥。
                08、年支或日支为未，地支见申。
                09、年支或日支为申，地支见巳。
                10、年支或日支为酉，地支见寅。
                11、年支或日支为戌，地支见亥。
                12、年支或日支为亥，地支见申。
         */ {
            put("子巳", "劫煞");
            put("丑寅", "劫煞");
            put("寅亥", "劫煞");
            put("卯申", "劫煞");
            put("辰巳", "劫煞");
            put("巳寅", "劫煞");
            put("午亥", "劫煞");
            put("未申", "劫煞");
            put("申巳", "劫煞");
            put("酉寅", "劫煞");
            put("戌亥", "劫煞");
            put("亥申", "劫煞");
        }
    };

    /**
     * 将星（地支+地支为键）
     */
    public static final Map<String, String> JIANG_XING = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支或日支+地支

                01、年支或日支为子，地支见子。
                02、年支或日支为丑，地支见酉。
                03、年支或日支为寅，地支见午。
                04、年支或日支为卯，地支见卯。
                05、年支或日支为辰，地支见子。
                06、年支或日支为巳，地支见酉。
                07、年支或日支为午，地支见午。
                08、年支或日支为未，地支见卯。
                09、年支或日支为申，地支见子。
                10、年支或日支为酉，地支见酉。
                11、年支或日支为戌，地支见午。
                12、年支或日支为亥，地支见卯。
         */ {
            put("子子", "将星");
            put("丑酉", "将星");
            put("寅午", "将星");
            put("卯卯", "将星");
            put("辰子", "将星");
            put("巳酉", "将星");
            put("午午", "将星");
            put("未卯", "将星");
            put("申子", "将星");
            put("酉酉", "将星");
            put("戌午", "将星");
            put("亥卯", "将星");
        }
    };

    /**
     * 桃花（地支+地支为键）
     */
    public static final Map<String, String> TAO_HUA = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支或日支+地支

                01、年支或日支为子，地支见酉。
                02、年支或日支为丑，地支见午。
                03、年支或日支为寅，地支见卯。
                04、年支或日支为卯，地支见子。
                05、年支或日支为辰，地支见酉。
                06、年支或日支为巳，地支见午。
                07、年支或日支为午，地支见卯。
                08、年支或日支为未，地支见子。
                09、年支或日支为申，地支见酉。
                10、年支或日支为酉，地支见午。
                11、年支或日支为戌，地支见卯。
                12、年支或日支为亥，地支见子。
         */ {
            put("子酉", "桃花");
            put("丑午", "桃花");
            put("寅卯", "桃花");
            put("卯子", "桃花");
            put("辰酉", "桃花");
            put("巳午", "桃花");
            put("午卯", "桃花");
            put("未子", "桃花");
            put("申酉", "桃花");
            put("酉午", "桃花");
            put("戌卯", "桃花");
            put("亥子", "桃花");
        }
    };

    /**
     * 亡神（地支+地支为键）
     */
    public static final Map<String, String> WANG_SHEN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支或日支+地支

                01、年支或日支为子，地支见亥。
                02、年支或日支为丑，地支见申。
                03、年支或日支为寅，地支见巳。
                04、年支或日支为卯，地支见寅。
                05、年支或日支为辰，地支见亥。
                06、年支或日支为巳，地支见申。
                07、年支或日支为午，地支见巳。
                08、年支或日支为未，地支见寅。
                09、年支或日支为申，地支见亥。
                10、年支或日支为酉，地支见申。
                11、年支或日支为戌，地支见巳。
                12、年支或日支为亥，地支见寅。
         */ {
            put("子亥", "亡神");
            put("丑申", "亡神");
            put("寅巳", "亡神");
            put("卯寅", "亡神");
            put("辰亥", "亡神");
            put("巳申", "亡神");
            put("午巳", "亡神");
            put("未寅", "亡神");
            put("申亥", "亡神");
            put("酉申", "亡神");
            put("戌巳", "亡神");
            put("亥寅", "亡神");
        }
    };

    /**
     * 吊客（地支+地支为键）
     */
    public static final Map<String, String> DIAO_KE = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支或日支+地支

                01、年支或日支为子，地支见戌。
                02、年支或日支为丑，地支见亥。
                03、年支或日支为寅，地支见子。
                04、年支或日支为卯，地支见丑。
                05、年支或日支为辰，地支见寅。
                06、年支或日支为巳，地支见卯。
                07、年支或日支为午，地支见辰。
                08、年支或日支为未，地支见巳。
                09、年支或日支为申，地支见午。
                10、年支或日支为酉，地支见未。
                11、年支或日支为戌，地支见申。
                12、年支或日支为亥，地支见酉。
         */ {
            put("子戌", "吊客");
            put("丑亥", "吊客");
            put("寅子", "吊客");
            put("卯丑", "吊客");
            put("辰寅", "吊客");
            put("巳卯", "吊客");
            put("午辰", "吊客");
            put("未巳", "吊客");
            put("申午", "吊客");
            put("酉未", "吊客");
            put("戌申", "吊客");
            put("亥酉", "吊客");
        }
    };

    /**
     * 披麻（地支+地支为键）
     */
    public static final Map<String, String> PI_MA = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支或日支+地支

                01、年支或日支为子，地支见酉。
                02、年支或日支为丑，地支见戌。
                03、年支或日支为寅，地支见亥。
                04、年支或日支为卯，地支见子。
                05、年支或日支为辰，地支见丑。
                06、年支或日支为巳，地支见寅。
                07、年支或日支为午，地支见卯。
                08、年支或日支为未，地支见辰。
                09、年支或日支为申，地支见巳。
                10、年支或日支为酉，地支见午。
                11、年支或日支为戌，地支见未。
                12、年支或日支为亥，地支见申。
         */ {
            put("子酉", "披麻");
            put("丑戌", "披麻");
            put("寅亥", "披麻");
            put("卯子", "披麻");
            put("辰丑", "披麻");
            put("巳寅", "披麻");
            put("午卯", "披麻");
            put("未辰", "披麻");
            put("申巳", "披麻");
            put("酉午", "披麻");
            put("戌未", "披麻");
            put("亥申", "披麻");
        }
    };

    /**
     * 天喜（地支+地支为键）
     */
    public static final Map<String, String> TIAN_XI = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支+地支

                01、年支为子，地支见酉。
                02、年支为丑，地支见申。
                03、年支为寅，地支见未。
                04、年支为卯，地支见午。
                05、年支为辰，地支见巳。
                06、年支为巳，地支见辰。
                07、年支为午，地支见卯。
                08、年支为未，地支见寅。
                09、年支为申，地支见丑。
                10、年支为酉，地支见子。
                11、年支为戌，地支见亥。
                12、年支为亥，地支见戌。
         */ {
            put("子酉", "天喜");
            put("丑申", "天喜");
            put("寅未", "天喜");
            put("卯午", "天喜");
            put("辰巳", "天喜");
            put("巳辰", "天喜");
            put("午卯", "天喜");
            put("未寅", "天喜");
            put("申丑", "天喜");
            put("酉子", "天喜");
            put("戌亥", "天喜");
            put("亥戌", "天喜");
        }
    };

    /**
     * 勾绞煞（地支+地支为键）
     */
    public static final Map<String, String> GOU_JIAO_SHA = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支+地支

                01、年支为子，地支见卯。
                02、年支为丑，地支见辰。
                03、年支为寅，地支见巳。
                04、年支为卯，地支见午。
                05、年支为辰，地支见未。
                06、年支为巳，地支见申。
                07、年支为午，地支见酉。
                08、年支为未，地支见戌。
                09、年支为申，地支见亥。
                10、年支为酉，地支见子。
                11、年支为戌，地支见丑。
                12、年支为亥，地支见寅。
         */ {
            put("子卯", "勾绞煞");
            put("丑辰", "勾绞煞");
            put("寅巳", "勾绞煞");
            put("卯午", "勾绞煞");
            put("辰未", "勾绞煞");
            put("巳申", "勾绞煞");
            put("午酉", "勾绞煞");
            put("未戌", "勾绞煞");
            put("申亥", "勾绞煞");
            put("酉子", "勾绞煞");
            put("戌丑", "勾绞煞");
            put("亥寅", "勾绞煞");
        }
    };

    /**
     * 红鸾（地支+地支为键）
     */
    public static final Map<String, String> HONG_LUAN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支+地支

                01、年支为子，地支见卯。
                02、年支为丑，地支见寅。
                03、年支为寅，地支见丑。
                04、年支为卯，地支见子。
                05、年支为辰，地支见亥。
                06、年支为巳，地支见戌。
                07、年支为午，地支见酉。
                08、年支为未，地支见申。
                09、年支为申，地支见未。
                10、年支为酉，地支见午。
                11、年支为戌，地支见巳。
                12、年支为亥，地支见辰。
         */ {
            put("子卯", "红鸾");
            put("丑寅", "红鸾");
            put("寅丑", "红鸾");
            put("卯子", "红鸾");
            put("辰亥", "红鸾");
            put("巳戌", "红鸾");
            put("午酉", "红鸾");
            put("未申", "红鸾");
            put("申未", "红鸾");
            put("酉午", "红鸾");
            put("戌巳", "红鸾");
            put("亥辰", "红鸾");
        }
    };

    /**
     * 丧门（地支+地支为键）
     */
    public static final Map<String, String> SANG_MEN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支+地支

                01、年支为子，地支见寅。
                02、年支为丑，地支见卯。
                03、年支为寅，地支见辰。
                04、年支为卯，地支见巳。
                05、年支为辰，地支见午。
                06、年支为巳，地支见未。
                07、年支为午，地支见申。
                08、年支为未，地支见酉。
                09、年支为申，地支见戌。
                10、年支为酉，地支见寅。
                11、年支为戌，地支见卯。
                12、年支为亥，地支见辰。
         */ {
            put("子寅", "丧门");
            put("丑卯", "丧门");
            put("寅辰", "丧门");
            put("卯巳", "丧门");
            put("辰午", "丧门");
            put("巳未", "丧门");
            put("午申", "丧门");
            put("未酉", "丧门");
            put("申戌", "丧门");
            put("酉寅", "丧门");
            put("戌卯", "丧门");
            put("亥辰", "丧门");
        }
    };

    /**
     * 灾煞（地支+地支为键）
     */
    public static final Map<String, String> ZAI_SHA = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支+地支

                01、年支为子，地支见午。
                02、年支为丑，地支见卯。
                03、年支为寅，地支见子。
                04、年支为卯，地支见酉。
                05、年支为辰，地支见午。
                06、年支为巳，地支见卯。
                07、年支为午，地支见子。
                08、年支为未，地支见酉。
                09、年支为申，地支见午。
                10、年支为酉，地支见卯。
                11、年支为戌，地支见子。
                12、年支为亥，地支见酉。
         */ {
            put("子午", "灾煞");
            put("丑卯", "灾煞");
            put("寅子", "灾煞");
            put("卯酉", "灾煞");
            put("辰午", "灾煞");
            put("巳卯", "灾煞");
            put("午子", "灾煞");
            put("未酉", "灾煞");
            put("申午", "灾煞");
            put("酉卯", "灾煞");
            put("戌子", "灾煞");
            put("亥酉", "灾煞");
        }
    };

    /**
     * 孤辰（地支+地支为键）
     */
    public static final Map<String, String> GU_CHEN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支+地支

                01、年支为子，地支见寅。
                02、年支为丑，地支见寅。
                03、年支为寅，地支见巳。
                04、年支为卯，地支见巳。
                05、年支为辰，地支见巳。
                06、年支为巳，地支见申。
                07、年支为午，地支见申。
                08、年支为未，地支见申。
                09、年支为申，地支见亥。
                10、年支为酉，地支见亥。
                11、年支为戌，地支见亥。
                12、年支为亥，地支见寅。
         */ {
            put("子寅", "孤辰");
            put("丑寅", "孤辰");
            put("寅巳", "孤辰");
            put("卯巳", "孤辰");
            put("辰巳", "孤辰");
            put("巳申", "孤辰");
            put("午申", "孤辰");
            put("未申", "孤辰");
            put("申亥", "孤辰");
            put("酉亥", "孤辰");
            put("戌亥", "孤辰");
            put("亥寅", "孤辰");
        }
    };

    /**
     * 寡宿（地支+地支为键）
     */
    public static final Map<String, String> SU_GUA = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支+地支

                01、年支为子，地支见戌。
                02、年支为丑，地支见戌。
                03、年支为寅，地支见丑。
                04、年支为卯，地支见丑。
                05、年支为辰，地支见丑。
                06、年支为巳，地支见辰。
                07、年支为午，地支见辰。
                08、年支为未，地支见辰。
                09、年支为申，地支见未。
                10、年支为酉，地支见未。
                11、年支为戌，地支见未。
                12、年支为亥，地支见戌。
         */ {
            put("子戌", "寡宿");
            put("丑戌", "寡宿");
            put("寅丑", "寡宿");
            put("卯丑", "寡宿");
            put("辰丑", "寡宿");
            put("巳辰", "寡宿");
            put("午辰", "寡宿");
            put("未辰", "寡宿");
            put("申未", "寡宿");
            put("酉未", "寡宿");
            put("戌未", "寡宿");
            put("亥戌", "寡宿");
        }
    };

    /**
     * 元辰（地支+地支），阳男阴女
     */
    public static final Map<String, String> YUAN_CHEN_YANG_NAN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支+地支

                01、年支为子，地支见未。
                02、年支为丑，地支见申。
                03、年支为寅，地支见酉。
                04、年支为卯，地支见戌。
                05、年支为辰，地支见亥。
                06、年支为巳，地支见子。
                07、年支为午，地支见丑。
                08、年支为未，地支见寅。
                09、年支为申，地支见卯。
                10、年支为酉，地支见辰。
                11、年支为戌，地支见巳。
                12、年支为亥，地支见午。
         */ {
            put("子未", "元辰");
            put("丑申", "元辰");
            put("寅酉", "元辰");
            put("卯戌", "元辰");
            put("辰亥", "元辰");
            put("巳子", "元辰");
            put("午丑", "元辰");
            put("未寅", "元辰");
            put("申卯", "元辰");
            put("酉辰", "元辰");
            put("戌巳", "元辰");
            put("亥午", "元辰");
        }
    };

    /**
     * 元辰（地支+地支），阴男阳女
     */
    public static final Map<String, String> YUAN_CHEN_YIN_NAN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支+地支

                01、年支为子，地支见巳。
                02、年支为丑，地支见午。
                03、年支为寅，地支见未。
                04、年支为卯，地支见申。
                05、年支为辰，地支见酉。
                06、年支为巳，地支见戌。
                07、年支为午，地支见亥。
                08、年支为未，地支见子。
                09、年支为申，地支见丑。
                10、年支为酉，地支见寅。
                11、年支为戌，地支见卯。
                12、年支为亥，地支见辰。
         */ {
            put("子巳", "元辰");
            put("丑午", "元辰");
            put("寅未", "元辰");
            put("卯申", "元辰");
            put("辰酉", "元辰");
            put("巳戌", "元辰");
            put("午亥", "元辰");
            put("未子", "元辰");
            put("申丑", "元辰");
            put("酉寅", "元辰");
            put("戌卯", "元辰");
            put("亥辰", "元辰");
        }
    };

    /**
     * 血刃（地支+地支为键）
     */
    public static final Map<String, String> XUE_REN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：月支+地支

                01、月支为子，地支见午。
                02、月支为丑，地支见子。
                03、月支为寅，地支见丑。
                04、月支为卯，地支见未。
                05、月支为辰，地支见寅。
                06、月支为巳，地支见申。
                07、月支为午，地支见卯。
                08、月支为未，地支见酉。
                09、月支为申，地支见辰。
                10、月支为酉，地支见戌。
                11、月支为戌，地支见巳。
                12、月支为亥，地支见亥。
         */ {
            put("子午", "血刃");
            put("丑子", "血刃");
            put("寅丑", "血刃");
            put("卯未", "血刃");
            put("辰寅", "血刃");
            put("巳申", "血刃");
            put("午卯", "血刃");
            put("未酉", "血刃");
            put("申辰", "血刃");
            put("酉戌", "血刃");
            put("戌巳", "血刃");
            put("亥亥", "血刃");
        }
    };

    /**
     * 天医（地支+地支为键）
     */
    public static final Map<String, String> TIAN_YI = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：月支+地支

                01、月支为子，地支见亥。
                02、月支为丑，地支见子。
                03、月支为寅，地支见丑。
                04、月支为卯，地支见寅。
                05、月支为辰，地支见卯。
                06、月支为巳，地支见辰。
                07、月支为午，地支见巳。
                08、月支为未，地支见午。
                09、月支为申，地支见未。
                10、月支为酉，地支见申。
                11、月支为戌，地支见酉。
                12、月支为亥，地支见戌。
         */ {
            put("子亥", "天医");
            put("丑亥", "天医");
            put("寅亥", "天医");
            put("卯亥", "天医");
            put("辰亥", "天医");
            put("巳亥", "天医");
            put("午亥", "天医");
            put("未亥", "天医");
            put("申亥", "天医");
            put("酉亥", "天医");
            put("戌亥", "天医");
            put("亥亥", "天医");
        }
    };

    /**
     * 词馆（年柱纳音五行+地支为键），禄命法
     */
    public static final Map<String, String> CI_GUAN_LU_MING = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年柱纳音五行+月支或日支或时支

                01、年柱纳音五行为木，月支或日支或时支见寅。（庚寅为正词馆）
                02、年柱纳音五行为火，月支或日支或时支见巳。（乙巳为正词馆）
                03、年柱纳音五行为土，月支或日支或时支见亥。（丁亥为正词馆）
                04、年柱纳音五行为金，月支或日支或时支见申。（壬申为正词馆）
                05、年柱纳音五行为水，月支或日支或时支见亥。（癸亥为正词馆）
         */ {
            put("木寅", "词馆");
            put("火巳", "词馆");
            put("土亥", "词馆");
            put("金申", "词馆");
            put("水亥", "词馆");
        }
    };

    /**
     * 词馆（天干+天干或地支为键），子平法
     */
    public static final Map<String, String> CI_GUAN_ZI_PING = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年干或日干+天干或地支

                01、年干或日干为甲，其余天干或地支见己或亥。
                02、年干或日干为乙，其余天干或地支见壬或午。
                03、年干或日干为丙，其余天干或地支见丙或寅。
                04、年干或日干为丁，其余天干或地支见丁或酉。
                05、年干或日干为戊，其余天干或地支见戊或寅。
                06、年干或日干为己，其余天干或地支见己或酉。
                07、年干或日干为庚，其余天干或地支见辛或巳。
                08、年干或日干为辛，其余天干或地支见甲或子。
                09、年干或日干为壬，其余天干或地支见甲或申。
                10、年干或日干为癸，其余天干或地支见乙或卯。
         */ {
            put("甲己", "词馆");
            put("甲亥", "词馆");
            put("乙壬", "词馆");
            put("乙午", "词馆");
            put("丙丙", "词馆");
            put("丙寅", "词馆");
            put("丁丁", "词馆");
            put("丁酉", "词馆");
            put("戊戊", "词馆");
            put("戊寅", "词馆");
            put("己己", "词馆");
            put("己酉", "词馆");
            put("庚辛", "词馆");
            put("庚巳", "词馆");
            put("辛甲", "词馆");
            put("辛子", "词馆");
            put("壬甲", "词馆");
            put("壬申", "词馆");
            put("癸乙", "词馆");
            put("癸卯", "词馆");
        }
    };

    /**
     * 学堂（年柱纳音五行+地支为键），禄命法
     */
    public static final Map<String, String> XUE_TANG_LU_MING = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年柱纳音五行+月支或日支或时支

                01、年柱纳音五行为木，月支或日支或时支见亥。（己亥为正学堂）
                02、年柱纳音五行为火，月支或日支或时支见寅。（丙寅为正学堂）
                03、年柱纳音五行为土，月支或日支或时支见申。（戊申为正学堂）
                04、年柱纳音五行为金，月支或日支或时支见巳。（辛巳为正学堂）
                05、年柱纳音五行为水，月支或日支或时支见申。（甲申为正学堂）
         */ {
            put("木亥", "学堂");
            put("火寅", "学堂");
            put("土申", "学堂");
            put("金巳", "学堂");
            put("水申", "学堂");
        }
    };

    /**
     * 学堂（天干+天干或地支为键），子平法
     */
    public static final Map<String, String> XUE_TANG_ZI_PING = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年干或日干+天干或地支

                01、年干或日干为甲，其余天干或地支见己或亥。
                02、年干或日干为乙，其余天干或地支见壬或午。
                03、年干或日干为丙，其余天干或地支见丙或寅。
                04、年干或日干为丁，其余天干或地支见丁或酉。
                05、年干或日干为戊，其余天干或地支见戊或寅。
                06、年干或日干为己，其余天干或地支见己或酉。
                07、年干或日干为庚，其余天干或地支见辛或巳。
                08、年干或日干为辛，其余天干或地支见甲或子。
                09、年干或日干为壬，其余天干或地支见甲或申。
                10、年干或日干为癸，其余天干或地支见乙或卯。
         */ {
            put("甲己", "学堂");
            put("甲亥", "学堂");
            put("乙壬", "学堂");
            put("乙午", "学堂");
            put("丙丙", "学堂");
            put("丙寅", "学堂");
            put("丁丁", "学堂");
            put("丁酉", "学堂");
            put("戊戊", "学堂");
            put("戊寅", "学堂");
            put("己己", "学堂");
            put("己酉", "学堂");
            put("庚辛", "学堂");
            put("庚巳", "学堂");
            put("辛甲", "学堂");
            put("辛子", "学堂");
            put("壬甲", "学堂");
            put("壬申", "学堂");
            put("癸乙", "学堂");
            put("癸卯", "学堂");
        }
    };

    /**
     * 天赦（地支+干支为键）
     */
    public static final Map<String, String> TIAN_SHE = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：月支+日干支

                01、月支为子，日干支见甲子。
                02、月支为丑，日干支见甲子。
                03、月支为寅，日干支见戊寅。
                04、月支为卯，日干支见戊寅。
                05、月支为辰，日干支见戊寅。
                06、月支为巳，日干支见甲午。
                07、月支为午，日干支见甲午。
                08、月支为未，日干支见甲午。
                09、月支为申，日干支见戊申。
                10、月支为酉，日干支见戊申。
                11、月支为戌，日干支见戊申。
                12、月支为亥，日干支见甲子。
         */ {
            put("子甲子", "天赦");
            put("丑甲子", "天赦");
            put("寅戊寅", "天赦");
            put("卯戊寅", "天赦");
            put("辰戊寅", "天赦");
            put("巳甲午", "天赦");
            put("午甲午", "天赦");
            put("未甲午", "天赦");
            put("申戊申", "天赦");
            put("酉戊申", "天赦");
            put("戌戊申", "天赦");
            put("亥甲子", "天赦");
        }
    };

    /**
     * 天转（地支+干支为键）
     */
    public static final Map<String, String> TIAN_ZHUAN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：月支+日干支

                01、月支为子，日干支见壬子。
                02、月支为丑，日干支见壬子。
                03、月支为寅，日干支见乙卯。
                04、月支为卯，日干支见乙卯。
                05、月支为辰，日干支见乙卯。
                06、月支为巳，日干支见丙午。
                07、月支为午，日干支见丙午。
                08、月支为未，日干支见丙午。
                09、月支为申，日干支见辛酉。
                10、月支为酉，日干支见辛酉。
                11、月支为戌，日干支见辛酉。
                12、月支为亥，日干支见壬子。
         */ {
            put("子壬子", "天转");
            put("丑壬子", "天转");
            put("寅乙卯", "天转");
            put("卯乙卯", "天转");
            put("辰乙卯", "天转");
            put("巳丙午", "天转");
            put("午丙午", "天转");
            put("未丙午", "天转");
            put("申辛酉", "天转");
            put("酉辛酉", "天转");
            put("戌辛酉", "天转");
            put("亥壬子", "天转");
        }
    };

    /**
     * 地转（地支+干支为键）
     */
    public static final Map<String, String> DI_ZHUAN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：月支+日干支

                01、月支为子，日干支见丙子。
                02、月支为丑，日干支见丙子。
                03、月支为寅，日干支见辛卯。
                04、月支为卯，日干支见辛卯。
                05、月支为辰，日干支见辛卯。
                06、月支为巳，日干支见戊午。
                07、月支为午，日干支见戊午。
                08、月支为未，日干支见戊午。
                09、月支为申，日干支见癸酉。
                10、月支为酉，日干支见癸酉。
                11、月支为戌，日干支见癸酉。
                12、月支为亥，日干支见丙子。
         */ {
            put("子丙子", "地转");
            put("丑丙子", "地转");
            put("寅辛卯", "地转");
            put("卯辛卯", "地转");
            put("辰辛卯", "地转");
            put("巳戊午", "地转");
            put("午戊午", "地转");
            put("未戊午", "地转");
            put("申癸酉", "地转");
            put("酉癸酉", "地转");
            put("戌癸酉", "地转");
            put("亥丙子", "地转");
        }
    };

//----------------------------------------------------------------------------------------------------------------------------------------------------

    /**
     * 月德贵人（地支+天干为键）
     */
    public static final Map<String, String> YUE_DE_GUI_REN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：月支+天干

                01、月支为子，天干见壬。
                02、月支为丑，天干见庚。
                03、月支为寅，天干见丙。
                04、月支为卯，天干见甲。
                05、月支为辰，天干见壬。
                06、月支为巳，天干见庚。
                07、月支为午，天干见丙。
                08、月支为未，天干见甲。
                09、月支为申，天干见壬。
                10、月支为酉，天干见庚。
                11、月支为戌，天干见丙。
                12、月支为亥，天干见甲。
         */ {
            put("子壬", "月德贵人");
            put("丑庚", "月德贵人");
            put("寅丙", "月德贵人");
            put("卯甲", "月德贵人");
            put("辰壬", "月德贵人");
            put("巳庚", "月德贵人");
            put("午丙", "月德贵人");
            put("未甲", "月德贵人");
            put("申壬", "月德贵人");
            put("酉庚", "月德贵人");
            put("戌丙", "月德贵人");
            put("亥甲", "月德贵人");
        }
    };

    /**
     * 德秀贵人（地支+天干为键）
     */
    public static final Map<String, String> DE_XIU_GUI_REN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：月支+天干

                01、月支为子，天干见甲或丙或戊或己或辛或壬或癸。
                02、月支为丑，天干见乙或庚或辛。
                03、月支为寅，天干见丙或丁或戊或癸。
                04、月支为卯，天干见甲或乙或丁或壬。
                05、月支为辰，天干见甲或丙或戊或己或辛或壬或癸。
                06、月支为巳，天干见乙或庚或辛。
                07、月支为午，天干见丙或丁或戊或癸。
                08、月支为未，天干见甲或乙或丁或壬。
                09、月支为申，天干见甲或丙或戊或己或辛或壬或癸。
                10、月支为酉，天干见乙或庚或辛。
                11、月支为戌，天干见丙或丁或戊或癸。
                12、月支为亥，天干见甲或乙或丁或壬'。
         */ {
            put("子甲", "德秀贵人");
            put("子丙", "德秀贵人");
            put("子戊", "德秀贵人");
            put("子己", "德秀贵人");
            put("子辛", "德秀贵人");
            put("子壬", "德秀贵人");
            put("子癸", "德秀贵人");
            put("丑乙", "德秀贵人");
            put("丑庚", "德秀贵人");
            put("丑辛", "德秀贵人");
            put("寅丙", "德秀贵人");
            put("寅丁", "德秀贵人");
            put("寅戊", "德秀贵人");
            put("寅癸", "德秀贵人");
            put("卯甲", "德秀贵人");
            put("卯乙", "德秀贵人");
            put("卯丁", "德秀贵人");
            put("卯壬", "德秀贵人");
            put("辰甲", "德秀贵人");
            put("辰丙", "德秀贵人");
            put("辰戊", "德秀贵人");
            put("辰己", "德秀贵人");
            put("辰辛", "德秀贵人");
            put("辰壬", "德秀贵人");
            put("辰癸", "德秀贵人");
            put("巳乙", "德秀贵人");
            put("巳庚", "德秀贵人");
            put("巳辛", "德秀贵人");
            put("午丙", "德秀贵人");
            put("午丁", "德秀贵人");
            put("午戊", "德秀贵人");
            put("午癸", "德秀贵人");
            put("未甲", "德秀贵人");
            put("未乙", "德秀贵人");
            put("未丁", "德秀贵人");
            put("未壬", "德秀贵人");
            put("申甲", "德秀贵人");
            put("申丙", "德秀贵人");
            put("申戊", "德秀贵人");
            put("申己", "德秀贵人");
            put("申辛", "德秀贵人");
            put("申壬", "德秀贵人");
            put("申癸", "德秀贵人");
            put("酉乙", "德秀贵人");
            put("酉庚", "德秀贵人");
            put("酉辛", "德秀贵人");
            put("戌丙", "德秀贵人");
            put("戌丁", "德秀贵人");
            put("戌戊", "德秀贵人");
            put("戌癸", "德秀贵人");
            put("亥甲", "德秀贵人");
            put("亥乙", "德秀贵人");
            put("亥丁", "德秀贵人");
            put("亥壬", "德秀贵人");
        }
    };

    /**
     * 天德贵人（地支+天干或地支为键）
     */
    public static final Map<String, String> TIAN_DE_GUI_REN = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：月支+天干或地支

                01、月支为子，地支见巳。
                02、月支为丑，天干见庚。
                03、月支为寅，天干见丁。
                04、月支为卯，地支见申。
                05、月支为辰，天干见壬。
                06、月支为巳，天干见辛。
                07、月支为午，地支见亥。
                08、月支为未，天干见甲。
                09、月支为申，天干见癸。
                10、月支为酉，地支见寅。
                11、月支为戌，天干见丙。
                12、月支为亥，天干见乙。
         */ {
            put("子巳", "天德贵人");
            put("丑庚", "天德贵人");
            put("寅丁", "天德贵人");
            put("卯申", "天德贵人");
            put("辰壬", "天德贵人");
            put("巳辛", "天德贵人");
            put("午亥", "天德贵人");
            put("未甲", "天德贵人");
            put("申癸", "天德贵人");
            put("酉寅", "天德贵人");
            put("戌丙", "天德贵人");
            put("亥乙", "天德贵人");
        }
    };

    /**
     * 拱禄（干支+干支+地支为键）
     */
    public static final Map<String, String> GONG_LU = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：日干支+时干支+地支

                1、日干支为癸亥，时干支为癸丑，地支见子。
                2、日干支为癸丑，时干支为癸亥，地支见子。
                3、日干支为丁巳，时干支为丁未，地支见午。
                4、日干支为己未，时干支为己巳，地支见午。
                5、日干支为戊辰，时干支为戊午，地支见巳。
         */ {
            put("癸亥癸丑子", "拱禄");
            put("癸丑癸亥子", "拱禄");
            put("丁巳丁未午", "拱禄");
            put("己未己巳午", "拱禄");
            put("戊辰戊午巳", "拱禄");
        }
    };

    /**
     * 华盖（地支+地支为键）
     */
    public static final Map<String, String> HUA_GAI = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：年支或日支+其余地支

                01、年支或日支为子，其余地支见辰。
                02、年支或日支为丑，其余地支见丑。
                03、年支或日支为寅，其余地支见戌。
                04、年支或日支为卯，其余地支见未。
                05、年支或日支为辰，其余地支见辰。
                06、年支或日支为巳，其余地支见丑。
                07、年支或日支为午，其余地支见戌。
                08、年支或日支为未，其余地支见未。
                09、年支或日支为申，其余地支见辰。
                10、年支或日支为酉，其余地支见丑。
                11、年支或日支为戌，其余地支见戌。
                12、年支或日支为亥，其余地支见未。
         */ {
            put("子辰", "华盖");
            put("丑丑", "华盖");
            put("寅戌", "华盖");
            put("卯未", "华盖");
            put("辰辰", "华盖");
            put("巳丑", "华盖");
            put("午戌", "华盖");
            put("未未", "华盖");
            put("申辰", "华盖");
            put("酉丑", "华盖");
            put("戌戌", "华盖");
            put("亥未", "华盖");
        }
    };

    /**
     * 童子煞（季节+地支为键，年柱纳音五行+地支为键）
     */
    public static final Map<String, String> TONG_ZI_SHA = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：季节+日支或时支，年柱纳音五行+日支或时支

                01、季节为春或秋：日支或时支见寅或子。
                02、季节为夏或冬：日支或时支见卯或未或辰。
                03、年柱纳音五行为木或金：日支或时支见午或卯。
                04、年柱纳音五行为水或火：日支或时支见酉或戌。
                05、年柱纳音五行为土：日支或时支见辰或巳。
         */ {
            put("春寅", "童子煞");
            put("春子", "童子煞");
            put("秋寅", "童子煞");
            put("秋子", "童子煞");
            put("木午", "童子煞");
            put("木卯", "童子煞");
            put("金午", "童子煞");
            put("金卯", "童子煞");
            put("水酉", "童子煞");
            put("水戌", "童子煞");
            put("火酉", "童子煞");
            put("火戌", "童子煞");
            put("土辰", "童子煞");
            put("土巳", "童子煞");
        }
    };

//----------------------------------------------------------------------------------------------------------------------------------------------------

    /**
     * 四废日（季节+干支为键）
     */
    public static final Map<String, String> SI_FEI_RI = new HashMap<String, String>() {

        private static final long serialVersionUID = -1;

        /*
            查法：季节+日干支

                01、季节为春：日干支见庚申或辛酉。
                02、季节为夏：日干支见壬子或癸亥。
                03、季节为秋：日干支见甲寅或乙卯。
                04、季节为冬：日干支见丙午或丁巳。
         */ {
            put("春庚申", "四废日");
            put("春辛酉", "四废日");
            put("夏壬子", "四废日");
            put("夏癸亥", "四废日");
            put("秋甲寅", "四废日");
            put("秋乙卯", "四废日");
            put("冬丙午", "四废日");
            put("冬丁巳", "四废日");
        }
    };

    /**
     * 十恶大败（日柱）
     */
    public static final String[] SHI_E_DA_BAI = {"甲辰", "乙巳", "丙申", "丁亥", "戊戌", "己丑", "庚辰", "辛巳", "壬申", "癸亥"};

    /**
     * 阴差阳错（日柱）
     */
    public static final String[] YIN_CHA_YANG_CUO = {"丙午", "丙子", "丁未", "丁丑", "戊申", "戊寅", "辛酉", "辛卯", "壬戌", "壬辰", "癸巳", "癸亥"};

    /**
     * 六秀日（日柱）
     */
    public static final String[] LIU_XIU_RI = {"丙午", "丁未", "戊子", "戊午", "己丑", "己未"};

    /**
     * 十灵日（日柱）
     */
    public static final String[] SHI_LING_RI = {"甲辰", "乙亥", "丙辰", "丁酉", "戊午", "庚戌", "庚寅", "辛亥", "壬寅", "癸未"};

    /**
     * 魁罡日（日柱）
     */
    public static final String[] KUI_GANG_RI = {"戊戌", "庚辰", "庚戌", "壬辰"};

    /**
     * 八专日（日柱）
     */
    public static final String[] BA_ZHUAN_RI = {"甲寅", "乙卯", "丁未", "戊戌", "己未", "庚申", "辛酉", "癸丑"};

    /**
     * 九丑日（日柱）
     */
    public static final String[] JIU_CHOU_RI = {"丁酉", "戊子", "戊午", "己卯", "己酉", "辛卯", "辛酉", "壬子", "壬午"};

    /**
     * 孤鸾煞（日柱）
     */
    public static final String[] GU_LUAN_SHA = {"甲寅", "乙巳", "丙午", "丁巳", "戊午", "戊申", "辛亥", "壬子"};


}
