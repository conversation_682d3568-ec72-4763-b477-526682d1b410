package com.shandai.xuan.controller;

import com.shandai.xuan.config.ModelConfig;
import com.shandai.xuan.service.ModelUsageMonitorService;
import com.shandai.xuan.service.ModelSwitchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 模型管理控制器
 * 提供模型使用监控和切换的REST接口
 */
@RestController
@RequestMapping("/api/model")
public class ModelManagementController {
    
    @Autowired
    private ModelUsageMonitorService usageMonitorService;
    
    @Autowired
    private ModelSwitchService modelSwitchService;
    
    @Autowired
    private ModelConfig modelConfig;
    
    /**
     * 获取当前模型状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getCurrentStatus() {
        Map<String, Object> response = new HashMap<>();
        
        String currentModel = usageMonitorService.getCurrentActiveModel();
        ModelUsageMonitorService.UsageStats stats = usageMonitorService.getUsageStats(currentModel);
        
        response.put("currentModel", currentModel);
        response.put("switchingInProgress", modelSwitchService.isSwitchingInProgress());
        
        if (stats != null) {
            Map<String, Object> usage = new HashMap<>();
            usage.put("rpmUsed", stats.getCurrentMinuteRequests().get());
            usage.put("rpmLimit", stats.getRpmLimit());
            usage.put("rpmUsagePercentage", stats.getRpmUsagePercentage());
            usage.put("dailyUsed", stats.getCurrentDayRequests().get());
            usage.put("dailyLimit", stats.getDailyLimit());
            usage.put("dailyUsagePercentage", stats.getDailyUsagePercentage());
            usage.put("canSendRequest", stats.canSendRequest());
            response.put("usage", usage);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取所有模型的使用统计
     */
    @GetMapping("/usage/all")
    public ResponseEntity<Map<String, Object>> getAllUsageStats() {
        Map<String, Object> response = new HashMap<>();
        Map<String, ModelUsageMonitorService.UsageStats> allStats = usageMonitorService.getAllUsageStats();
        
        Map<String, Map<String, Object>> formattedStats = new HashMap<>();
        for (Map.Entry<String, ModelUsageMonitorService.UsageStats> entry : allStats.entrySet()) {
            ModelUsageMonitorService.UsageStats stats = entry.getValue();
            Map<String, Object> statMap = new HashMap<>();
            statMap.put("modelName", stats.getModelName());
            statMap.put("rpmUsed", stats.getCurrentMinuteRequests().get());
            statMap.put("rpmLimit", stats.getRpmLimit());
            statMap.put("rpmUsagePercentage", stats.getRpmUsagePercentage());
            statMap.put("dailyUsed", stats.getCurrentDayRequests().get());
            statMap.put("dailyLimit", stats.getDailyLimit());
            statMap.put("dailyUsagePercentage", stats.getDailyUsagePercentage());
            statMap.put("canSendRequest", stats.canSendRequest());
            statMap.put("lastRequestTime", stats.getLastRequestTime().get());
            statMap.put("lastRequestDate", stats.getLastRequestDate());
            
            formattedStats.put(entry.getKey(), statMap);
        }
        
        response.put("usageStats", formattedStats);
        response.put("currentActiveModel", usageMonitorService.getCurrentActiveModel());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取使用情况摘要（文本格式）
     */
    @GetMapping("/usage/summary")
    public ResponseEntity<String> getUsageSummary() {
        String summary = usageMonitorService.getUsageSummary();
        return ResponseEntity.ok(summary);
    }
    
    /**
     * 手动切换到指定模型
     */
    @PostMapping("/switch/{modelName}")
    public ResponseEntity<Map<String, Object>> switchToModel(@PathVariable String modelName) {
        Map<String, Object> response = new HashMap<>();
        
        // 检查模型是否存在
        ModelConfig.ModelInfo modelInfo = modelConfig.getModelInfo(modelName);
        if (modelInfo == null) {
            response.put("success", false);
            response.put("message", "未知模型: " + modelName);
            return ResponseEntity.badRequest().body(response);
        }
        
        // 尝试切换
        boolean success = modelSwitchService.manualSwitchToModel(modelName);
        response.put("success", success);
        
        if (success) {
            response.put("message", "成功切换到模型: " + modelName);
            response.put("newModel", modelName);
            response.put("modelInfo", Map.of(
                "rpmLimit", modelInfo.getRpmLimit(),
                "dailyLimit", modelInfo.getDailyLimit(),
                "description", modelInfo.getDescription()
            ));
        } else {
            response.put("message", "切换失败，可能正在进行其他切换操作");
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取下一个推荐的模型
     */
    @GetMapping("/next-recommended")
    public ResponseEntity<Map<String, Object>> getNextRecommendedModel() {
        Map<String, Object> response = new HashMap<>();
        
        String nextModel = modelSwitchService.getNextRecommendedModel();
        response.put("currentModel", usageMonitorService.getCurrentActiveModel());
        response.put("nextRecommendedModel", nextModel);
        
        if (nextModel != null) {
            ModelConfig.ModelInfo modelInfo = modelConfig.getModelInfo(nextModel);
            if (modelInfo != null) {
                response.put("nextModelInfo", Map.of(
                    "rpmLimit", modelInfo.getRpmLimit(),
                    "dailyLimit", modelInfo.getDailyLimit(),
                    "description", modelInfo.getDescription()
                ));
            }
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取所有可用模型列表
     */
    @GetMapping("/available")
    public ResponseEntity<Map<String, Object>> getAvailableModels() {
        Map<String, Object> response = new HashMap<>();
        
        List<ModelConfig.ModelInfo> primaryModels = modelConfig.getPrimaryModels();
        List<ModelConfig.ModelInfo> allModels = modelConfig.getAllModels();
        
        response.put("primaryModels", primaryModels);
        response.put("allModels", allModels);
        response.put("currentModel", usageMonitorService.getCurrentActiveModel());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 重置所有使用统计
     */
    @PostMapping("/reset-stats")
    public ResponseEntity<Map<String, Object>> resetUsageStats() {
        Map<String, Object> response = new HashMap<>();
        
        modelSwitchService.resetAllUsageStats();
        response.put("success", true);
        response.put("message", "所有模型的使用统计已重置");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取当前模型状态的简要信息
     */
    @GetMapping("/status/brief")
    public ResponseEntity<String> getBriefStatus() {
        String status = modelSwitchService.getCurrentModelStatus();
        return ResponseEntity.ok(status);
    }
    
    /**
     * 测试模型切换功能
     */
    @PostMapping("/test-switch")
    public ResponseEntity<Map<String, Object>> testModelSwitch() {
        Map<String, Object> response = new HashMap<>();
        
        String currentModel = usageMonitorService.getCurrentActiveModel();
        String nextModel = modelSwitchService.getNextRecommendedModel();
        
        response.put("currentModel", currentModel);
        response.put("nextRecommendedModel", nextModel);
        response.put("canSwitch", nextModel != null);
        response.put("switchingInProgress", modelSwitchService.isSwitchingInProgress());
        
        if (nextModel != null) {
            response.put("message", "可以从 " + currentModel + " 切换到 " + nextModel);
        } else {
            response.put("message", "当前没有可用的备用模型");
        }
        
        return ResponseEntity.ok(response);
    }
}
